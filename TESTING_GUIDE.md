# JDCAIChat 测试指南

## 🧪 测试概述

本指南将帮助您全面测试JDCAIChat应用的各项功能，确保所有核心特性正常工作。

## 📋 测试前准备

### 1. 启动应用
```bash
# 启动React开发服务器
npm start

# 在另一个终端启动Electron
npx electron .
```

### 2. 准备测试数据
- 准备一个可用的OpenAI兼容API（如本地Ollama、OpenAI API等）
- 确保API服务正在运行
- 准备API密钥（如果需要）

## 🔧 功能测试清单

### 1. 模型配置管理测试

#### 1.1 添加配置组
- [ ] 点击"模型配置"页面
- [ ] 点击"添加配置组"按钮
- [ ] 填写配置信息：
  - 配置组名称：`测试配置组`
  - Base URL：`http://localhost:11434/v1`（或您的API地址）
  - API Key：`test-key`（或实际密钥）
- [ ] 点击"添加"按钮
- [ ] 验证配置组出现在列表中

#### 1.2 添加模型
- [ ] 展开刚创建的配置组
- [ ] 点击"添加模型"按钮
- [ ] 填写模型信息：
  - 模型名称：`llama3.1`（或您的模型名）
  - 支持Thinking：勾选
  - 支持Vision：根据模型能力选择
- [ ] 点击"添加"按钮
- [ ] 验证模型出现在配置组下

#### 1.3 测试模型连接
- [ ] 点击模型行的"测试连接"按钮
- [ ] 观察连接结果：
  - ✅ 成功：显示"连接成功"消息
  - ❌ 失败：显示具体错误信息

### 2. 搭档角色管理测试

#### 2.1 添加搭档角色
- [ ] 点击"搭档设置"页面
- [ ] 点击"添加搭档"按钮
- [ ] 填写搭档信息：
  - 搭档名称：`测试助手`
  - 角色提示词：`你是一个友好的测试助手，请简洁地回答问题。`
- [ ] 点击"添加"按钮
- [ ] 验证搭档出现在列表中

#### 2.2 使用预设模板
- [ ] 点击"添加搭档"按钮
- [ ] 点击任一预设模板按钮（如"代码专家"）
- [ ] 验证表单自动填充
- [ ] 修改名称为`代码专家-测试`
- [ ] 保存搭档

### 3. 聊天室功能测试

#### 3.1 创建聊天室会话
- [ ] 点击"模型聊天室"页面
- [ ] 在左侧边栏邀请刚配置的模型
- [ ] 选择搭档角色（可选）
- [ ] 点击"新建会话"按钮
- [ ] 验证会话创建成功，显示系统消息

#### 3.2 发送消息测试
- [ ] 在输入框输入测试消息：`你好，请简单介绍一下自己`
- [ ] 点击"发送"按钮或按Enter键
- [ ] **观察控制台日志**：
  ```
  🚀 [ChatRoom] 开始发送消息
  📝 [ChatRoom] 创建用户消息: 你好，请简单介绍一下自己
  🤖 [ChatRoom] 开始AI协作，邀请的模型数量: 1
  🎯 [ChatRoom] 选择下一个发言的模型...
  ✅ [ChatRoom] 随机选择的模型: llama3.1
  📡 [ChatRoom] sendModelRequest 开始
  🌐 [APIManager] sendChatRequest 开始
  📡 [APIManager] 发送HTTP请求到: http://localhost:11434/v1/chat/completions
  ✅ [APIManager] HTTP请求成功
  ✅ [ChatRoom] 模型请求成功，返回结果
  💾 [ChatRoom] 模型消息已保存
  ✅ [ChatRoom] AI协作完成
  ```
- [ ] 验证AI回复出现在聊天界面中
- [ ] 验证消息格式正确（用户消息在右侧，AI消息在左侧）

#### 3.3 多轮对话测试
- [ ] 继续发送几条消息
- [ ] 验证对话上下文保持连贯
- [ ] 观察回合数计数器变化

#### 3.4 防死循环保护测试
- [ ] 打开"高级设置"
- [ ] 将"最大连续回合数"设置为3
- [ ] 邀请多个模型（如果有）
- [ ] 发送消息触发多轮AI对话
- [ ] 验证达到3轮后自动停止
- [ ] 验证显示保护提示消息

### 4. 多模型回答测试

#### 4.1 基础对比测试
- [ ] 点击"多模型回答"页面
- [ ] 选择多个模型（如果有）
- [ ] 输入测试问题：`请用一句话解释什么是人工智能`
- [ ] 点击"发送给所有模型"
- [ ] 验证所有模型并行处理
- [ ] 观察执行统计信息更新
- [ ] 验证结果以标签页形式展示

#### 4.2 导出功能测试
- [ ] 在有结果的情况下点击"导出结果"
- [ ] 验证下载JSON文件
- [ ] 检查文件内容包含问题、回答、统计信息

## 🐛 错误处理测试

### 1. 网络错误测试
- [ ] 停止API服务
- [ ] 尝试发送消息
- [ ] 验证显示网络错误提示
- [ ] 验证控制台显示详细错误信息

### 2. 无效配置测试
- [ ] 添加错误的Base URL
- [ ] 测试连接
- [ ] 验证显示连接失败信息

### 3. 空消息测试
- [ ] 尝试发送空消息
- [ ] 验证不允许发送

## 📊 性能测试

### 1. 内存使用测试
- [ ] 打开浏览器开发者工具
- [ ] 进行多次对话
- [ ] 观察内存使用情况
- [ ] 验证没有明显内存泄漏

### 2. 响应时间测试
- [ ] 观察API调用响应时间
- [ ] 验证用户操作响应及时（<200ms）
- [ ] 检查控制台性能日志

## 🔍 调试信息检查

### 控制台日志检查
在测试过程中，请注意观察浏览器控制台的日志输出：

1. **正常流程日志**：
   - `🚀 [ChatRoom] 开始发送消息`
   - `🤖 [ChatRoom] 开始AI协作`
   - `🌐 [APIManager] sendChatRequest 开始`
   - `✅ [APIManager] HTTP请求成功`
   - `✅ [ChatRoom] AI协作完成`

2. **错误日志**：
   - `❌ [ChatRoom] 模型请求失败`
   - `❌ [APIManager] 请求失败`
   - 具体错误详情

3. **调度日志**：
   - `🎯 [ChatRoom] 选择下一个发言的模型`
   - `✅ [ChatRoom] 随机选择的模型`

## ✅ 测试完成检查

### 核心功能验证
- [ ] 模型配置和连接测试正常
- [ ] 搭档角色创建和应用正常
- [ ] 聊天室消息发送和接收正常
- [ ] 多模型并行回答正常
- [ ] 防死循环保护机制有效
- [ ] 错误处理和提示友好
- [ ] 性能表现良好

### 用户体验验证
- [ ] 界面响应及时
- [ ] 操作流程直观
- [ ] 错误提示清晰
- [ ] 数据持久化正常

## 🚨 常见问题排查

### 1. AI不回复
**检查项目**：
- API服务是否运行
- 模型名称是否正确
- API密钥是否有效
- 控制台是否有错误日志

### 2. 连接测试失败
**检查项目**：
- Base URL格式是否正确
- 网络连接是否正常
- API服务是否支持OpenAI格式

### 3. 应用崩溃
**检查项目**：
- 浏览器控制台错误信息
- Electron进程是否正常
- React开发服务器是否运行

## 📝 测试报告

测试完成后，请记录：
- [ ] 测试环境信息
- [ ] 通过的测试项目
- [ ] 发现的问题和错误
- [ ] 性能表现评估
- [ ] 改进建议

---

**注意**：如果在测试过程中发现任何问题，请查看控制台日志获取详细信息，这将有助于快速定位和解决问题。
