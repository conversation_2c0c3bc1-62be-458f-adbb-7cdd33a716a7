{"name": "jdcaichat", "version": "1.0.0", "description": "一个本地化桌面AI模型协作工具", "main": "public/electron.js", "homepage": "./", "private": true, "dependencies": {"antd": "^5.8.4", "axios": "^1.4.0", "clipboardy": "^3.0.0", "cosine-similarity": "^1.0.1", "electron-is-dev": "^2.0.0", "electron-store": "^8.1.0", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "mammoth": "^1.9.1", "mermaid": "^11.9.0", "ml-distance": "^4.0.1", "ml-matrix": "^6.12.1", "openai": "^4.0.0", "pdfjs-dist": "^5.3.93", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-scripts": "5.0.1", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tesseract.js": "^6.0.1", "uuid": "^9.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/lodash": "^4.14.195", "@types/uuid": "^9.0.2", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "electron": "^25.3.1", "electron-builder": "^24.6.3", "wait-on": "^7.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "npm run build && cross-env ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ ELECTRON_BUILDER_BINARIES_MIRROR=https://npmmirror.com/mirrors/electron-builder-binaries/ electron-builder", "electron-pack-win": "npm run build && cross-env ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ ELECTRON_BUILDER_BINARIES_MIRROR=https://npmmirror.com/mirrors/electron-builder-binaries/ electron-builder --win", "electron-pack-mac": "npm run build && cross-env ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ ELECTRON_BUILDER_BINARIES_MIRROR=https://npmmirror.com/mirrors/electron-builder-binaries/ electron-builder --mac", "electron-pack-linux": "npm run build && cross-env ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ ELECTRON_BUILDER_BINARIES_MIRROR=https://npmmirror.com/mirrors/electron-builder-binaries/ electron-builder --linux", "preelectron-pack": "npm run build"}, "build": {"appId": "com.jdc.aichat", "productName": "JDCAIChat", "copyright": "Copyright © 2024 JDC", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "public/preload.js", "public/AiChatLogo.png", "node_modules/**/*"], "extraResources": [{"from": "public/AiChatLogo.png", "to": "AiChatLogo.png"}], "mac": {"category": "public.app-category.productivity", "icon": "public/AiChatLogo.png", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": "nsis", "icon": "public/AiChatLogo.png"}, "linux": {"target": "AppImage", "icon": "public/AiChatLogo.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}