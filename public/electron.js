const { app, BrowserWindow, ipcMain, dialog, clipboard, Tray, Menu, globalShortcut } = require('electron');
const path = require('path');
const Store = require('electron-store');
const https = require('https');
const http = require('http');
const { URL } = require('url');

// � 导入优化服务（可选加载，避免依赖问题）
let MainProcessService = null;
try {
  MainProcessService = require('../main/services/mainProcessService');
} catch (error) {
  console.warn('⚠️ [Electron] 优化服务未找到，使用基础模式:', error.message);
}

// �🔧 更可靠的开发环境检测
const isDev = process.env.NODE_ENV === 'development' ||
              process.env.ELECTRON_IS_DEV === '1' ||
              !app.isPackaged;

// 初始化存储
const store = new Store();

let mainWindow;
let tray = null; // 🎯 系统托盘实例

// 🚀 优化服务实例
let mainProcessService = null;

function createWindow() {
  // 🎨 获取应用图标路径
  let iconPath;
  if (isDev) {
    iconPath = path.join(__dirname, 'AiChatLogo.png');
  } else {
    // 打包后的图标路径
    iconPath = path.join(process.resourcesPath, 'AiChatLogo.png');
  }

  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    icon: iconPath, // 🎨 动态设置应用图标
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: 'default',
    show: false,
    title: 'JDCAIChat - AI协作平台' // 🎨 设置窗口标题
  });

  // 加载应用
  const startUrl = isDev 
    ? 'http://localhost:3000' 
    : `file://${path.join(__dirname, '../build/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // 窗口准备好后显示（由托盘控制）
  mainWindow.once('ready-to-show', () => {
    // 创建系统托盘后再显示
    createTray();
    if (!tray) {
      // 如果托盘创建失败，直接显示窗口
      mainWindow.show();
    }
  });

  // 窗口关闭时的处理（最小化到托盘）
  mainWindow.on('close', (event) => {
    if (tray && !app.isQuitting) {
      event.preventDefault();
      mainWindow.hide();
      return false;
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 开发环境下打开开发者工具
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }
}

// 🎯 创建系统托盘
function createTray() {
  // 获取托盘图标路径
  let trayIconPath;
  if (isDev) {
    trayIconPath = path.join(__dirname, 'AiChatLogo.png');
  } else {
    trayIconPath = path.join(process.resourcesPath, 'AiChatLogo.png');
  }

  try {
    tray = new Tray(trayIconPath);
    
    // 创建托盘菜单
    const contextMenu = Menu.buildFromTemplate([
      {
        label: '显示窗口',
        click: () => {
          if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.show();
            mainWindow.focus();
          }
        }
      },
      {
        label: '隐藏窗口',
        click: () => {
          if (mainWindow) {
            mainWindow.hide();
          }
        }
      },
      { type: 'separator' },
      {
        label: '快捷键帮助',
        accelerator: 'CmdOrCtrl+O',
        click: () => {
          if (mainWindow) {
            mainWindow.webContents.send('show-shortcuts-help');
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.show();
            mainWindow.focus();
          }
        }
      },
      {
        label: '新建会话',
        accelerator: 'CmdOrCtrl+N',
        click: () => {
          if (mainWindow) {
            mainWindow.webContents.send('new-session');
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.show();
            mainWindow.focus();
          }
        }
      },
      {
        label: '切换剪贴板',
        accelerator: 'CmdOrCtrl+K',
        click: () => {
          if (mainWindow) {
            mainWindow.webContents.send('toggle-clipboard');
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.show();
            mainWindow.focus();
          }
        }
      },
      { type: 'separator' },
      {
        label: '重启应用',
        click: () => {
          app.relaunch();
          app.exit();
        }
      },
      {
        label: '退出应用',
        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
        click: () => {
          app.isQuitting = true;
          app.quit();
        }
      }
    ]);

    tray.setContextMenu(contextMenu);
    tray.setToolTip('JDCAIChat - AI协作平台');

    // 托盘图标点击事件
    tray.on('click', () => {
      if (mainWindow) {
        if (mainWindow.isVisible()) {
          mainWindow.hide();
        } else {
          if (mainWindow.isMinimized()) mainWindow.restore();
          mainWindow.show();
          mainWindow.focus();
        }
      }
    });

    // 托盘双击事件
    tray.on('double-click', () => {
      if (mainWindow) {
        if (mainWindow.isMinimized()) mainWindow.restore();
        mainWindow.show();
        mainWindow.focus();
      }
    });

    console.log('🎯 [Electron] 系统托盘已创建');
    
    // 延迟显示窗口（给用户时间看到托盘）
    setTimeout(() => {
      if (mainWindow && !mainWindow.isVisible()) {
        mainWindow.show();
      }
    }, 1000);

  } catch (error) {
    console.error('❌ [Electron] 创建系统托盘失败:', error);
    tray = null;
  }
}

// 🚀 初始化优化服务（可选）
async function initializeOptimizedServices() {
  if (!MainProcessService) {
    console.log('📝 [Electron] 使用基础模式（无优化服务）');
    return;
  }

  try {
    console.log('🚀 [Electron] 初始化优化服务...');

    const os = require('os');
    mainProcessService = new MainProcessService({
      // 向量搜索配置
      vectorSearch: {
        maxWorkers: Math.max(2, os.cpus().length - 1),
      },

      // 缓存配置
      cache: {
        maxMemoryItems: 1000,
        maxMemorySize: 100 * 1024 * 1024, // 100MB
        cacheDir: path.join(app.getPath('userData'), 'cache'),
        ttl: 24 * 60 * 60 * 1000, // 24小时
      },

      // 文档处理配置
      documentProcessor: {
        tempDir: path.join(app.getPath('userData'), 'temp'),
        maxFileSize: 50 * 1024 * 1024, // 50MB
        ocrLanguages: ['chi_sim', 'eng'],
      },

      // 版本管理配置
      versionManager: {
        versionsDir: path.join(app.getPath('userData'), 'versions'),
        maxVersions: 50,
        autoSave: true,
      },

      // 知识图谱配置
      knowledgeGraph: {
        graphDir: path.join(app.getPath('userData'), 'knowledge-graph'),
        maxRelations: 10000,
        autoSave: true,
      }
    });

    await mainProcessService.initialize();
    console.log('✅ [Electron] 优化服务初始化完成');

  } catch (error) {
    console.error('❌ [Electron] 优化服务初始化失败:', error);
    console.log('📝 [Electron] 继续使用基础模式');
    mainProcessService = null;
  }
}

// 🎯 注册全局快捷键
function registerGlobalShortcuts() {
  try {
    // 快捷键帮助
    globalShortcut.register('CmdOrCtrl+O', () => {
      if (mainWindow) {
        mainWindow.webContents.send('show-shortcuts-help');
        if (mainWindow.isMinimized()) mainWindow.restore();
        mainWindow.show();
        mainWindow.focus();
      }
    });

    // 显示/隐藏窗口
    globalShortcut.register('CmdOrCtrl+Shift+A', () => {
      if (mainWindow) {
        if (mainWindow.isVisible()) {
          mainWindow.hide();
        } else {
          if (mainWindow.isMinimized()) mainWindow.restore();
          mainWindow.show();
          mainWindow.focus();
        }
      }
    });

    console.log('🎯 [Electron] 全局快捷键已注册');
  } catch (error) {
    console.error('❌ [Electron] 注册全局快捷键失败:', error);
  }
}

// 应用准备就绪时创建窗口
app.whenReady().then(async () => {
  await initializeOptimizedServices();
  createWindow();
  registerGlobalShortcuts();
});

// 所有窗口关闭时退出应用（macOS除外）
app.on('window-all-closed', () => {
  // 如果有托盘，不退出应用
  if (process.platform !== 'darwin' && !tray) {
    app.quit();
  }
});

// 🚀 应用退出前清理优化服务
app.on('before-quit', async () => {
  if (mainProcessService) {
    console.log('🧹 [Electron] 清理优化服务...');
    try {
      await mainProcessService.cleanup();
      console.log('✅ [Electron] 优化服务清理完成');
    } catch (error) {
      console.error('❌ [Electron] 优化服务清理失败:', error);
    }
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  } else if (mainWindow) {
    mainWindow.show();
  }
});

// 应用退出前清理
app.on('before-quit', () => {
  app.isQuitting = true;
  
  // 注销全局快捷键
  globalShortcut.unregisterAll();
  
  // 销毁托盘
  if (tray) {
    tray.destroy();
    tray = null;
  }
});

// IPC 处理程序
ipcMain.handle('get-app-path', () => {
  return app.getPath('userData');
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

// 存储相关的IPC处理
ipcMain.handle('store-get', (event, key) => {
  return store.get(key);
});

ipcMain.handle('store-set', (event, key, value) => {
  store.set(key, value);
});

ipcMain.handle('store-delete', (event, key) => {
  store.delete(key);
});

ipcMain.handle('store-clear', () => {
  store.clear();
});

// 🌐 网络搜索相关的IPC处理
ipcMain.handle('web-search', async (event, searchUrl, options = {}) => {
  return new Promise((resolve, reject) => {
    const maxRedirects = options.maxRedirects || 5;
    let redirectCount = 0;

    const makeRequest = (currentUrl) => {
      try {
        const url = new URL(currentUrl);
        const protocol = url.protocol === 'https:' ? https : http;

        const requestOptions = {
          hostname: url.hostname,
          port: url.port || (url.protocol === 'https:' ? 443 : 80),
          path: url.pathname + url.search,
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Microsoft Edge";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            ...options.headers
          },
          timeout: options.timeout || 15000
        };

        console.log(`🌐 [Electron] 请求URL: ${currentUrl} (重定向次数: ${redirectCount})`);

        const req = protocol.request(requestOptions, (res) => {
          console.log(`🌐 [Electron] 响应状态: ${res.statusCode}, Headers:`, Object.keys(res.headers));

          // 处理重定向
          if (res.statusCode >= 300 && res.statusCode < 400 && res.headers.location) {
            if (redirectCount >= maxRedirects) {
              reject(new Error(`Too many redirects (${redirectCount})`));
              return;
            }

            redirectCount++;
            const redirectUrl = new URL(res.headers.location, currentUrl).toString();
            console.log(`🔄 [Electron] HTTP重定向 ${redirectCount}/${maxRedirects}: ${redirectUrl}`);

            // 递归处理重定向
            setTimeout(() => makeRequest(redirectUrl), 1000); // 增加延迟避免过快请求
            return;
          }

          // 处理非200状态码
          if (res.statusCode !== 200) {
            reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage || '请求失败'}`));
            return;
          }

          let data = '';

          // 处理压缩
          let stream = res;
          if (res.headers['content-encoding'] === 'gzip') {
            const zlib = require('zlib');
            stream = res.pipe(zlib.createGunzip());
          } else if (res.headers['content-encoding'] === 'deflate') {
            const zlib = require('zlib');
            stream = res.pipe(zlib.createInflate());
          } else if (res.headers['content-encoding'] === 'br') {
            const zlib = require('zlib');
            stream = res.pipe(zlib.createBrotliDecompress());
          }

          stream.on('data', (chunk) => {
            data += chunk;
          });

          stream.on('end', () => {
            console.log(`✅ [Electron] 请求成功，数据长度: ${data.length}`);
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              data: data,
              finalUrl: currentUrl,
              redirectCount: redirectCount
            });
          });

          stream.on('error', (error) => {
            reject(new Error(`Stream error: ${error.message}`));
          });
        });

        req.on('error', (error) => {
          reject(new Error(`Request error: ${error.message}`));
        });

        req.on('timeout', () => {
          req.destroy();
          reject(new Error('Request timeout'));
        });

        req.end();
      } catch (error) {
        reject(new Error(`URL parsing error: ${error.message}`));
      }
    };

    // 开始请求
    makeRequest(searchUrl);
  });
});

// 🌐 网页内容提取的IPC处理
ipcMain.handle('web-extract', async (event, url, options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      const urlObj = new URL(url);
      const protocol = urlObj.protocol === 'https:' ? https : http;

      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        timeout: options.timeout || 8000
      };

      const req = protocol.request(requestOptions, (res) => {
        // 检查内容类型
        const contentType = res.headers['content-type'] || '';
        if (!contentType.includes('text/html')) {
          reject(new Error('不支持的内容类型: ' + contentType));
          return;
        }

        let data = '';

        // 处理压缩
        let stream = res;
        if (res.headers['content-encoding'] === 'gzip') {
          const zlib = require('zlib');
          stream = res.pipe(zlib.createGunzip());
        } else if (res.headers['content-encoding'] === 'deflate') {
          const zlib = require('zlib');
          stream = res.pipe(zlib.createInflate());
        }

        stream.on('data', (chunk) => {
          data += chunk;
        });

        stream.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data,
            url: url
          });
        });

        stream.on('error', (error) => {
          reject(new Error(`Stream error: ${error.message}`));
        });
      });

      req.on('error', (error) => {
        reject(new Error(`Request error: ${error.message}`));
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.end();
    } catch (error) {
      reject(new Error(`URL parsing error: ${error.message}`));
    }
  });
});

// 🚀 系统剪贴板管理器
class SystemClipboardManager {
  constructor() {
    this.isMonitoring = false;
    this.clipboardHistory = [];
    this.maxHistorySize = 100;
    this.monitorInterval = null;
    this.lastClipboardContent = '';
    this.lastClipboardImage = null;
  }

  startMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    console.log('🎯 [剪贴板管理器] 开始监听系统剪贴板');

    // 每500ms检查一次剪贴板内容
    this.monitorInterval = setInterval(() => {
      this.checkClipboardChange();
    }, 500);

    // 初始化时读取一次当前剪贴板内容
    this.checkClipboardChange();
  }

  stopMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    console.log('⏹️ [剪贴板管理器] 停止监听系统剪贴板');
  }

  checkClipboardChange() {
    try {
      // 检查文本内容
      const text = clipboard.readText();
      const hasImage = clipboard.availableFormats().includes('image/png');

      let contentChanged = false;
      let newItem = null;

      // 检查文本变化
      if (text && text !== this.lastClipboardContent) {
        this.lastClipboardContent = text;
        newItem = this.createClipboardItem(text, 'text');
        contentChanged = true;
      }

      // 检查图片变化
      if (hasImage) {
        const image = clipboard.readImage();
        const imageBuffer = image.toPNG();
        const imageHash = require('crypto').createHash('md5').update(imageBuffer).digest('hex');

        if (imageHash !== this.lastClipboardImage) {
          this.lastClipboardImage = imageHash;
          const imageDataUrl = `data:image/png;base64,${imageBuffer.toString('base64')}`;
          newItem = this.createClipboardItem(imageDataUrl, 'image');
          contentChanged = true;
        }
      }

      if (contentChanged && newItem) {
        this.addToHistory(newItem);

        // 通知渲染进程
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('clipboard-content-added', newItem);
        }
      }

    } catch (error) {
      console.warn('⚠️ [剪贴板管理器] 读取剪贴板失败:', error.message);
    }
  }

  createClipboardItem(content, type) {
    const item = {
      id: `clip_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content: content,
      timestamp: new Date().toISOString(),
      type: type,
      category: type === 'image' ? 'image' : this.detectContentCategory(content),
      preview: type === 'image' ? '[图片]' : this.generatePreview(content),
      size: content.length,
      source: 'system'
    };

    return item;
  }

  detectContentCategory(content) {
    const trimmed = content.trim();

    // URL检测
    if (/^https?:\/\/[^\s]+$/i.test(trimmed)) {
      return 'url';
    }

    // 邮箱检测
    if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(trimmed)) {
      return 'email';
    }

    // JSON检测
    if ((trimmed.startsWith('{') && trimmed.endsWith('}')) ||
        (trimmed.startsWith('[') && trimmed.endsWith(']'))) {
      try {
        JSON.parse(trimmed);
        return 'json';
      } catch (e) {
        // 不是有效JSON，继续其他检测
      }
    }

    // 代码检测
    const codePatterns = [
      /\b(function|const|let|var|class|import|export|return|if|else|for|while)\b/,
      /\b(def|class|import|from|return|if|elif|else|for|while|try|except)\b/,
      /\b(public|private|static|void|int|string|boolean|class|interface)\b/,
      /[{}();].*[{}();]/,
      /^\s*[<>].*[<>]\s*$/m,
      /^\s*#.*|\/\/.*|\/\*.*\*\//m
    ];

    if (codePatterns.some(pattern => pattern.test(trimmed))) {
      return 'code';
    }

    return 'text';
  }

  generatePreview(content, maxLength = 100) {
    const trimmed = content.trim();
    if (trimmed.length <= maxLength) {
      return trimmed;
    }

    return trimmed.substring(0, maxLength) + '...';
  }

  addToHistory(item) {
    // 检查是否已存在相同内容
    const existingIndex = this.clipboardHistory.findIndex(
      existing => existing.content === item.content
    );

    if (existingIndex !== -1) {
      // 如果存在，更新时间戳并移到最前面
      this.clipboardHistory.splice(existingIndex, 1);
    }

    // 添加到开头
    this.clipboardHistory.unshift(item);

    // 限制历史记录大小
    if (this.clipboardHistory.length > this.maxHistorySize) {
      this.clipboardHistory = this.clipboardHistory.slice(0, this.maxHistorySize);
    }

    console.log(`📋 [剪贴板管理器] 新增${item.category}内容:`, item.preview);
  }

  getHistory() {
    return this.clipboardHistory;
  }

  clearHistory() {
    this.clipboardHistory = [];
    console.log('🧹 [剪贴板管理器] 历史记录已清空');
  }

  deleteItem(itemId) {
    const index = this.clipboardHistory.findIndex(item => item.id === itemId);
    if (index !== -1) {
      this.clipboardHistory.splice(index, 1);
      console.log(`🗑️ [剪贴板管理器] 删除项目: ${itemId}`);
      return true;
    }
    return false;
  }

  copyToClipboard(content, type = 'text') {
    try {
      if (type === 'image') {
        // 处理图片数据
        const base64Data = content.replace(/^data:image\/[a-z]+;base64,/, '');
        const buffer = Buffer.from(base64Data, 'base64');
        const image = require('electron').nativeImage.createFromBuffer(buffer);
        clipboard.writeImage(image);
      } else {
        clipboard.writeText(content);
      }
      return true;
    } catch (error) {
      console.error('❌ [剪贴板管理器] 复制失败:', error);
      return false;
    }
  }
}

// 创建剪贴板管理器实例
const clipboardManager = new SystemClipboardManager();

// 🚀 自动启动剪贴板监听（当主窗口存在时）
// 🎯 修复：延迟启动，等待渲染进程准备好
if (mainWindow && !mainWindow.isDestroyed()) {
  mainWindow.webContents.once('did-finish-load', () => {
    setTimeout(() => {
      try {
        clipboardManager.startMonitoring();
        console.log('🎯 [Electron] 剪贴板监听已自动启动');
        
        // 通知渲染进程剪贴板监听已启动
        mainWindow.webContents.send('clipboard-monitoring-started');
      } catch (error) {
        console.error('❌ [Electron] 启动剪贴板监听失败:', error);
      }
    }, 2000); // 延迟2秒启动，确保渲染进程完全准备好
  });
}

// 🚀 剪贴板管理器 IPC 处理程序
ipcMain.handle('clipboard-start-monitoring', () => {
  clipboardManager.startMonitoring();
  return { success: true };
});

ipcMain.handle('clipboard-stop-monitoring', () => {
  clipboardManager.stopMonitoring();
  return { success: true };
});

ipcMain.handle('clipboard-get-history', () => {
  return clipboardManager.getHistory();
});

ipcMain.handle('clipboard-clear-history', () => {
  clipboardManager.clearHistory();
  return { success: true };
});

ipcMain.handle('clipboard-delete-item', (event, itemId) => {
  const success = clipboardManager.deleteItem(itemId);
  return { success };
});

ipcMain.handle('clipboard-copy-content', (event, content, type) => {
  const success = clipboardManager.copyToClipboard(content, type);
  return { success };
});

ipcMain.handle('clipboard-get-current', () => {
  try {
    const text = clipboard.readText();
    const hasImage = clipboard.availableFormats().includes('image/png');

    let result = { text: text || '' };

    if (hasImage) {
      const image = clipboard.readImage();
      const imageBuffer = image.toPNG();
      result.image = `data:image/png;base64,${imageBuffer.toString('base64')}`;
    }

    return result;
  } catch (error) {
    console.error('❌ [剪贴板] 读取当前内容失败:', error);
    return { text: '', error: error.message };
  }
});

// 🎯 应用控制IPC处理器
ipcMain.handle('app-quit', () => {
  app.isQuitting = true;
  app.quit();
});

ipcMain.handle('app-minimize', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('app-hide', () => {
  if (mainWindow) {
    mainWindow.hide();
  }
});

ipcMain.handle('app-show', () => {
  if (mainWindow) {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.show();
    mainWindow.focus();
  }
});

// 🎯 窗口控制IPC处理器
ipcMain.handle('window-toggle-fullscreen', () => {
  if (mainWindow) {
    mainWindow.setFullScreen(!mainWindow.isFullScreen());
    return mainWindow.isFullScreen();
  }
  return false;
});

ipcMain.handle('window-toggle-devtools', () => {
  if (mainWindow) {
    if (mainWindow.webContents.isDevToolsOpened()) {
      mainWindow.webContents.closeDevTools();
    } else {
      mainWindow.webContents.openDevTools();
    }
  }
});

ipcMain.handle('window-is-fullscreen', () => {
  return mainWindow ? mainWindow.isFullScreen() : false;
});
