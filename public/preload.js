const { contextBridge, ipc<PERSON>enderer } = require('electron');

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 应用路径
  getAppPath: () => ipcRenderer.invoke('get-app-path'),
  
  // 对话框
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  
  // 存储
  store: {
    get: (key) => ipcRenderer.invoke('store-get', key),
    set: (key, value) => ipcRenderer.invoke('store-set', key, value),
    delete: (key) => ipcRenderer.invoke('store-delete', key),
    clear: () => ipcRenderer.invoke('store-clear')
  },

  // 🌐 网络搜索
  webSearch: {
    search: (searchUrl, options) => ipcRenderer.invoke('web-search', searchUrl, options),
    extract: (url, options) => ipcRenderer.invoke('web-extract', url, options)
  },

  // 🚀 系统剪贴板管理器
  clipboard: {
    startMonitoring: () => ipcRenderer.invoke('clipboard-start-monitoring'),
    stopMonitoring: () => ipcRenderer.invoke('clipboard-stop-monitoring'),
    getHistory: () => ipcRenderer.invoke('clipboard-get-history'),
    clearHistory: () => ipcRenderer.invoke('clipboard-clear-history'),
    deleteItem: (itemId) => ipcRenderer.invoke('clipboard-delete-item', itemId),
    copyContent: (content, type) => ipcRenderer.invoke('clipboard-copy-content', content, type),
    getCurrent: () => ipcRenderer.invoke('clipboard-get-current'),
    onContentAdded: (callback) => {
      ipcRenderer.on('clipboard-content-added', (event, item) => callback(item));
    },
    removeContentListener: () => {
      ipcRenderer.removeAllListeners('clipboard-content-added');
    },
    onMonitoringStarted: (callback) => {
      ipcRenderer.on('clipboard-monitoring-started', callback);
    },
    removeMonitoringListener: () => {
      ipcRenderer.removeAllListeners('clipboard-monitoring-started');
    }
  },

  // 🎯 应用控制API
  app: {
    quit: () => ipcRenderer.invoke('app-quit'),
    minimize: () => ipcRenderer.invoke('app-minimize'),
    hide: () => ipcRenderer.invoke('app-hide'),
    show: () => ipcRenderer.invoke('app-show')
  },

  // 🎯 窗口控制API
  window: {
    toggleFullscreen: () => ipcRenderer.invoke('window-toggle-fullscreen'),
    toggleDevtools: () => ipcRenderer.invoke('window-toggle-devtools'),
    isFullscreen: () => ipcRenderer.invoke('window-is-fullscreen')
  },

  // 🚀 优化功能API（可选，如果主进程服务可用）
  // 向量搜索
  vectorSearch: (queryVector, vectors, options) => ipcRenderer.invoke('vector-search', queryVector, vectors, options),
  vectorSearchStats: () => ipcRenderer.invoke('vector-search-stats'),

  // 缓存管理
  cache: {
    get: (key, category) => ipcRenderer.invoke('cache-get', key, category),
    set: (key, data, category, options) => ipcRenderer.invoke('cache-set', key, data, category, options),
    delete: (key, category) => ipcRenderer.invoke('cache-delete', key, category),
    getStats: () => ipcRenderer.invoke('cache-stats'),
    cleanup: () => ipcRenderer.invoke('cache-cleanup')
  },

  // 文档处理
  document: {
    process: (filePath, options) => ipcRenderer.invoke('process-document', filePath, options),
    processBatch: (filePaths, options) => ipcRenderer.invoke('process-documents-batch', filePaths, options),
    getSupportedFormats: () => ipcRenderer.invoke('get-supported-formats'),
    onProgress: (callback) => {
      ipcRenderer.on('document-processing-progress', (event, progress) => callback(progress));
    },
    removeProgressListener: () => {
      ipcRenderer.removeAllListeners('document-processing-progress');
    }
  },

  // 版本管理
  version: {
    save: (documentId, content, metadata) => ipcRenderer.invoke('version-save', documentId, content, metadata),
    getList: (documentId) => ipcRenderer.invoke('version-get-list', documentId),
    get: (documentId, versionId) => ipcRenderer.invoke('version-get', documentId, versionId),
    compare: (documentId, version1Id, version2Id) => ipcRenderer.invoke('version-compare', documentId, version1Id, version2Id),
    restore: (documentId, versionId) => ipcRenderer.invoke('version-restore', documentId, versionId),
    delete: (documentId, versionId) => ipcRenderer.invoke('version-delete', documentId, versionId),
    getStats: () => ipcRenderer.invoke('version-stats')
  },

  // 知识图谱
  knowledgeGraph: {
    addKBRelation: (sourceKbId, targetKbId, relationType, metadata) =>
      ipcRenderer.invoke('graph-add-kb-relation', sourceKbId, targetKbId, relationType, metadata),
    addDocRelation: (sourceDocId, targetDocId, relationType, context) =>
      ipcRenderer.invoke('graph-add-doc-relation', sourceDocId, targetDocId, relationType, context),
    discoverRelations: (documents, options) => ipcRenderer.invoke('graph-discover-relations', documents, options),
    findRelated: (documentId, options) => ipcRenderer.invoke('graph-find-related', documentId, options),
    getKBRelations: (knowledgeBaseId) => ipcRenderer.invoke('graph-get-kb-relations', knowledgeBaseId),
    analyze: () => ipcRenderer.invoke('graph-analyze'),
    onRelationAdded: (callback) => {
      ipcRenderer.on('graph-relation-added', (event, data) => callback(data));
    },
    onDiscoveryProgress: (callback) => {
      ipcRenderer.on('graph-discovery-progress', (event, progress) => callback(progress));
    },
    removeListeners: () => {
      ipcRenderer.removeAllListeners('graph-relation-added');
      ipcRenderer.removeAllListeners('graph-discovery-progress');
    }
  },

  // 服务管理
  service: {
    getStatus: () => ipcRenderer.invoke('service-status'),
    restart: (serviceName) => ipcRenderer.invoke('service-restart', serviceName)
  },

  // 工具函数
  utils: {
    generateUUID: () => {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },
    formatFileSize: (bytes) => {
      const units = ['B', 'KB', 'MB', 'GB', 'TB'];
      let size = bytes;
      let unitIndex = 0;

      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
      }

      return `${size.toFixed(2)} ${units[unitIndex]}`;
    }
  }
});
