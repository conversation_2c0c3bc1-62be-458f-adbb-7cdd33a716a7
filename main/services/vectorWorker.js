const { parentPort } = require('worker_threads');

/**
 * 向量计算Worker线程
 * 专门处理向量相似度计算的密集型任务
 */

/**
 * 计算余弦相似度（优化版本）
 */
function cosineSimilarity(vecA, vecB) {
  if (vecA.length !== vecB.length) {
    throw new Error('向量维度不匹配');
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  // 使用单次循环计算所有值
  for (let i = 0; i < vecA.length; i++) {
    const a = vecA[i];
    const b = vecB[i];
    
    dotProduct += a * b;
    normA += a * a;
    normB += b * b;
  }

  const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
  
  if (magnitude === 0) {
    return 0;
  }

  return dotProduct / magnitude;
}

/**
 * 批量计算向量相似度（SIMD优化思路）
 */
function batchCosineSimilarity(queryVector, vectors) {
  const results = [];
  const queryNorm = Math.sqrt(queryVector.reduce((sum, val) => sum + val * val, 0));
  
  if (queryNorm === 0) {
    return vectors.map(() => 0);
  }

  for (let i = 0; i < vectors.length; i++) {
    const vector = vectors[i].vector;
    
    if (!vector || vector.length !== queryVector.length) {
      results.push(0);
      continue;
    }

    let dotProduct = 0;
    let vectorNorm = 0;

    // 展开循环优化（处理4个元素为一组）
    let j = 0;
    const len = queryVector.length;
    const remainder = len % 4;
    const limit = len - remainder;

    // 处理4的倍数部分
    for (j = 0; j < limit; j += 4) {
      const q0 = queryVector[j], q1 = queryVector[j + 1], q2 = queryVector[j + 2], q3 = queryVector[j + 3];
      const v0 = vector[j], v1 = vector[j + 1], v2 = vector[j + 2], v3 = vector[j + 3];
      
      dotProduct += q0 * v0 + q1 * v1 + q2 * v2 + q3 * v3;
      vectorNorm += v0 * v0 + v1 * v1 + v2 * v2 + v3 * v3;
    }

    // 处理剩余元素
    for (j = limit; j < len; j++) {
      const q = queryVector[j];
      const v = vector[j];
      dotProduct += q * v;
      vectorNorm += v * v;
    }

    const magnitude = queryNorm * Math.sqrt(vectorNorm);
    results.push(magnitude === 0 ? 0 : dotProduct / magnitude);
  }

  return results;
}

/**
 * 高性能向量搜索实现
 */
function performVectorSearch(queryVector, vectors, options = {}) {
  const { topK = 5, threshold = 0.5 } = options;
  
  try {
    const startTime = Date.now();
    
    // 批量计算相似度
    const similarities = batchCosineSimilarity(queryVector, vectors);
    
    // 创建结果数组并过滤
    const results = [];
    for (let i = 0; i < vectors.length; i++) {
      const similarity = similarities[i];
      
      if (similarity >= threshold) {
        results.push({
          ...vectors[i],
          similarity,
          index: i
        });
      }
    }

    // 使用快速排序（对于大数据集更高效）
    results.sort((a, b) => b.similarity - a.similarity);
    
    // 返回top-k结果
    const topResults = results.slice(0, topK);
    
    const computeTime = Date.now() - startTime;
    
    return {
      results: topResults,
      totalProcessed: vectors.length,
      totalMatched: results.length,
      computeTime,
      avgSimilarity: results.length > 0 ? results.reduce((sum, r) => sum + r.similarity, 0) / results.length : 0
    };
    
  } catch (error) {
    throw new Error(`向量搜索计算失败: ${error.message}`);
  }
}

/**
 * 欧几里得距离计算（备选相似度算法）
 */
function euclideanDistance(vecA, vecB) {
  if (vecA.length !== vecB.length) {
    throw new Error('向量维度不匹配');
  }

  let sum = 0;
  for (let i = 0; i < vecA.length; i++) {
    const diff = vecA[i] - vecB[i];
    sum += diff * diff;
  }

  return Math.sqrt(sum);
}

/**
 * 曼哈顿距离计算
 */
function manhattanDistance(vecA, vecB) {
  if (vecA.length !== vecB.length) {
    throw new Error('向量维度不匹配');
  }

  let sum = 0;
  for (let i = 0; i < vecA.length; i++) {
    sum += Math.abs(vecA[i] - vecB[i]);
  }

  return sum;
}

/**
 * 多种相似度算法的搜索
 */
function performMultiMetricSearch(queryVector, vectors, options = {}) {
  const { 
    topK = 5, 
    threshold = 0.5, 
    metric = 'cosine' // 'cosine', 'euclidean', 'manhattan'
  } = options;

  let calculateSimilarity;
  let isDistance = false; // 是否为距离度量（越小越相似）

  switch (metric) {
    case 'euclidean':
      calculateSimilarity = euclideanDistance;
      isDistance = true;
      break;
    case 'manhattan':
      calculateSimilarity = manhattanDistance;
      isDistance = true;
      break;
    case 'cosine':
    default:
      calculateSimilarity = cosineSimilarity;
      break;
  }

  const results = [];

  for (let i = 0; i < vectors.length; i++) {
    const vector = vectors[i];
    
    if (!vector.vector || vector.vector.length !== queryVector.length) {
      continue;
    }

    const score = calculateSimilarity(queryVector, vector.vector);
    
    // 对于距离度量，需要转换为相似度
    const similarity = isDistance ? 1 / (1 + score) : score;
    
    if (similarity >= threshold) {
      results.push({
        ...vector,
        similarity,
        score,
        metric,
        index: i
      });
    }
  }

  // 排序（相似度越高越好）
  results.sort((a, b) => b.similarity - a.similarity);
  
  return {
    results: results.slice(0, topK),
    totalProcessed: vectors.length,
    totalMatched: results.length,
    metric
  };
}

/**
 * 监听主线程消息
 */
parentPort.on('message', (data) => {
  const { taskId, queryVector, vectors, options } = data;
  
  try {
    // 验证输入数据
    if (!queryVector || !Array.isArray(queryVector)) {
      throw new Error('无效的查询向量');
    }
    
    if (!vectors || !Array.isArray(vectors)) {
      throw new Error('无效的向量数据');
    }

    // 执行向量搜索
    const result = options.metric && options.metric !== 'cosine' 
      ? performMultiMetricSearch(queryVector, vectors, options)
      : performVectorSearch(queryVector, vectors, options);

    // 发送结果回主线程
    parentPort.postMessage({
      taskId,
      success: true,
      results: result.results,
      stats: {
        totalProcessed: result.totalProcessed,
        totalMatched: result.totalMatched,
        computeTime: result.computeTime,
        avgSimilarity: result.avgSimilarity,
        metric: result.metric || 'cosine'
      }
    });

  } catch (error) {
    // 发送错误信息
    parentPort.postMessage({
      taskId,
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

/**
 * 处理Worker线程错误
 */
process.on('uncaughtException', (error) => {
  console.error('Worker线程未捕获异常:', error);
  parentPort.postMessage({
    success: false,
    error: `Worker线程异常: ${error.message}`,
    fatal: true
  });
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Worker线程未处理的Promise拒绝:', reason);
  parentPort.postMessage({
    success: false,
    error: `Promise拒绝: ${reason}`,
    fatal: false
  });
});

// 发送Worker就绪信号
parentPort.postMessage({
  type: 'ready',
  workerId: process.pid,
  timestamp: Date.now()
});
