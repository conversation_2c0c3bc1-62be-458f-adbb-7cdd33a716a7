const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { EventEmitter } = require('events');

/**
 * 智能缓存管理器
 * 实现多层缓存策略：内存缓存 + 磁盘缓存 + IndexedDB
 */
class CacheManager extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      maxMemoryItems: options.maxMemoryItems || 1000,
      maxMemorySize: options.maxMemorySize || 100 * 1024 * 1024, // 100MB
      maxDiskSize: options.maxDiskSize || 1024 * 1024 * 1024, // 1GB
      cacheDir: options.cacheDir || path.join(process.cwd(), 'cache'),
      compressionLevel: options.compressionLevel || 6,
      ttl: options.ttl || 24 * 60 * 60 * 1000, // 24小时
      cleanupInterval: options.cleanupInterval || 60 * 60 * 1000 // 1小时
    };

    // 内存缓存（LRU）
    this.memoryCache = new Map();
    this.accessOrder = new Map(); // 访问顺序追踪
    this.memorySize = 0;

    // 磁盘缓存统计
    this.diskStats = {
      totalSize: 0,
      fileCount: 0,
      hitRate: 0,
      totalRequests: 0,
      hits: 0
    };

    // 初始化
    this.initialized = false;
    this.cleanupTimer = null;
  }

  /**
   * 初始化缓存管理器
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // 创建缓存目录
      await fs.mkdir(this.options.cacheDir, { recursive: true });
      
      // 创建子目录
      await fs.mkdir(path.join(this.options.cacheDir, 'vectors'), { recursive: true });
      await fs.mkdir(path.join(this.options.cacheDir, 'documents'), { recursive: true });
      await fs.mkdir(path.join(this.options.cacheDir, 'search'), { recursive: true });

      // 加载磁盘缓存统计
      await this.loadDiskStats();

      // 启动定期清理
      this.startCleanupTimer();

      this.initialized = true;
      console.log('✅ [CacheManager] 缓存管理器初始化完成');
      
    } catch (error) {
      console.error('❌ [CacheManager] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取缓存项
   * @param {string} key - 缓存键
   * @param {string} category - 缓存分类 ('vectors', 'documents', 'search')
   */
  async get(key, category = 'vectors') {
    if (!this.initialized) await this.initialize();

    this.diskStats.totalRequests++;
    
    // 1. 检查内存缓存
    const memoryKey = `${category}:${key}`;
    if (this.memoryCache.has(memoryKey)) {
      this.updateAccessOrder(memoryKey);
      this.diskStats.hits++;
      console.log('🎯 [Cache] 内存缓存命中:', key);
      return this.memoryCache.get(memoryKey).data;
    }

    // 2. 检查磁盘缓存
    try {
      const filePath = this.getCacheFilePath(key, category);
      const metaPath = this.getMetaFilePath(key, category);

      // 检查文件是否存在
      await fs.access(filePath);
      await fs.access(metaPath);

      // 读取元数据
      const metaData = JSON.parse(await fs.readFile(metaPath, 'utf8'));
      
      // 检查TTL
      if (Date.now() > metaData.expireAt) {
        await this.deleteCacheFile(key, category);
        return null;
      }

      // 读取数据
      let data;
      if (metaData.compressed) {
        const zlib = require('zlib');
        const compressed = await fs.readFile(filePath);
        data = JSON.parse(zlib.gunzipSync(compressed).toString());
      } else {
        data = JSON.parse(await fs.readFile(filePath, 'utf8'));
      }

      // 添加到内存缓存
      await this.addToMemoryCache(memoryKey, data, metaData.size);
      
      this.diskStats.hits++;
      console.log('💾 [Cache] 磁盘缓存命中:', key);
      return data;

    } catch (error) {
      // 缓存未命中
      return null;
    }
  }

  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {*} data - 缓存数据
   * @param {string} category - 缓存分类
   * @param {Object} options - 选项
   */
  async set(key, data, category = 'vectors', options = {}) {
    if (!this.initialized) await this.initialize();

    const {
      ttl = this.options.ttl,
      compress = true,
      priority = 'normal' // 'low', 'normal', 'high'
    } = options;

    try {
      const serialized = JSON.stringify(data);
      const size = Buffer.byteLength(serialized, 'utf8');
      
      // 创建元数据
      const metaData = {
        key,
        category,
        size,
        compressed: compress && size > 1024, // 大于1KB才压缩
        priority,
        createdAt: Date.now(),
        expireAt: Date.now() + ttl,
        accessCount: 0,
        lastAccess: Date.now()
      };

      // 保存到磁盘
      await this.saveToDisk(key, data, category, metaData);

      // 添加到内存缓存
      const memoryKey = `${category}:${key}`;
      await this.addToMemoryCache(memoryKey, data, size, metaData);

      console.log(`💾 [Cache] 缓存已保存: ${key} (${this.formatSize(size)})`);

    } catch (error) {
      console.error('❌ [Cache] 保存缓存失败:', error);
      throw error;
    }
  }

  /**
   * 保存到磁盘
   */
  async saveToDisk(key, data, category, metaData) {
    const filePath = this.getCacheFilePath(key, category);
    const metaPath = this.getMetaFilePath(key, category);

    let content = JSON.stringify(data);
    
    // 压缩大文件
    if (metaData.compressed) {
      const zlib = require('zlib');
      content = zlib.gzipSync(content, { level: this.options.compressionLevel });
    }

    // 原子写入
    const tempFilePath = filePath + '.tmp';
    const tempMetaPath = metaPath + '.tmp';

    await fs.writeFile(tempFilePath, content);
    await fs.writeFile(tempMetaPath, JSON.stringify(metaData, null, 2));

    await fs.rename(tempFilePath, filePath);
    await fs.rename(tempMetaPath, metaPath);

    // 更新统计
    this.diskStats.totalSize += metaData.size;
    this.diskStats.fileCount++;
  }

  /**
   * 添加到内存缓存
   */
  async addToMemoryCache(key, data, size, metaData = null) {
    // 检查内存限制
    while (this.memorySize + size > this.options.maxMemorySize || 
           this.memoryCache.size >= this.options.maxMemoryItems) {
      await this.evictLRU();
    }

    const cacheItem = {
      data,
      size,
      timestamp: Date.now(),
      accessCount: 1,
      metaData
    };

    this.memoryCache.set(key, cacheItem);
    this.updateAccessOrder(key);
    this.memorySize += size;
  }

  /**
   * LRU淘汰策略
   */
  async evictLRU() {
    if (this.accessOrder.size === 0) return;

    // 找到最久未访问的项
    const oldestKey = this.accessOrder.keys().next().value;
    const item = this.memoryCache.get(oldestKey);

    if (item) {
      this.memoryCache.delete(oldestKey);
      this.accessOrder.delete(oldestKey);
      this.memorySize -= item.size;
      
      console.log(`🗑️ [Cache] LRU淘汰: ${oldestKey}`);
    }
  }

  /**
   * 更新访问顺序
   */
  updateAccessOrder(key) {
    if (this.accessOrder.has(key)) {
      this.accessOrder.delete(key);
    }
    this.accessOrder.set(key, Date.now());

    // 更新访问计数
    const item = this.memoryCache.get(key);
    if (item) {
      item.accessCount++;
    }
  }

  /**
   * 删除缓存
   */
  async delete(key, category = 'vectors') {
    const memoryKey = `${category}:${key}`;
    
    // 从内存删除
    if (this.memoryCache.has(memoryKey)) {
      const item = this.memoryCache.get(memoryKey);
      this.memoryCache.delete(memoryKey);
      this.accessOrder.delete(memoryKey);
      this.memorySize -= item.size;
    }

    // 从磁盘删除
    await this.deleteCacheFile(key, category);
  }

  /**
   * 删除磁盘缓存文件
   */
  async deleteCacheFile(key, category) {
    try {
      const filePath = this.getCacheFilePath(key, category);
      const metaPath = this.getMetaFilePath(key, category);

      await fs.unlink(filePath);
      await fs.unlink(metaPath);

      this.diskStats.fileCount--;
      
    } catch (error) {
      // 文件可能不存在，忽略错误
    }
  }

  /**
   * 清理过期缓存
   */
  async cleanup() {
    console.log('🧹 [Cache] 开始清理过期缓存');
    
    const categories = ['vectors', 'documents', 'search'];
    let cleanedCount = 0;
    let reclaimedSize = 0;

    for (const category of categories) {
      const categoryDir = path.join(this.options.cacheDir, category);
      
      try {
        const files = await fs.readdir(categoryDir);
        const metaFiles = files.filter(f => f.endsWith('.meta'));

        for (const metaFile of metaFiles) {
          const metaPath = path.join(categoryDir, metaFile);
          
          try {
            const metaData = JSON.parse(await fs.readFile(metaPath, 'utf8'));
            
            if (Date.now() > metaData.expireAt) {
              const key = metaFile.replace('.meta', '');
              await this.deleteCacheFile(key, category);
              cleanedCount++;
              reclaimedSize += metaData.size;
            }
          } catch (error) {
            // 元数据文件损坏，删除
            await fs.unlink(metaPath);
          }
        }
      } catch (error) {
        console.error(`❌ [Cache] 清理分类 ${category} 失败:`, error);
      }
    }

    console.log(`✅ [Cache] 清理完成: ${cleanedCount}个文件, 回收${this.formatSize(reclaimedSize)}`);
  }

  /**
   * 启动定期清理
   */
  startCleanupTimer() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup().catch(console.error);
    }, this.options.cleanupInterval);
  }

  /**
   * 获取缓存统计
   */
  getStats() {
    this.diskStats.hitRate = this.diskStats.totalRequests > 0 
      ? (this.diskStats.hits / this.diskStats.totalRequests * 100).toFixed(2)
      : 0;

    return {
      memory: {
        items: this.memoryCache.size,
        size: this.memorySize,
        maxSize: this.options.maxMemorySize,
        usage: ((this.memorySize / this.options.maxMemorySize) * 100).toFixed(2) + '%'
      },
      disk: {
        ...this.diskStats,
        maxSize: this.options.maxDiskSize,
        usage: ((this.diskStats.totalSize / this.options.maxDiskSize) * 100).toFixed(2) + '%'
      }
    };
  }

  /**
   * 工具方法
   */
  getCacheFilePath(key, category) {
    const hash = crypto.createHash('md5').update(key).digest('hex');
    return path.join(this.options.cacheDir, category, `${hash}.cache`);
  }

  getMetaFilePath(key, category) {
    const hash = crypto.createHash('md5').update(key).digest('hex');
    return path.join(this.options.cacheDir, category, `${hash}.meta`);
  }

  formatSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  async loadDiskStats() {
    // 扫描磁盘缓存，计算统计信息
    const categories = ['vectors', 'documents', 'search'];
    
    for (const category of categories) {
      const categoryDir = path.join(this.options.cacheDir, category);
      
      try {
        const files = await fs.readdir(categoryDir);
        const metaFiles = files.filter(f => f.endsWith('.meta'));

        for (const metaFile of metaFiles) {
          const metaPath = path.join(categoryDir, metaFile);
          
          try {
            const metaData = JSON.parse(await fs.readFile(metaPath, 'utf8'));
            this.diskStats.totalSize += metaData.size;
            this.diskStats.fileCount++;
          } catch (error) {
            // 忽略损坏的元数据文件
          }
        }
      } catch (error) {
        // 目录不存在，忽略
      }
    }
  }

  /**
   * 清理资源
   */
  async destroy() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    this.memoryCache.clear();
    this.accessOrder.clear();
    this.memorySize = 0;
    this.initialized = false;

    console.log('🧹 [Cache] 缓存管理器已清理');
  }
}

module.exports = CacheManager;
