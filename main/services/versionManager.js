const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { EventEmitter } = require('events');

/**
 * 文档版本管理器
 * 实现文档版本控制、对比和历史追踪
 */
class VersionManager extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      versionsDir: options.versionsDir || path.join(process.cwd(), 'versions'),
      maxVersions: options.maxVersions || 50,
      autoSave: options.autoSave !== false,
      compressionLevel: options.compressionLevel || 6,
      ...options
    };

    this.versions = new Map(); // documentId -> versions[]
    this.diffCache = new Map(); // 差异缓存
    this.initialized = false;
  }

  /**
   * 初始化版本管理器
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // 创建版本目录
      await fs.mkdir(this.options.versionsDir, { recursive: true });

      // 加载现有版本数据
      await this.loadVersions();

      this.initialized = true;
      console.log('✅ [VersionManager] 版本管理器初始化完成');
      
    } catch (error) {
      console.error('❌ [VersionManager] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 保存文档版本
   * @param {string} documentId - 文档ID
   * @param {string} content - 文档内容
   * @param {Object} metadata - 元数据
   */
  async saveVersion(documentId, content, metadata = {}) {
    if (!this.initialized) await this.initialize();

    try {
      const version = {
        id: crypto.randomUUID(),
        documentId,
        content,
        hash: this.calculateHash(content),
        size: Buffer.byteLength(content, 'utf8'),
        timestamp: new Date().toISOString(),
        metadata: {
          ...metadata,
          author: metadata.author || 'system',
          comment: metadata.comment || '自动保存',
          tags: metadata.tags || []
        }
      };

      // 检查是否有内容变化
      const existingVersions = this.versions.get(documentId) || [];
      if (existingVersions.length > 0) {
        const lastVersion = existingVersions[existingVersions.length - 1];
        if (lastVersion.hash === version.hash) {
          console.log('📝 [VersionManager] 内容无变化，跳过版本保存');
          return lastVersion;
        }
      }

      // 添加版本到内存
      if (!this.versions.has(documentId)) {
        this.versions.set(documentId, []);
      }

      const versions = this.versions.get(documentId);
      versions.push(version);

      // 限制版本数量
      if (versions.length > this.options.maxVersions) {
        const removedVersion = versions.shift();
        await this.deleteVersionFile(removedVersion);
      }

      // 保存到磁盘
      await this.saveVersionToDisk(version);

      // 保存版本索引
      if (this.options.autoSave) {
        await this.saveVersionIndex(documentId);
      }

      console.log(`📝 [VersionManager] 版本已保存: ${documentId} v${versions.length}`);
      
      this.emit('versionSaved', {
        documentId,
        version,
        totalVersions: versions.length
      });

      return version;

    } catch (error) {
      console.error('❌ [VersionManager] 保存版本失败:', error);
      throw error;
    }
  }

  /**
   * 获取文档版本列表
   * @param {string} documentId - 文档ID
   */
  getVersions(documentId) {
    return this.versions.get(documentId) || [];
  }

  /**
   * 获取特定版本
   * @param {string} documentId - 文档ID
   * @param {string} versionId - 版本ID
   */
  async getVersion(documentId, versionId) {
    const versions = this.getVersions(documentId);
    const version = versions.find(v => v.id === versionId);

    if (!version) {
      throw new Error(`版本不存在: ${versionId}`);
    }

    // 如果内容不在内存中，从磁盘加载
    if (!version.content) {
      version.content = await this.loadVersionFromDisk(version);
    }

    return version;
  }

  /**
   * 比较两个版本
   * @param {string} documentId - 文档ID
   * @param {string} version1Id - 版本1 ID
   * @param {string} version2Id - 版本2 ID
   */
  async compareVersions(documentId, version1Id, version2Id) {
    try {
      // 检查缓存
      const cacheKey = `${documentId}_${version1Id}_${version2Id}`;
      if (this.diffCache.has(cacheKey)) {
        console.log('🎯 [VersionManager] 差异缓存命中');
        return this.diffCache.get(cacheKey);
      }

      const version1 = await this.getVersion(documentId, version1Id);
      const version2 = await this.getVersion(documentId, version2Id);

      // 使用 diff 库进行比较
      const diff = await this.calculateDiff(version1.content, version2.content);

      const comparison = {
        documentId,
        version1: {
          id: version1.id,
          timestamp: version1.timestamp,
          hash: version1.hash,
          size: version1.size
        },
        version2: {
          id: version2.id,
          timestamp: version2.timestamp,
          hash: version2.hash,
          size: version2.size
        },
        diff,
        summary: this.generateDiffSummary(diff),
        statistics: this.calculateDiffStats(diff),
        generatedAt: new Date().toISOString()
      };

      // 缓存结果
      this.diffCache.set(cacheKey, comparison);
      
      // 限制缓存大小
      if (this.diffCache.size > 100) {
        const firstKey = this.diffCache.keys().next().value;
        this.diffCache.delete(firstKey);
      }

      console.log(`📊 [VersionManager] 版本比较完成: ${comparison.statistics.totalChanges} 个变更`);
      return comparison;

    } catch (error) {
      console.error('❌ [VersionManager] 版本比较失败:', error);
      throw error;
    }
  }

  /**
   * 计算文本差异
   */
  async calculateDiff(text1, text2) {
    try {
      // 动态导入 diff 库
      const { diffLines, diffWords, diffChars } = await import('diff');

      return {
        lines: diffLines(text1, text2),
        words: diffWords(text1, text2),
        chars: diffChars(text1, text2)
      };

    } catch (error) {
      // 如果 diff 库不可用，使用简单实现
      return this.simpleDiff(text1, text2);
    }
  }

  /**
   * 简单差异实现（备选方案）
   */
  simpleDiff(text1, text2) {
    const lines1 = text1.split('\n');
    const lines2 = text2.split('\n');
    const changes = [];

    const maxLines = Math.max(lines1.length, lines2.length);
    
    for (let i = 0; i < maxLines; i++) {
      const line1 = lines1[i] || '';
      const line2 = lines2[i] || '';

      if (line1 !== line2) {
        if (!lines1[i]) {
          changes.push({ added: true, value: line2 + '\n' });
        } else if (!lines2[i]) {
          changes.push({ removed: true, value: line1 + '\n' });
        } else {
          changes.push({ removed: true, value: line1 + '\n' });
          changes.push({ added: true, value: line2 + '\n' });
        }
      } else {
        changes.push({ value: line1 + '\n' });
      }
    }

    return {
      lines: changes,
      words: changes, // 简化实现
      chars: changes  // 简化实现
    };
  }

  /**
   * 生成差异摘要
   */
  generateDiffSummary(diff) {
    const summary = {
      lines: { added: 0, removed: 0, modified: 0 },
      words: { added: 0, removed: 0, modified: 0 },
      chars: { added: 0, removed: 0, modified: 0 }
    };

    ['lines', 'words', 'chars'].forEach(type => {
      if (diff[type]) {
        diff[type].forEach(change => {
          if (change.added) {
            summary[type].added++;
          } else if (change.removed) {
            summary[type].removed++;
          }
        });
      }
    });

    return summary;
  }

  /**
   * 计算差异统计
   */
  calculateDiffStats(diff) {
    const stats = {
      totalChanges: 0,
      addedLines: 0,
      removedLines: 0,
      modifiedLines: 0,
      similarity: 0
    };

    if (diff.lines) {
      diff.lines.forEach(change => {
        if (change.added) {
          stats.addedLines++;
          stats.totalChanges++;
        } else if (change.removed) {
          stats.removedLines++;
          stats.totalChanges++;
        }
      });

      // 计算相似度
      const totalLines = diff.lines.length;
      const unchangedLines = totalLines - stats.totalChanges;
      stats.similarity = totalLines > 0 ? (unchangedLines / totalLines * 100).toFixed(2) : 100;
    }

    return stats;
  }

  /**
   * 恢复到指定版本
   * @param {string} documentId - 文档ID
   * @param {string} versionId - 版本ID
   */
  async restoreVersion(documentId, versionId) {
    try {
      const version = await this.getVersion(documentId, versionId);
      
      // 创建恢复版本
      const restoreVersion = await this.saveVersion(
        documentId,
        version.content,
        {
          ...version.metadata,
          comment: `恢复到版本 ${versionId}`,
          restoredFrom: versionId
        }
      );

      console.log(`🔄 [VersionManager] 已恢复到版本: ${versionId}`);
      
      this.emit('versionRestored', {
        documentId,
        restoredFrom: versionId,
        newVersion: restoreVersion
      });

      return restoreVersion;

    } catch (error) {
      console.error('❌ [VersionManager] 版本恢复失败:', error);
      throw error;
    }
  }

  /**
   * 删除版本
   * @param {string} documentId - 文档ID
   * @param {string} versionId - 版本ID
   */
  async deleteVersion(documentId, versionId) {
    try {
      const versions = this.versions.get(documentId) || [];
      const versionIndex = versions.findIndex(v => v.id === versionId);

      if (versionIndex === -1) {
        throw new Error(`版本不存在: ${versionId}`);
      }

      const version = versions[versionIndex];
      
      // 从内存中删除
      versions.splice(versionIndex, 1);

      // 从磁盘删除
      await this.deleteVersionFile(version);

      // 保存更新的索引
      if (this.options.autoSave) {
        await this.saveVersionIndex(documentId);
      }

      console.log(`🗑️ [VersionManager] 版本已删除: ${versionId}`);
      
      this.emit('versionDeleted', {
        documentId,
        versionId,
        remainingVersions: versions.length
      });

      return true;

    } catch (error) {
      console.error('❌ [VersionManager] 删除版本失败:', error);
      throw error;
    }
  }

  /**
   * 保存版本到磁盘
   */
  async saveVersionToDisk(version) {
    const versionDir = path.join(this.options.versionsDir, version.documentId);
    await fs.mkdir(versionDir, { recursive: true });

    const versionFile = path.join(versionDir, `${version.id}.json`);
    
    // 压缩大文件
    let content = version.content;
    if (version.size > 10240) { // 大于10KB压缩
      const zlib = require('zlib');
      content = zlib.gzipSync(content, { level: this.options.compressionLevel }).toString('base64');
      version.compressed = true;
    }

    const versionData = {
      ...version,
      content
    };

    await fs.writeFile(versionFile, JSON.stringify(versionData, null, 2));
  }

  /**
   * 从磁盘加载版本
   */
  async loadVersionFromDisk(version) {
    const versionFile = path.join(this.options.versionsDir, version.documentId, `${version.id}.json`);
    
    try {
      const data = JSON.parse(await fs.readFile(versionFile, 'utf8'));
      
      let content = data.content;
      if (data.compressed) {
        const zlib = require('zlib');
        content = zlib.gunzipSync(Buffer.from(content, 'base64')).toString();
      }

      return content;

    } catch (error) {
      throw new Error(`加载版本失败: ${error.message}`);
    }
  }

  /**
   * 删除版本文件
   */
  async deleteVersionFile(version) {
    const versionFile = path.join(this.options.versionsDir, version.documentId, `${version.id}.json`);
    
    try {
      await fs.unlink(versionFile);
    } catch (error) {
      // 文件可能不存在，忽略错误
    }
  }

  /**
   * 保存版本索引
   */
  async saveVersionIndex(documentId) {
    const indexFile = path.join(this.options.versionsDir, `${documentId}.index`);
    const versions = this.versions.get(documentId) || [];
    
    // 只保存元数据，不保存内容
    const index = versions.map(v => ({
      id: v.id,
      hash: v.hash,
      size: v.size,
      timestamp: v.timestamp,
      metadata: v.metadata,
      compressed: v.compressed
    }));

    await fs.writeFile(indexFile, JSON.stringify(index, null, 2));
  }

  /**
   * 加载版本数据
   */
  async loadVersions() {
    try {
      const files = await fs.readdir(this.options.versionsDir);
      const indexFiles = files.filter(f => f.endsWith('.index'));

      for (const indexFile of indexFiles) {
        const documentId = indexFile.replace('.index', '');
        const indexPath = path.join(this.options.versionsDir, indexFile);
        
        try {
          const index = JSON.parse(await fs.readFile(indexPath, 'utf8'));
          this.versions.set(documentId, index);
          
          console.log(`📚 [VersionManager] 加载文档版本: ${documentId} (${index.length}个版本)`);
        } catch (error) {
          console.error(`❌ [VersionManager] 加载版本索引失败: ${indexFile}`, error);
        }
      }

    } catch (error) {
      // 版本目录不存在，忽略
    }
  }

  /**
   * 计算内容哈希
   */
  calculateHash(content) {
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  /**
   * 获取版本统计
   */
  getStats() {
    const stats = {
      totalDocuments: this.versions.size,
      totalVersions: 0,
      totalSize: 0,
      oldestVersion: null,
      newestVersion: null
    };

    for (const [documentId, versions] of this.versions) {
      stats.totalVersions += versions.length;
      
      versions.forEach(version => {
        stats.totalSize += version.size;
        
        if (!stats.oldestVersion || version.timestamp < stats.oldestVersion) {
          stats.oldestVersion = version.timestamp;
        }
        
        if (!stats.newestVersion || version.timestamp > stats.newestVersion) {
          stats.newestVersion = version.timestamp;
        }
      });
    }

    return stats;
  }

  /**
   * 清理资源
   */
  async cleanup() {
    this.versions.clear();
    this.diffCache.clear();
    this.initialized = false;
    
    console.log('🧹 [VersionManager] 版本管理器已清理');
  }
}

  /**
   * 清理资源
   */
  async cleanup() {
    this.versions.clear();
    this.diffCache.clear();
    this.initialized = false;

    console.log('🧹 [VersionManager] 版本管理器已清理');
  }
}

module.exports = VersionManager;
