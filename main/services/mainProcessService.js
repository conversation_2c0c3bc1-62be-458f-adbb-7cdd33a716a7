const { ipcMain } = require('electron');
const VectorSearchService = require('./vectorSearchService');
const CacheManager = require('./cacheManager');
const DocumentProcessor = require('./documentProcessor');
const VersionManager = require('./versionManager');
const KnowledgeGraphManager = require('./knowledgeGraphManager');

/**
 * 主进程服务管理器
 * 集成所有后台服务并提供IPC接口
 */
class MainProcessService {
  constructor(options = {}) {
    this.options = {
      enableVectorSearch: options.enableVectorSearch !== false,
      enableCache: options.enableCache !== false,
      enableDocumentProcessor: options.enableDocumentProcessor !== false,
      enableVersionManager: options.enableVersionManager !== false,
      enableKnowledgeGraph: options.enableKnowledgeGraph !== false,
      ...options
    };

    this.services = {};
    this.initialized = false;
  }

  /**
   * 初始化所有服务
   */
  async initialize() {
    if (this.initialized) return;

    try {
      console.log('🚀 [MainProcess] 开始初始化后台服务...');

      // 初始化缓存管理器
      if (this.options.enableCache) {
        this.services.cache = new CacheManager(this.options.cache);
        await this.services.cache.initialize();
      }

      // 初始化向量搜索服务
      if (this.options.enableVectorSearch) {
        this.services.vectorSearch = new VectorSearchService(this.options.vectorSearch);
        await this.services.vectorSearch.initialize();
      }

      // 初始化文档处理器
      if (this.options.enableDocumentProcessor) {
        this.services.documentProcessor = new DocumentProcessor(this.options.documentProcessor);
        await this.services.documentProcessor.initialize();
      }

      // 初始化版本管理器
      if (this.options.enableVersionManager) {
        this.services.versionManager = new VersionManager(this.options.versionManager);
        await this.services.versionManager.initialize();
      }

      // 初始化知识图谱管理器
      if (this.options.enableKnowledgeGraph) {
        this.services.knowledgeGraph = new KnowledgeGraphManager(this.options.knowledgeGraph);
        await this.services.knowledgeGraph.initialize();
      }

      // 设置IPC处理器
      this.setupIPC();

      this.initialized = true;
      console.log('✅ [MainProcess] 所有后台服务初始化完成');

    } catch (error) {
      console.error('❌ [MainProcess] 服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置IPC通信处理器
   */
  setupIPC() {
    // 向量搜索相关
    if (this.services.vectorSearch) {
      ipcMain.handle('vector-search', async (event, queryVector, vectors, options) => {
        try {
          return await this.services.vectorSearch.search(queryVector, vectors, options);
        } catch (error) {
          console.error('❌ [IPC] 向量搜索失败:', error);
          throw error;
        }
      });

      ipcMain.handle('vector-search-stats', async (event) => {
        return this.services.vectorSearch.getStats();
      });
    }

    // 缓存管理相关
    if (this.services.cache) {
      ipcMain.handle('cache-get', async (event, key, category) => {
        try {
          return await this.services.cache.get(key, category);
        } catch (error) {
          console.error('❌ [IPC] 缓存获取失败:', error);
          return null;
        }
      });

      ipcMain.handle('cache-set', async (event, key, data, category, options) => {
        try {
          await this.services.cache.set(key, data, category, options);
          return true;
        } catch (error) {
          console.error('❌ [IPC] 缓存设置失败:', error);
          return false;
        }
      });

      ipcMain.handle('cache-delete', async (event, key, category) => {
        try {
          await this.services.cache.delete(key, category);
          return true;
        } catch (error) {
          console.error('❌ [IPC] 缓存删除失败:', error);
          return false;
        }
      });

      ipcMain.handle('cache-stats', async (event) => {
        return this.services.cache.getStats();
      });

      ipcMain.handle('cache-cleanup', async (event) => {
        try {
          await this.services.cache.cleanup();
          return true;
        } catch (error) {
          console.error('❌ [IPC] 缓存清理失败:', error);
          return false;
        }
      });
    }

    // 文档处理相关
    if (this.services.documentProcessor) {
      ipcMain.handle('process-document', async (event, filePath, options) => {
        try {
          return await this.services.documentProcessor.processDocument(filePath, options);
        } catch (error) {
          console.error('❌ [IPC] 文档处理失败:', error);
          throw error;
        }
      });

      ipcMain.handle('process-documents-batch', async (event, filePaths, options) => {
        try {
          return await this.services.documentProcessor.processDocuments(filePaths, options);
        } catch (error) {
          console.error('❌ [IPC] 批量文档处理失败:', error);
          throw error;
        }
      });

      ipcMain.handle('get-supported-formats', async (event) => {
        return this.services.documentProcessor.getSupportedFormats();
      });

      // 监听处理进度事件
      this.services.documentProcessor.on('batchProgress', (progress) => {
        event.sender.send('document-processing-progress', progress);
      });
    }

    // 版本管理相关
    if (this.services.versionManager) {
      ipcMain.handle('version-save', async (event, documentId, content, metadata) => {
        try {
          return await this.services.versionManager.saveVersion(documentId, content, metadata);
        } catch (error) {
          console.error('❌ [IPC] 版本保存失败:', error);
          throw error;
        }
      });

      ipcMain.handle('version-get-list', async (event, documentId) => {
        return this.services.versionManager.getVersions(documentId);
      });

      ipcMain.handle('version-get', async (event, documentId, versionId) => {
        try {
          return await this.services.versionManager.getVersion(documentId, versionId);
        } catch (error) {
          console.error('❌ [IPC] 版本获取失败:', error);
          throw error;
        }
      });

      ipcMain.handle('version-compare', async (event, documentId, version1Id, version2Id) => {
        try {
          return await this.services.versionManager.compareVersions(documentId, version1Id, version2Id);
        } catch (error) {
          console.error('❌ [IPC] 版本比较失败:', error);
          throw error;
        }
      });

      ipcMain.handle('version-restore', async (event, documentId, versionId) => {
        try {
          return await this.services.versionManager.restoreVersion(documentId, versionId);
        } catch (error) {
          console.error('❌ [IPC] 版本恢复失败:', error);
          throw error;
        }
      });

      ipcMain.handle('version-delete', async (event, documentId, versionId) => {
        try {
          return await this.services.versionManager.deleteVersion(documentId, versionId);
        } catch (error) {
          console.error('❌ [IPC] 版本删除失败:', error);
          throw error;
        }
      });

      ipcMain.handle('version-stats', async (event) => {
        return this.services.versionManager.getStats();
      });
    }

    // 知识图谱相关
    if (this.services.knowledgeGraph) {
      ipcMain.handle('graph-add-kb-relation', async (event, sourceKbId, targetKbId, relationType, metadata) => {
        try {
          return await this.services.knowledgeGraph.addKnowledgeBaseRelation(sourceKbId, targetKbId, relationType, metadata);
        } catch (error) {
          console.error('❌ [IPC] 添加知识库关系失败:', error);
          throw error;
        }
      });

      ipcMain.handle('graph-add-doc-relation', async (event, sourceDocId, targetDocId, relationType, context) => {
        try {
          return await this.services.knowledgeGraph.addDocumentRelation(sourceDocId, targetDocId, relationType, context);
        } catch (error) {
          console.error('❌ [IPC] 添加文档关系失败:', error);
          throw error;
        }
      });

      ipcMain.handle('graph-discover-relations', async (event, documents, options) => {
        try {
          return await this.services.knowledgeGraph.discoverDocumentRelations(documents, options);
        } catch (error) {
          console.error('❌ [IPC] 关系发现失败:', error);
          throw error;
        }
      });

      ipcMain.handle('graph-find-related', async (event, documentId, options) => {
        try {
          return await this.services.knowledgeGraph.findRelatedDocuments(documentId, options);
        } catch (error) {
          console.error('❌ [IPC] 查找相关文档失败:', error);
          throw error;
        }
      });

      ipcMain.handle('graph-get-kb-relations', async (event, knowledgeBaseId) => {
        return this.services.knowledgeGraph.getKnowledgeBaseRelations(knowledgeBaseId);
      });

      ipcMain.handle('graph-analyze', async (event) => {
        return this.services.knowledgeGraph.analyzeGraph();
      });

      // 监听图谱事件
      this.services.knowledgeGraph.on('relationAdded', (data) => {
        event.sender.send('graph-relation-added', data);
      });

      this.services.knowledgeGraph.on('discoveryProgress', (progress) => {
        event.sender.send('graph-discovery-progress', progress);
      });
    }

    // 通用服务状态
    ipcMain.handle('service-status', async (event) => {
      const status = {};
      
      for (const [name, service] of Object.entries(this.services)) {
        status[name] = {
          initialized: service.initialized || true,
          stats: service.getStats ? service.getStats() : null
        };
      }

      return status;
    });

    // 服务重启
    ipcMain.handle('service-restart', async (event, serviceName) => {
      try {
        const service = this.services[serviceName];
        if (!service) {
          throw new Error(`服务不存在: ${serviceName}`);
        }

        if (service.cleanup) {
          await service.cleanup();
        }

        if (service.initialize) {
          await service.initialize();
        }

        console.log(`🔄 [MainProcess] 服务已重启: ${serviceName}`);
        return true;

      } catch (error) {
        console.error(`❌ [MainProcess] 服务重启失败: ${serviceName}`, error);
        throw error;
      }
    });

    console.log('✅ [MainProcess] IPC处理器设置完成');
  }

  /**
   * 获取服务实例
   */
  getService(serviceName) {
    return this.services[serviceName];
  }

  /**
   * 获取所有服务状态
   */
  getServicesStatus() {
    const status = {};
    
    for (const [name, service] of Object.entries(this.services)) {
      status[name] = {
        initialized: service.initialized || true,
        hasStats: !!service.getStats,
        hasCleanup: !!service.cleanup
      };
    }

    return status;
  }

  /**
   * 清理所有服务
   */
  async cleanup() {
    console.log('🧹 [MainProcess] 开始清理所有服务...');

    for (const [name, service] of Object.entries(this.services)) {
      try {
        if (service.cleanup) {
          await service.cleanup();
          console.log(`✅ [MainProcess] 服务已清理: ${name}`);
        }
      } catch (error) {
        console.error(`❌ [MainProcess] 服务清理失败: ${name}`, error);
      }
    }

    // 移除所有IPC处理器
    ipcMain.removeAllListeners();

    this.services = {};
    this.initialized = false;

    console.log('✅ [MainProcess] 所有服务清理完成');
  }
}

module.exports = MainProcessService;
