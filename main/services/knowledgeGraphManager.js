const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { EventEmitter } = require('events');

/**
 * 知识图谱管理器
 * 实现知识库间的关联、引用和关系网络
 */
class KnowledgeGraphManager extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      graphDir: options.graphDir || path.join(process.cwd(), 'knowledge-graph'),
      maxRelations: options.maxRelations || 10000,
      autoSave: options.autoSave !== false,
      relationTypes: options.relationTypes || [
        'references', 'extends', 'implements', 'depends_on', 
        'similar_to', 'contradicts', 'supports', 'derived_from'
      ],
      ...options
    };

    // 知识库关系图
    this.kbGraph = new Map(); // kbId -> Set<{targetId, type, weight, metadata}>
    
    // 文档关系图
    this.docGraph = new Map(); // docId -> Set<{targetId, type, weight, metadata}>
    
    // 概念关系图
    this.conceptGraph = new Map(); // concept -> Set<{relatedConcept, type, weight}>
    
    // 反向索引
    this.reverseIndex = new Map(); // targetId -> Set<sourceId>
    
    this.initialized = false;
  }

  /**
   * 初始化知识图谱管理器
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // 创建图谱目录
      await fs.mkdir(this.options.graphDir, { recursive: true });

      // 加载现有图谱数据
      await this.loadGraphData();

      this.initialized = true;
      console.log('✅ [KnowledgeGraph] 知识图谱管理器初始化完成');
      
    } catch (error) {
      console.error('❌ [KnowledgeGraph] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 添加知识库关系
   * @param {string} sourceKbId - 源知识库ID
   * @param {string} targetKbId - 目标知识库ID
   * @param {string} relationType - 关系类型
   * @param {Object} metadata - 关系元数据
   */
  async addKnowledgeBaseRelation(sourceKbId, targetKbId, relationType, metadata = {}) {
    if (!this.initialized) await this.initialize();

    try {
      if (!this.options.relationTypes.includes(relationType)) {
        throw new Error(`不支持的关系类型: ${relationType}`);
      }

      const relation = {
        targetId: targetKbId,
        type: relationType,
        weight: metadata.weight || 1.0,
        confidence: metadata.confidence || 0.8,
        metadata: {
          ...metadata,
          createdAt: new Date().toISOString(),
          createdBy: metadata.createdBy || 'system'
        }
      };

      // 添加到图中
      if (!this.kbGraph.has(sourceKbId)) {
        this.kbGraph.set(sourceKbId, new Set());
      }

      // 检查是否已存在相同关系
      const existingRelations = this.kbGraph.get(sourceKbId);
      const existingRelation = Array.from(existingRelations).find(
        r => r.targetId === targetKbId && r.type === relationType
      );

      if (existingRelation) {
        // 更新现有关系
        existingRelation.weight = Math.max(existingRelation.weight, relation.weight);
        existingRelation.confidence = Math.max(existingRelation.confidence, relation.confidence);
        existingRelation.metadata.updatedAt = new Date().toISOString();
      } else {
        // 添加新关系
        existingRelations.add(relation);
      }

      // 更新反向索引
      if (!this.reverseIndex.has(targetKbId)) {
        this.reverseIndex.set(targetKbId, new Set());
      }
      this.reverseIndex.get(targetKbId).add(sourceKbId);

      // 自动保存
      if (this.options.autoSave) {
        await this.saveGraphData();
      }

      console.log(`🔗 [KnowledgeGraph] 知识库关系已添加: ${sourceKbId} -> ${targetKbId} (${relationType})`);
      
      this.emit('relationAdded', {
        type: 'knowledgeBase',
        sourceId: sourceKbId,
        targetId: targetKbId,
        relationType,
        relation
      });

      return relation;

    } catch (error) {
      console.error('❌ [KnowledgeGraph] 添加知识库关系失败:', error);
      throw error;
    }
  }

  /**
   * 添加文档关系
   * @param {string} sourceDocId - 源文档ID
   * @param {string} targetDocId - 目标文档ID
   * @param {string} relationType - 关系类型
   * @param {Object} context - 关系上下文
   */
  async addDocumentRelation(sourceDocId, targetDocId, relationType, context = {}) {
    if (!this.initialized) await this.initialize();

    try {
      const relation = {
        targetId: targetDocId,
        type: relationType,
        weight: context.weight || 1.0,
        confidence: context.confidence || 0.8,
        context: {
          ...context,
          sourceText: context.sourceText || '',
          targetText: context.targetText || '',
          similarity: context.similarity || 0,
          createdAt: new Date().toISOString()
        }
      };

      // 添加到文档图中
      if (!this.docGraph.has(sourceDocId)) {
        this.docGraph.set(sourceDocId, new Set());
      }

      this.docGraph.get(sourceDocId).add(relation);

      // 更新反向索引
      if (!this.reverseIndex.has(targetDocId)) {
        this.reverseIndex.set(targetDocId, new Set());
      }
      this.reverseIndex.get(targetDocId).add(sourceDocId);

      console.log(`📄 [KnowledgeGraph] 文档关系已添加: ${sourceDocId} -> ${targetDocId} (${relationType})`);
      
      this.emit('relationAdded', {
        type: 'document',
        sourceId: sourceDocId,
        targetId: targetDocId,
        relationType,
        relation
      });

      return relation;

    } catch (error) {
      console.error('❌ [KnowledgeGraph] 添加文档关系失败:', error);
      throw error;
    }
  }

  /**
   * 自动发现文档关系
   * @param {Array} documents - 文档列表
   * @param {Object} options - 发现选项
   */
  async discoverDocumentRelations(documents, options = {}) {
    const {
      similarityThreshold = 0.7,
      maxRelationsPerDoc = 5,
      enableSemanticAnalysis = true
    } = options;

    console.log(`🔍 [KnowledgeGraph] 开始自动发现文档关系: ${documents.length} 个文档`);

    const relations = [];

    // 计算文档间相似度
    for (let i = 0; i < documents.length; i++) {
      for (let j = i + 1; j < documents.length; j++) {
        const doc1 = documents[i];
        const doc2 = documents[j];

        try {
          // 计算文本相似度
          const similarity = await this.calculateTextSimilarity(doc1.content, doc2.content);

          if (similarity >= similarityThreshold) {
            // 确定关系类型
            const relationType = this.determineRelationType(doc1, doc2, similarity);

            await this.addDocumentRelation(doc1.id, doc2.id, relationType, {
              similarity,
              weight: similarity,
              confidence: similarity,
              discoveryMethod: 'automatic',
              sourceText: doc1.content.substring(0, 200),
              targetText: doc2.content.substring(0, 200)
            });

            relations.push({
              source: doc1.id,
              target: doc2.id,
              type: relationType,
              similarity
            });
          }

        } catch (error) {
          console.error(`❌ [KnowledgeGraph] 计算文档相似度失败: ${doc1.id} <-> ${doc2.id}`, error);
        }
      }

      // 发送进度事件
      this.emit('discoveryProgress', {
        current: i + 1,
        total: documents.length,
        relationsFound: relations.length
      });
    }

    console.log(`✅ [KnowledgeGraph] 关系发现完成: ${relations.length} 个关系`);
    return relations;
  }

  /**
   * 计算文本相似度
   */
  async calculateTextSimilarity(text1, text2) {
    // 简单的基于词汇重叠的相似度计算
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));

    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);

    return intersection.size / union.size;
  }

  /**
   * 确定关系类型
   */
  determineRelationType(doc1, doc2, similarity) {
    // 基于文档元数据和内容特征确定关系类型
    if (doc1.metadata?.category === doc2.metadata?.category) {
      return 'similar_to';
    }

    if (similarity > 0.9) {
      return 'extends';
    }

    if (similarity > 0.8) {
      return 'references';
    }

    return 'similar_to';
  }

  /**
   * 查找相关文档
   * @param {string} documentId - 文档ID
   * @param {Object} options - 查询选项
   */
  async findRelatedDocuments(documentId, options = {}) {
    const {
      maxDepth = 2,
      maxResults = 10,
      minWeight = 0.5,
      relationTypes = null
    } = options;

    const visited = new Set();
    const related = [];
    const queue = [{ id: documentId, depth: 0, path: [] }];

    while (queue.length > 0 && related.length < maxResults) {
      const { id, depth, path } = queue.shift();

      if (depth > maxDepth || visited.has(id)) {
        continue;
      }

      visited.add(id);

      // 获取直接关系
      const relations = this.docGraph.get(id) || new Set();
      
      for (const relation of relations) {
        if (visited.has(relation.targetId)) continue;
        
        // 过滤条件
        if (relation.weight < minWeight) continue;
        if (relationTypes && !relationTypes.includes(relation.type)) continue;

        const relatedDoc = {
          documentId: relation.targetId,
          relationType: relation.type,
          weight: relation.weight,
          confidence: relation.confidence,
          depth: depth + 1,
          path: [...path, id],
          context: relation.context
        };

        related.push(relatedDoc);

        // 添加到队列继续搜索
        if (depth + 1 < maxDepth) {
          queue.push({
            id: relation.targetId,
            depth: depth + 1,
            path: [...path, id]
          });
        }
      }

      // 检查反向关系
      const reverseRelations = this.reverseIndex.get(id) || new Set();
      for (const sourceId of reverseRelations) {
        if (visited.has(sourceId)) continue;

        const sourceRelations = this.docGraph.get(sourceId) || new Set();
        for (const relation of sourceRelations) {
          if (relation.targetId === id && relation.weight >= minWeight) {
            const relatedDoc = {
              documentId: sourceId,
              relationType: relation.type,
              weight: relation.weight,
              confidence: relation.confidence,
              depth: depth + 1,
              path: [...path, id],
              context: relation.context,
              reverse: true
            };

            related.push(relatedDoc);
          }
        }
      }
    }

    // 按权重排序
    related.sort((a, b) => b.weight - a.weight);

    console.log(`🔍 [KnowledgeGraph] 找到 ${related.length} 个相关文档`);
    return related.slice(0, maxResults);
  }

  /**
   * 获取知识库关系
   * @param {string} knowledgeBaseId - 知识库ID
   */
  getKnowledgeBaseRelations(knowledgeBaseId) {
    const outgoing = Array.from(this.kbGraph.get(knowledgeBaseId) || []);
    const incoming = [];

    // 查找指向该知识库的关系
    for (const [sourceId, relations] of this.kbGraph) {
      for (const relation of relations) {
        if (relation.targetId === knowledgeBaseId) {
          incoming.push({
            sourceId,
            ...relation
          });
        }
      }
    }

    return {
      outgoing,
      incoming,
      total: outgoing.length + incoming.length
    };
  }

  /**
   * 分析知识图谱
   */
  analyzeGraph() {
    const analysis = {
      knowledgeBases: {
        total: this.kbGraph.size,
        connected: 0,
        isolated: 0,
        totalRelations: 0
      },
      documents: {
        total: this.docGraph.size,
        connected: 0,
        isolated: 0,
        totalRelations: 0
      },
      relationTypes: new Map(),
      centralNodes: [],
      clusters: []
    };

    // 分析知识库图
    for (const [kbId, relations] of this.kbGraph) {
      const relationCount = relations.size;
      analysis.knowledgeBases.totalRelations += relationCount;

      if (relationCount > 0) {
        analysis.knowledgeBases.connected++;
      } else {
        analysis.knowledgeBases.isolated++;
      }

      // 统计关系类型
      for (const relation of relations) {
        const count = analysis.relationTypes.get(relation.type) || 0;
        analysis.relationTypes.set(relation.type, count + 1);
      }
    }

    // 分析文档图
    for (const [docId, relations] of this.docGraph) {
      const relationCount = relations.size;
      analysis.documents.totalRelations += relationCount;

      if (relationCount > 0) {
        analysis.documents.connected++;
      } else {
        analysis.documents.isolated++;
      }
    }

    // 找出中心节点（连接度最高的节点）
    const nodeDegrees = new Map();
    
    for (const [nodeId, relations] of this.docGraph) {
      nodeDegrees.set(nodeId, relations.size);
    }

    analysis.centralNodes = Array.from(nodeDegrees.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([nodeId, degree]) => ({ nodeId, degree }));

    return analysis;
  }

  /**
   * 保存图谱数据
   */
  async saveGraphData() {
    try {
      const graphData = {
        kbGraph: this.serializeGraph(this.kbGraph),
        docGraph: this.serializeGraph(this.docGraph),
        conceptGraph: this.serializeGraph(this.conceptGraph),
        reverseIndex: this.serializeGraph(this.reverseIndex),
        metadata: {
          version: '1.0',
          savedAt: new Date().toISOString(),
          totalRelations: this.getTotalRelations()
        }
      };

      const graphFile = path.join(this.options.graphDir, 'knowledge-graph.json');
      await fs.writeFile(graphFile, JSON.stringify(graphData, null, 2));

      console.log('💾 [KnowledgeGraph] 图谱数据已保存');

    } catch (error) {
      console.error('❌ [KnowledgeGraph] 保存图谱数据失败:', error);
      throw error;
    }
  }

  /**
   * 加载图谱数据
   */
  async loadGraphData() {
    try {
      const graphFile = path.join(this.options.graphDir, 'knowledge-graph.json');
      
      try {
        const data = JSON.parse(await fs.readFile(graphFile, 'utf8'));
        
        this.kbGraph = this.deserializeGraph(data.kbGraph);
        this.docGraph = this.deserializeGraph(data.docGraph);
        this.conceptGraph = this.deserializeGraph(data.conceptGraph);
        this.reverseIndex = this.deserializeGraph(data.reverseIndex);

        console.log(`📚 [KnowledgeGraph] 图谱数据已加载: ${this.getTotalRelations()} 个关系`);

      } catch (error) {
        // 文件不存在或格式错误，使用空图谱
        console.log('📚 [KnowledgeGraph] 初始化空图谱');
      }

    } catch (error) {
      console.error('❌ [KnowledgeGraph] 加载图谱数据失败:', error);
    }
  }

  /**
   * 序列化图数据
   */
  serializeGraph(graph) {
    const serialized = {};
    
    for (const [key, value] of graph) {
      if (value instanceof Set) {
        serialized[key] = Array.from(value);
      } else {
        serialized[key] = value;
      }
    }

    return serialized;
  }

  /**
   * 反序列化图数据
   */
  deserializeGraph(serialized) {
    const graph = new Map();
    
    if (serialized) {
      for (const [key, value] of Object.entries(serialized)) {
        if (Array.isArray(value)) {
          graph.set(key, new Set(value));
        } else {
          graph.set(key, value);
        }
      }
    }

    return graph;
  }

  /**
   * 获取总关系数
   */
  getTotalRelations() {
    let total = 0;
    
    for (const relations of this.kbGraph.values()) {
      total += relations.size;
    }
    
    for (const relations of this.docGraph.values()) {
      total += relations.size;
    }

    return total;
  }

  /**
   * 清理资源
   */
  async cleanup() {
    if (this.options.autoSave) {
      await this.saveGraphData();
    }

    this.kbGraph.clear();
    this.docGraph.clear();
    this.conceptGraph.clear();
    this.reverseIndex.clear();
    this.initialized = false;

    console.log('🧹 [KnowledgeGraph] 知识图谱管理器已清理');
  }
}

module.exports = KnowledgeGraphManager;
