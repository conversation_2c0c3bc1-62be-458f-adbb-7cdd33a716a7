const fs = require('fs').promises;
const path = require('path');
const { EventEmitter } = require('events');

/**
 * 多媒体文档处理服务
 * 支持图片、PDF、Word、Excel等多种文件格式
 */
class DocumentProcessor extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      tempDir: options.tempDir || path.join(process.cwd(), 'temp'),
      maxFileSize: options.maxFileSize || 50 * 1024 * 1024, // 50MB
      ocrLanguages: options.ocrLanguages || ['chi_sim', 'eng'],
      imageFormats: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'],
      documentFormats: ['.pdf', '.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt'],
      ...options
    };

    this.processors = new Map();
    this.initialized = false;
  }

  /**
   * 初始化文档处理器
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // 创建临时目录
      await fs.mkdir(this.options.tempDir, { recursive: true });

      // 初始化各种处理器
      await this.initializeProcessors();

      this.initialized = true;
      console.log('✅ [DocumentProcessor] 文档处理器初始化完成');
      
    } catch (error) {
      console.error('❌ [DocumentProcessor] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化各种文档处理器
   */
  async initializeProcessors() {
    // 图片处理器（OCR）
    this.processors.set('image', new ImageProcessor(this.options));
    
    // PDF处理器
    this.processors.set('pdf', new PDFProcessor(this.options));
    
    // Word文档处理器
    this.processors.set('word', new WordProcessor(this.options));
    
    // Excel处理器
    this.processors.set('excel', new ExcelProcessor(this.options));
    
    // PowerPoint处理器
    this.processors.set('powerpoint', new PowerPointProcessor(this.options));

    // 初始化所有处理器
    for (const [type, processor] of this.processors) {
      try {
        if (processor.initialize) {
          await processor.initialize();
        }
        console.log(`✅ [DocumentProcessor] ${type} 处理器初始化完成`);
      } catch (error) {
        console.warn(`⚠️ [DocumentProcessor] ${type} 处理器初始化失败:`, error.message);
      }
    }
  }

  /**
   * 处理文档
   * @param {string} filePath - 文件路径
   * @param {Object} options - 处理选项
   */
  async processDocument(filePath, options = {}) {
    if (!this.initialized) await this.initialize();

    try {
      // 检查文件是否存在
      await fs.access(filePath);
      
      // 检查文件大小
      const stats = await fs.stat(filePath);
      if (stats.size > this.options.maxFileSize) {
        throw new Error(`文件过大: ${this.formatSize(stats.size)}, 最大支持: ${this.formatSize(this.options.maxFileSize)}`);
      }

      // 检测文件类型
      const fileType = this.detectFileType(filePath);
      const processor = this.processors.get(fileType);

      if (!processor) {
        throw new Error(`不支持的文件类型: ${path.extname(filePath)}`);
      }

      console.log(`🔄 [DocumentProcessor] 开始处理 ${fileType} 文件: ${path.basename(filePath)}`);

      // 处理文档
      const result = await processor.process(filePath, options);

      // 添加通用元数据
      result.metadata = {
        ...result.metadata,
        fileName: path.basename(filePath),
        fileSize: stats.size,
        fileType,
        processedAt: new Date().toISOString(),
        processingTime: result.processingTime || 0
      };

      console.log(`✅ [DocumentProcessor] 处理完成: ${result.text?.length || 0} 字符`);
      
      this.emit('documentProcessed', {
        filePath,
        fileType,
        result
      });

      return result;

    } catch (error) {
      console.error('❌ [DocumentProcessor] 处理失败:', error);
      
      this.emit('processingError', {
        filePath,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * 批量处理文档
   */
  async processDocuments(filePaths, options = {}) {
    const results = [];
    const errors = [];

    for (let i = 0; i < filePaths.length; i++) {
      const filePath = filePaths[i];
      
      try {
        this.emit('batchProgress', {
          current: i + 1,
          total: filePaths.length,
          currentFile: path.basename(filePath)
        });

        const result = await this.processDocument(filePath, options);
        results.push({
          filePath,
          success: true,
          result
        });

      } catch (error) {
        errors.push({
          filePath,
          success: false,
          error: error.message
        });
      }
    }

    return {
      results,
      errors,
      summary: {
        total: filePaths.length,
        successful: results.length,
        failed: errors.length
      }
    };
  }

  /**
   * 检测文件类型
   */
  detectFileType(filePath) {
    const ext = path.extname(filePath).toLowerCase();

    if (this.options.imageFormats.includes(ext)) {
      return 'image';
    }

    switch (ext) {
      case '.pdf':
        return 'pdf';
      case '.docx':
      case '.doc':
        return 'word';
      case '.xlsx':
      case '.xls':
        return 'excel';
      case '.pptx':
      case '.ppt':
        return 'powerpoint';
      default:
        throw new Error(`不支持的文件格式: ${ext}`);
    }
  }

  /**
   * 获取支持的文件格式
   */
  getSupportedFormats() {
    return {
      images: this.options.imageFormats,
      documents: this.options.documentFormats,
      all: [...this.options.imageFormats, ...this.options.documentFormats]
    };
  }

  /**
   * 格式化文件大小
   */
  formatSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 清理临时文件
   */
  async cleanup() {
    try {
      const tempFiles = await fs.readdir(this.options.tempDir);
      
      for (const file of tempFiles) {
        const filePath = path.join(this.options.tempDir, file);
        const stats = await fs.stat(filePath);
        
        // 删除1小时前的临时文件
        if (Date.now() - stats.mtime.getTime() > 60 * 60 * 1000) {
          await fs.unlink(filePath);
        }
      }
      
    } catch (error) {
      console.error('❌ [DocumentProcessor] 清理临时文件失败:', error);
    }
  }
}

/**
 * 图片处理器（OCR）
 */
class ImageProcessor {
  constructor(options) {
    this.options = options;
    this.tesseract = null;
  }

  async initialize() {
    try {
      // 动态导入 tesseract.js
      const Tesseract = await import('tesseract.js');
      this.tesseract = Tesseract;
      console.log('✅ [ImageProcessor] Tesseract.js 已加载');
    } catch (error) {
      console.warn('⚠️ [ImageProcessor] Tesseract.js 加载失败，OCR功能不可用');
    }
  }

  async process(imagePath, options = {}) {
    const startTime = Date.now();

    if (!this.tesseract) {
      throw new Error('OCR引擎未初始化');
    }

    try {
      const { data: { text, confidence } } = await this.tesseract.recognize(
        imagePath,
        this.options.ocrLanguages.join('+'),
        {
          logger: m => {
            if (m.status === 'recognizing text') {
              // 可以发送进度事件
            }
          }
        }
      );

      // 获取图片元数据
      const metadata = await this.getImageMetadata(imagePath);

      return {
        text: text.trim(),
        metadata: {
          ...metadata,
          ocrConfidence: confidence,
          ocrLanguages: this.options.ocrLanguages
        },
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      throw new Error(`OCR处理失败: ${error.message}`);
    }
  }

  async getImageMetadata(imagePath) {
    try {
      // 尝试使用 sharp 获取图片信息
      const sharp = await import('sharp');
      const metadata = await sharp.default(imagePath).metadata();
      
      return {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        channels: metadata.channels,
        density: metadata.density
      };
    } catch (error) {
      // 如果 sharp 不可用，返回基本信息
      const stats = await fs.stat(imagePath);
      return {
        size: stats.size
      };
    }
  }
}

/**
 * PDF处理器
 */
class PDFProcessor {
  constructor(options) {
    this.options = options;
    this.pdfParse = null;
  }

  async initialize() {
    try {
      // 动态导入 pdf-parse
      this.pdfParse = await import('pdf-parse');
      console.log('✅ [PDFProcessor] pdf-parse 已加载');
    } catch (error) {
      console.warn('⚠️ [PDFProcessor] pdf-parse 加载失败');
    }
  }

  async process(pdfPath, options = {}) {
    const startTime = Date.now();

    if (!this.pdfParse) {
      throw new Error('PDF解析器未初始化');
    }

    try {
      const dataBuffer = await fs.readFile(pdfPath);
      const data = await this.pdfParse.default(dataBuffer);

      return {
        text: data.text,
        metadata: {
          pages: data.numpages,
          info: data.info,
          version: data.version
        },
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      throw new Error(`PDF处理失败: ${error.message}`);
    }
  }
}

/**
 * Word文档处理器
 */
class WordProcessor {
  constructor(options) {
    this.options = options;
    this.mammoth = null;
  }

  async initialize() {
    try {
      // 动态导入 mammoth
      this.mammoth = await import('mammoth');
      console.log('✅ [WordProcessor] mammoth 已加载');
    } catch (error) {
      console.warn('⚠️ [WordProcessor] mammoth 加载失败');
    }
  }

  async process(docPath, options = {}) {
    const startTime = Date.now();

    if (!this.mammoth) {
      throw new Error('Word文档解析器未初始化');
    }

    try {
      const result = await this.mammoth.default.extractRawText({ path: docPath });

      return {
        text: result.value,
        metadata: {
          messages: result.messages,
          warnings: result.messages.filter(m => m.type === 'warning').length
        },
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      throw new Error(`Word文档处理失败: ${error.message}`);
    }
  }
}

/**
 * Excel处理器
 */
class ExcelProcessor {
  constructor(options) {
    this.options = options;
    this.xlsx = null;
  }

  async initialize() {
    try {
      // 动态导入 xlsx
      this.xlsx = await import('xlsx');
      console.log('✅ [ExcelProcessor] xlsx 已加载');
    } catch (error) {
      console.warn('⚠️ [ExcelProcessor] xlsx 加载失败');
    }
  }

  async process(excelPath, options = {}) {
    const startTime = Date.now();

    if (!this.xlsx) {
      throw new Error('Excel解析器未初始化');
    }

    try {
      const workbook = this.xlsx.default.readFile(excelPath);
      const texts = [];
      const sheetData = {};

      workbook.SheetNames.forEach(sheetName => {
        const worksheet = workbook.Sheets[sheetName];
        const csv = this.xlsx.default.utils.sheet_to_csv(worksheet);
        const json = this.xlsx.default.utils.sheet_to_json(worksheet);
        
        texts.push(`工作表: ${sheetName}\n${csv}`);
        sheetData[sheetName] = {
          csv,
          rowCount: json.length,
          range: worksheet['!ref']
        };
      });

      return {
        text: texts.join('\n\n'),
        metadata: {
          sheetCount: workbook.SheetNames.length,
          sheetNames: workbook.SheetNames,
          sheetData
        },
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      throw new Error(`Excel处理失败: ${error.message}`);
    }
  }
}

/**
 * PowerPoint处理器
 */
class PowerPointProcessor {
  constructor(options) {
    this.options = options;
  }

  async initialize() {
    // PowerPoint处理器暂时使用基础实现
    console.log('✅ [PowerPointProcessor] 基础处理器已初始化');
  }

  async process(pptPath, options = {}) {
    const startTime = Date.now();

    // 基础实现：返回文件信息
    const stats = await fs.stat(pptPath);
    
    return {
      text: `PowerPoint文件: ${path.basename(pptPath)}\n文件大小: ${this.formatSize(stats.size)}`,
      metadata: {
        fileSize: stats.size,
        note: 'PowerPoint文本提取功能正在开发中'
      },
      processingTime: Date.now() - startTime
    };
  }

  formatSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }
}

module.exports = DocumentProcessor;
