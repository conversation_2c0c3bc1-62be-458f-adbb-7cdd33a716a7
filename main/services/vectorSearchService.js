const { Worker } = require('worker_threads');
const path = require('path');
const EventEmitter = require('events');

/**
 * 高性能向量搜索服务
 * 使用Worker Threads池进行并行计算
 */
class VectorSearchService extends EventEmitter {
  constructor(options = {}) {
    super();
    this.maxWorkers = options.maxWorkers || require('os').cpus().length;
    this.workers = [];
    this.taskQueue = [];
    this.busyWorkers = new Set();
    this.initialized = false;
    
    // 性能统计
    this.stats = {
      totalSearches: 0,
      avgSearchTime: 0,
      cacheHits: 0,
      workerUtilization: 0
    };
  }

  /**
   * 初始化Worker线程池
   */
  async initialize() {
    if (this.initialized) return;

    console.log(`🚀 [VectorSearch] 初始化 ${this.maxWorkers} 个Worker线程`);
    
    for (let i = 0; i < this.maxWorkers; i++) {
      const worker = new Worker(path.join(__dirname, 'vectorWorker.js'));
      
      worker.on('message', (result) => {
        this.handleWorkerMessage(worker, result);
      });
      
      worker.on('error', (error) => {
        console.error(`❌ [VectorSearch] Worker ${i} 错误:`, error);
        this.restartWorker(i);
      });
      
      worker.on('exit', (code) => {
        if (code !== 0) {
          console.error(`❌ [VectorSearch] Worker ${i} 异常退出: ${code}`);
          this.restartWorker(i);
        }
      });
      
      this.workers.push({
        worker,
        id: i,
        busy: false,
        currentTask: null
      });
    }
    
    this.initialized = true;
    console.log('✅ [VectorSearch] Worker线程池初始化完成');
  }

  /**
   * 高性能向量搜索
   * @param {Array} queryVector - 查询向量
   * @param {Array} vectors - 向量数据库
   * @param {Object} options - 搜索选项
   */
  async search(queryVector, vectors, options = {}) {
    const startTime = Date.now();
    
    if (!this.initialized) {
      await this.initialize();
    }

    const {
      topK = 5,
      threshold = 0.5,
      knowledgeBaseId = null,
      useCache = true,
      chunkSize = 1000 // 每个Worker处理的向量数量
    } = options;

    // 🚀 缓存检查
    const cacheKey = this.generateCacheKey(queryVector, options);
    if (useCache) {
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        this.stats.cacheHits++;
        console.log('🎯 [VectorSearch] 缓存命中');
        return cached;
      }
    }

    // 过滤向量数据
    const filteredVectors = knowledgeBaseId 
      ? vectors.filter(v => v.metadata?.knowledgeBaseId === knowledgeBaseId)
      : vectors;

    if (filteredVectors.length === 0) {
      return [];
    }

    // 🚀 分块并行处理
    const chunks = this.chunkArray(filteredVectors, chunkSize);
    const searchPromises = chunks.map(chunk => 
      this.searchChunk(queryVector, chunk, { topK, threshold })
    );

    try {
      const chunkResults = await Promise.all(searchPromises);
      
      // 合并结果并排序
      const allResults = chunkResults.flat();
      allResults.sort((a, b) => b.similarity - a.similarity);
      const finalResults = allResults.slice(0, topK);

      // 缓存结果
      if (useCache) {
        await this.saveToCache(cacheKey, finalResults);
      }

      // 更新统计
      const searchTime = Date.now() - startTime;
      this.updateStats(searchTime);

      console.log(`✅ [VectorSearch] 搜索完成: ${finalResults.length}个结果, 耗时${searchTime}ms`);
      return finalResults;

    } catch (error) {
      console.error('❌ [VectorSearch] 搜索失败:', error);
      throw error;
    }
  }

  /**
   * 使用Worker搜索向量块
   */
  async searchChunk(queryVector, vectorChunk, options) {
    return new Promise((resolve, reject) => {
      const task = {
        id: Date.now() + Math.random(),
        queryVector,
        vectors: vectorChunk,
        options,
        resolve,
        reject,
        timestamp: Date.now()
      };

      // 寻找空闲Worker
      const availableWorker = this.workers.find(w => !w.busy);
      
      if (availableWorker) {
        this.assignTask(availableWorker, task);
      } else {
        // 加入队列等待
        this.taskQueue.push(task);
      }
    });
  }

  /**
   * 分配任务给Worker
   */
  assignTask(workerInfo, task) {
    workerInfo.busy = true;
    workerInfo.currentTask = task;
    this.busyWorkers.add(workerInfo.id);

    workerInfo.worker.postMessage({
      taskId: task.id,
      queryVector: task.queryVector,
      vectors: task.vectors,
      options: task.options
    });
  }

  /**
   * 处理Worker返回的消息
   */
  handleWorkerMessage(worker, message) {
    const workerInfo = this.workers.find(w => w.worker === worker);
    if (!workerInfo || !workerInfo.currentTask) return;

    const task = workerInfo.currentTask;
    
    if (message.error) {
      task.reject(new Error(message.error));
    } else {
      task.resolve(message.results);
    }

    // 释放Worker
    workerInfo.busy = false;
    workerInfo.currentTask = null;
    this.busyWorkers.delete(workerInfo.id);

    // 处理队列中的下一个任务
    if (this.taskQueue.length > 0) {
      const nextTask = this.taskQueue.shift();
      this.assignTask(workerInfo, nextTask);
    }
  }

  /**
   * 重启Worker
   */
  restartWorker(workerId) {
    const workerInfo = this.workers[workerId];
    if (workerInfo) {
      workerInfo.worker.terminate();
      
      const newWorker = new Worker(path.join(__dirname, 'vectorWorker.js'));
      newWorker.on('message', (result) => {
        this.handleWorkerMessage(newWorker, result);
      });
      
      workerInfo.worker = newWorker;
      workerInfo.busy = false;
      workerInfo.currentTask = null;
    }
  }

  /**
   * 数组分块
   */
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(queryVector, options) {
    const vectorHash = this.hashVector(queryVector);
    const optionsHash = this.hashObject(options);
    return `search_${vectorHash}_${optionsHash}`;
  }

  /**
   * 向量哈希
   */
  hashVector(vector) {
    const crypto = require('crypto');
    const str = vector.slice(0, 10).join(','); // 使用前10个元素生成哈希
    return crypto.createHash('md5').update(str).digest('hex').substring(0, 8);
  }

  /**
   * 对象哈希
   */
  hashObject(obj) {
    const crypto = require('crypto');
    const str = JSON.stringify(obj);
    return crypto.createHash('md5').update(str).digest('hex').substring(0, 8);
  }

  /**
   * 缓存操作（简单内存缓存）
   */
  async getFromCache(key) {
    if (!this.cache) this.cache = new Map();
    return this.cache.get(key);
  }

  async saveToCache(key, data) {
    if (!this.cache) this.cache = new Map();
    
    // LRU缓存策略
    if (this.cache.size > 1000) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, data);
  }

  /**
   * 更新性能统计
   */
  updateStats(searchTime) {
    this.stats.totalSearches++;
    this.stats.avgSearchTime = (this.stats.avgSearchTime * (this.stats.totalSearches - 1) + searchTime) / this.stats.totalSearches;
    this.stats.workerUtilization = (this.busyWorkers.size / this.maxWorkers) * 100;
  }

  /**
   * 获取性能统计
   */
  getStats() {
    return { ...this.stats };
  }

  /**
   * 清理资源
   */
  async cleanup() {
    console.log('🧹 [VectorSearch] 清理Worker线程池');
    
    for (const workerInfo of this.workers) {
      await workerInfo.worker.terminate();
    }
    
    this.workers = [];
    this.taskQueue = [];
    this.busyWorkers.clear();
    this.initialized = false;
  }
}

module.exports = VectorSearchService;
