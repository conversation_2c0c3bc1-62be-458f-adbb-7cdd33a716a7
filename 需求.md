# JDCAIChat 产品需求文档 (PRD)

**文档版本**：1.2  
**日期**：2025-07-11  
**作者**：Chenmx（u53）  
**项目名称**：JDCAIChat  
**项目描述**：一个本地化桌面AI模型协作工具，使用Electron.js构建，支持Mac和Windows平台。用户可以配置和管理多个OpenAI兼容模型，进行动态聊天室协作或多模型并行回答，支持文件/图片上传和预设角色提示词。强调本地隐私、无需登录、灵活协作。

---

## 1. 产品概述
### 1.1 产品定位
JDCAIChat 是一个桌面应用，旨在帮助程序员、AI研究者和开发者本地管理和协作多个AI模型。核心是模拟“AI团队”协作：模型可以随机/动态顺序互动、互相响应，而非固定顺序。应用完全本地化，无需云端登录，数据存储在本地文件系统中。

### 1.2 目标用户
- 主要用户：程序员、AI爱好者、研究者，需要测试多模型协作、隐私敏感的用户。
- 使用场景：本地调试AI提示、模型比较、协作解决问题（如代码debug、脑暴想法）。

### 1.3 核心价值
- **灵活配置**：自定义多个OpenAI兼容模型，支持thinking（思考链）和vision（视觉分析）。
- **协作模块**：聊天室（手动邀请模型，动态响应）；多模型并行回答。
- **增强功能**：预设“搭档”角色（提示词模板）；文件/图片上传（支持Ctrl+V粘贴）。
- **本地化**：跨平台（Mac/Windows），数据持久化使用JSON文件，无SQLite依赖。
- **用户体验**：简单、直观，避免信息过载；手动控制模型参与。

### 1.4 MVP 范围
- 核心功能：模型配置、聊天室、多模型回答、搭档设置。
- 排除：高级特性如模型性能监控、自动建议邀请（可作为迭代）。

---

## 2. 功能规格
应用分为四个主要模块。所有模块支持新建/保存/加载会话，会话数据本地存储。

### 2.1 模型配置模块
- **描述**：用户添加和管理模型配置组，每个组下可添加多个模型。
- **功能点**：
  - 添加/编辑/删除配置组和模型。
  - 模型字段：名称、baseURL、API Key、支持thinking（布尔）、支持vision（布尔）、上传类型（文件/图片）。
  - 测试API连通性。
  - 搜索/排序模型。
- **输入/输出**：表单输入；列表展示。

### 2.2 模型聊天室模块
- **描述**：用户手动邀请模型加入聊天室，进行动态协作回答问题。模型响应非固定顺序，可互相引用/协作。
- **功能点**：
  - 新建会话：输入问题，选择初始模型（手动从配置中挑选，非自动全部添加）。
  - 动态邀请：会话中添加更多模型（从未邀列表选择），新增模型接收历史上下文并首次响应。
  - 响应机制：随机顺序（shuffle）或中控模式（选一模型分析并决定顺序）；支持thinking和vision。
  - 协作：模型响应可引用他人输出；循环响应直到用户停止。
  - 移除模型：从会话中踢出。
  - 系统消息：记录邀请/移除事件。
  - 上传：Ctrl+V粘贴文件/图片，注入prompt。
- **输入/输出**：文本输入；消息流展示（带模型标签、thinking过程）。

### 2.3 多模型回答模块
- **描述**：选择多个模型，对同一问题并行回答，并排展示结果。
- **功能点**：
  - 选择模型（多选），输入问题。
  - 并行调用API，展示每个模型的响应、thinking、耗时。
  - 支持重新提问、导出结果。
  - 上传：同聊天室，支持文件/图片。
- **输入/输出**：问题输入；Tabs/Card展示结果。

### 2.4 搭档（预设角色）模块
- **描述**：预设提示词模板，作为系统prompt注入会话，提升模型智能。
- **功能点**：
  - 添加/编辑/删除搭档角色（名称 + 提示词）。
  - 在新建聊天室/多模型会话时选择搭档，自动应用到所有模型。
- **输入/输出**：列表管理；下拉选择。

### 2.5 通用功能
- **会话管理**：新建/保存/加载/删除会话；自动保存变化。
- **上传支持**：拖拽或Ctrl+V（图片/文件），自动base64编码注入prompt。
- **搜索/过滤**：全局搜索模型/会话。
- **设置**：应用主题、语言（默认中文）、API限流。

---

## 3. 数据模型
数据使用JSON结构，本地文件存储。优化说明：baseURL和API Key为配置组级别（组内所有模型共享），以简化管理（如同一服务器多个模型）。每个模型仅定义特定属性（如支持thinking）。示例：

### 3.1 模型配置
```json
{
  "configs": [
    {
      "id": "config1",
      "name": "Local Group",
      "baseUrl": "http://localhost:8000/v1",
      "apiKey": "sk-xxx",
      "models": [
        {
          "id": "modelA",
          "name": "GPT-4-like",
          "supportThinking": true,
          "supportVision": true,
          "uploadTypes": ["file", "image"]
        }
      ]
    }
  ]
}
```

### 3.2 会话数据（聊天室示例）
```json
{
  "sessionId": "chatroom1",
  "type": "chatroom",
  "invitedModels": ["modelA", "modelB"],
  "coordinator": "modelA",
  "partnerId": "partner1",  // 可选搭档
  "messages": [
    { "sender": "system", "content": "邀请了 modelB", "timestamp": "2025-07-11T10:00" },
    { "sender": "user", "content": "解决问题X", "timestamp": "..." },
    { "sender": "modelA", "content": "回答...", "thinking": "...", "timestamp": "..." }
  ]
}
```

### 3.3 搭档数据
```json
{
  "partners": [
    { "id": "partner1", "name": "代码专家", "prompt": "你是一个资深程序员..." }
  ]
}
```

---

## 4. UI/UX 设计
- **整体布局**：Electron主窗口，左侧侧边栏导航（配置、聊天室、多模型、搭档），右侧主内容区。
- **组件库**：Antd（Table for lists, Chat for messages, Modal for forms, Upload for files）。
- **交互原则**：
  - 简洁：拖拽排序、搜索过滤。
  - 反馈：Loading spinner for API calls；警告 for 不支持功能（e.g., 无vision模型上传图片）。
  - 跨平台：适应Mac/Windows快捷键（e.g., Command+V / Ctrl+V）。
- **关键页面**：
  - 配置：Table + Modal。
  - 聊天室：左侧邀请列表 + 右侧消息流 + 工具栏（邀请按钮）。
  - 多模型：Checkbox选择 + Tabs结果。
  - 搭档：简单列表 + 编辑表单。
- **视觉风格**：Antd默认主题，暗模式支持。

---

## 5. 技术栈和实现要求
- **框架**：Electron.js (桌面壳子，跨平台打包)。
- **前端**：React (推荐，hooks管理状态) 或 Vue；Antd for UI。
- **后端/集成**：
  - OpenAI SDK (自定义baseURL/Key)。
  - 库：axios (API)、clipboardy (Ctrl+V)、electron-store (可选，辅助存储)。
  - IPC：主进程处理文件I/O和API，避免UI阻塞。
- **开发要点**：
  - 状态管理：React useState/useEffect for lists and messages。
  - API调用：异步，带重试/错误处理；stream模式 for thinking。
  - 随机逻辑：Math.random() shuffle for 非固定顺序。
  - 邀请实现：更新invitedModels数组，注入历史prompt。

---

## 6. 持久化方案
- **存储方式**：JSON文件，非SQLite（保持轻量）。
- **路径**：`${app.getPath('userData')}/jdcaichat/` (e.g., Mac: ~/Library/Application Support/jdcaichat/)。
- **文件结构**：
  - configs.json
  - partners.json
  - sessions/ (e.g., chatroom1.json)
- **操作**：启动加载；变化时debounce保存；支持导出/导入。
- **备份**：自动或手动导出文件夹。

---

## 7. 非功能需求
- **跨平台**：支持Mac (darwin) 和Windows；测试路径、快捷键兼容。
- **性能**：API限流（e.g., 并发不超过5）；响应时间<5s。
- **安全性**：本地加密API Key；无网络依赖（模型API除外）。
- **可用性**：中英文支持；错误提示友好。
- **可访问性**：Antd组件默认支持。

---

## 8. 潜在风险和迭代计划
### 8.1 风险
- API不稳定：加重试机制。
- 随机顺序不可预测：加日志和用户控制选项。
- 性能瓶颈：多模型并发时，优化队列。