# JDCAIChat - Git忽略文件配置

# ===== Node.js 相关 =====
# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov
coverage/
*.lcov
.nyc_output

# 依赖锁定文件（可选择性忽略）
# package-lock.json
# yarn.lock

# ===== React 相关 =====
# 构建输出
build/
dist/
out/

# 开发服务器
.cache/
.parcel-cache/

# 测试覆盖率
coverage/

# Storybook 构建输出
storybook-static/

# ===== Electron 相关 =====
# Electron 构建输出
app-builds/
release-builds/
releases/

# Electron 打包文件
*.dmg
*.pkg
*.deb
*.rpm
*.exe
*.msi
*.zip
*.tar.gz

# Electron 开发
electron-builder.env

# ===== 环境配置 =====
# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 配置文件
config.json
config.local.json
.config/

# ===== 日志文件 =====
# 应用日志
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 系统日志
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ===== IDE 和编辑器 =====
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# WebStorm
.idea/
*.swp
*.swo

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*~
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== 操作系统 =====
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== 临时文件 =====
# 备份文件
*.bak
*.backup
*.tmp
*.temp

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# ===== 应用特定 =====
# JDCAIChat 数据文件
data/
user-data/
sessions/
backups/

# 用户配置
user-config.json
user-settings.json

# 缓存文件
.cache/
cache/
tmp/

# 性能监控数据
performance-logs/
*.perf

# 调试文件
debug.log
error.log

# ===== 安全相关 =====
# API密钥和敏感信息
.env.secret
secrets.json
api-keys.json
*.pem
*.key
*.crt
*.p12

# 证书文件
certificates/

# ===== 其他 =====
# 测试文件
test-results/
screenshots/
videos/

# 文档生成
docs-build/
.docusaurus/

# 依赖分析
bundle-analyzer-report.html
stats.json

# 性能分析
*.cpuprofile
*.heapprofile

# 本地开发
local/
dev/
sandbox/
