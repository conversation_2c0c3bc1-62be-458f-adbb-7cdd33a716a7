/**
 * 🚀 性能数据管理器测试脚本
 * 用于验证性能数据收集和统计功能
 */

// 模拟浏览器环境
global.localStorage = {
  data: {},
  getItem: function(key) {
    return this.data[key] || null;
  },
  setItem: function(key, value) {
    this.data[key] = value;
  },
  removeItem: function(key) {
    delete this.data[key];
  }
};

// 导入性能数据管理器
const { PerformanceDataManager } = require('./src/utils/performanceDataManager.js');

async function testPerformanceManager() {
  console.log('🚀 开始测试性能数据管理器...\n');

  // 创建实例
  const manager = new PerformanceDataManager();
  
  // 初始化
  await manager.initialize();
  console.log('✅ 初始化完成\n');

  // 测试API调用记录
  console.log('📝 测试API调用记录...');
  
  // 模拟多个API调用
  const testCalls = [
    { model: 'gpt-4', sessionId: 'session-1', success: true, duration: 1500, tokens: { prompt: 100, completion: 200, total: 300 } },
    { model: 'gpt-4', sessionId: 'session-1', success: true, duration: 2000, tokens: { prompt: 150, completion: 250, total: 400 } },
    { model: 'claude-3', sessionId: 'session-1', success: true, duration: 1200, tokens: { prompt: 80, completion: 180, total: 260 } },
    { model: 'gpt-4', sessionId: 'session-2', success: false, duration: 5000, tokens: null },
    { model: 'claude-3', sessionId: 'session-2', success: true, duration: 1800, tokens: { prompt: 120, completion: 220, total: 340 } }
  ];

  for (let i = 0; i < testCalls.length; i++) {
    const call = testCalls[i];
    
    // 记录开始
    const callId = manager.recordAPICallStart(call.model, call.sessionId, {
      messageCount: 5,
      maxTokens: 2000,
      temperature: 0.7,
      stream: false,
      estimatedTokens: call.tokens?.prompt || 100
    });

    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 10));

    // 记录结束
    const responseData = call.success ? {
      choices: [{ finish_reason: 'stop' }],
      usage: call.tokens
    } : {};

    manager.recordAPICallEnd(
      callId,
      call.success,
      responseData,
      call.success ? null : 'API request failed'
    );

    console.log(`  ✅ 记录API调用 ${i + 1}: ${call.model} (${call.success ? '成功' : '失败'})`);
  }

  console.log('\n📊 测试统计功能...');

  // 测试模型性能统计
  const gpt4Stats = manager.getModelPerformanceStats('gpt-4', 'all');
  console.log('GPT-4 统计:', {
    总请求数: gpt4Stats.totalRequests,
    成功率: `${gpt4Stats.successRate.toFixed(1)}%`,
    平均响应时间: `${gpt4Stats.avgResponseTime}ms`,
    总Token数: gpt4Stats.totalTokens,
    状态: gpt4Stats.status
  });

  const claude3Stats = manager.getModelPerformanceStats('claude-3', 'all');
  console.log('Claude-3 统计:', {
    总请求数: claude3Stats.totalRequests,
    成功率: `${claude3Stats.successRate.toFixed(1)}%`,
    平均响应时间: `${claude3Stats.avgResponseTime}ms`,
    总Token数: claude3Stats.totalTokens,
    状态: claude3Stats.status
  });

  // 测试全局统计
  const globalStats = manager.getGlobalStats('all');
  console.log('\n全局统计:', {
    总请求数: globalStats.totalRequests,
    成功请求数: globalStats.successfulRequests,
    失败请求数: globalStats.failedRequests,
    成功率: `${globalStats.successRate.toFixed(1)}%`,
    平均响应时间: `${globalStats.avgResponseTime}ms`,
    总Token数: globalStats.totalTokens,
    活跃模型数: globalStats.activeModels
  });

  // 测试所有模型统计
  const allModelStats = manager.getAllModelStats('all');
  console.log('\n所有模型统计:');
  Object.entries(allModelStats).forEach(([modelName, stats]) => {
    console.log(`  ${modelName}: ${stats.totalRequests}次请求, ${stats.successRate.toFixed(1)}%成功率`);
  });

  // 测试数据持久化
  console.log('\n💾 测试数据持久化...');
  await manager.saveToStorage();
  console.log('✅ 数据保存完成');

  // 创建新实例测试加载
  const manager2 = new PerformanceDataManager();
  await manager2.initialize();
  
  const loadedGlobalStats = manager2.getGlobalStats('all');
  console.log('加载的全局统计:', {
    总请求数: loadedGlobalStats.totalRequests,
    成功率: `${loadedGlobalStats.successRate.toFixed(1)}%`
  });

  console.log('\n🎉 所有测试完成！');
}

// 运行测试
testPerformanceManager().catch(console.error);
