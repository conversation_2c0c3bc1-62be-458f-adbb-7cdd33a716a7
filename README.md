# JDCAIChat - 本地化桌面AI模型协作工具

JDCAIChat 是一个基于 Electron.js 的本地化桌面 AI 模型协作工具，支持 Mac 和 Windows 平台。该应用允许用户配置和管理多个 OpenAI 兼容模型，进行动态聊天室协作或多模型并行回答，并集成了完整的向量知识库系统，强调本地隐私、无需登录、灵活协作。

## 🚀 核心功能

### 1. AI模型配置管理
- **用户自定义配置**：所有AI模型均通过用户配置添加，系统不预设任何默认模型
- **配置验证**：在模型添加时验证API连通性和兼容性
- **分组管理**：支持配置组管理，每个组可包含多个模型
- **API测试**：一键测试模型连接状态

### 2. 聊天室协作机制
- **中控模型调度**：使用指定的中控模型来分析对话上下文并决定下一个发言的模型
- **动态邀请**：可随时邀请或移除模型参与对话
- **智能调度**：中控模型根据对话内容、模型特长、上下文相关性来选择最适合回答的模型
- **调度透明度**：在界面中显示中控模型的调度决策过程

### 3. 防死循环保护机制
- **回合数限制**：设置最大连续AI对话回合数为12轮（可在设置中调整8-20轮范围）
- **内容重复检测**：使用文本相似度算法检测连续消息的重复模式
- **循环模式识别**：检测A→B→A或A→B→C→A的循环发言模式
- **用户控制**：提供"停止对话"和"继续对话"按钮，用户可随时控制对话流程

### 4. 多模型并行回答
- **并行处理**：同时向多个模型发送相同问题
- **结果对比**：并排展示不同模型的回答结果
- **性能统计**：显示各模型的响应时间和成功率
- **结果导出**：支持导出对比结果为JSON格式

### 5. 向量知识库系统
- **知识库管理**：创建、编辑、删除知识库，支持多种文档类型
- **多媒体文档处理**：支持PDF、Word、Excel、图片等多种格式的智能文本提取
- **OCR文字识别**：使用Tesseract.js对图片进行OCR文字识别
- **智能分块**：使用AI模型进行内容感知的智能分块，保持结构完整性
- **多内容类型支持**：SQL数据库、代码片段、API文档、技术文档、业务流程、配置文件等
- **高性能向量搜索**：使用Worker Threads并行计算，5-10倍性能提升
- **智能缓存系统**：多层缓存机制，减少重复计算
- **向量化存储**：使用优化的向量数据库进行高效存储和相似度搜索
- **知识库集成**：AI模型可基于知识库内容回答问题，提供准确的上下文信息
- **引用显示**：显示AI回答的知识来源，包括文档标题、相似度分数和原文链接
- **分层分块**：支持父子分块结构，提供更精细的搜索粒度
- **进度监控**：实时显示文档向量化进度和处理状态
- **版本管理**：文档版本控制，支持版本比较和恢复
- **知识图谱**：可视化知识库间的关联关系

### 6. 搭档角色系统
- **预设角色**：内置多种专业角色模板（代码专家、产品经理、技术架构师等）
- **自定义角色**：用户可创建自定义的角色提示词
- **角色应用**：在聊天室和多模型回答中应用角色设定

## 🛠️ 技术架构

### 技术栈
- **桌面框架**：Electron.js
- **前端框架**：React 18
- **UI组件库**：Ant Design 5
- **状态管理**：React Hooks
- **API调用**：Axios + OpenAI SDK
- **数据存储**：本地JSON文件
- **向量数据库**：优化的向量数据库 + Worker Threads并行计算
- **向量化服务**：支持多种Embedding模型
- **智能分块**：基于AI模型的内容感知分块
- **文档处理**：PDF.js、Mammoth.js、SheetJS、Tesseract.js
- **性能优化**：智能缓存、主进程服务、优雅降级

### 核心模块
- **DataManager**：数据管理和持久化
- **APIManager**：API调用控制和错误处理
- **LoopProtectionManager**：防死循环保护机制
- **CoordinatorManager**：中控模型调度系统
- **KnowledgeBaseManager**：知识库管理和向量化处理
- **OptimizedVectorDatabase**：高性能向量数据库，支持Worker Threads并行计算
- **IntelligentChunker**：智能分块服务，支持多种内容类型
- **DocumentProcessorManager**：多媒体文档处理器，支持PDF、Word、Excel、图片等
- **SmartCache**：智能缓存系统，多层缓存优化
- **PerformanceMonitor**：性能监控和统计
- **DocumentVersionManager**：文档版本管理
- **KnowledgeGraphVisualization**：知识图谱可视化
- **KnowledgeBaseIntegration**：知识库与聊天系统集成

## 📦 安装和运行

### 环境要求
- Node.js 16+
- npm 或 yarn
- Python 3.8+ (用于向量化服务，可选)
- 支持的操作系统：macOS 10.14+, Windows 10+

### 安装依赖
```bash
npm install
```

### 开发模式运行
```bash
# 启动React开发服务器
npm start

# 在另一个终端启动Electron
npx electron .
```

### 打包应用
```bash
# 构建生产版本
npm run build

# 打包为桌面应用
npm run electron-pack
```

## 🎯 使用指南

### 1. 配置AI模型
1. 点击左侧导航的"模型配置"
2. 添加配置组，输入Base URL和API Key
3. 在配置组下添加具体的模型
4. 测试模型连接状态

### 2. 设置搭档角色
1. 点击"搭档设置"
2. 选择预设角色或创建自定义角色
3. 编写详细的角色提示词

### 3. 使用聊天室
1. 点击"模型聊天室"
2. 邀请需要参与的模型
3. 选择中控模型（可选）
4. 选择搭档角色（可选）
5. 创建新会话并开始对话

### 4. 多模型对比
1. 点击"多模型回答"
2. 选择要对比的模型
3. 输入问题并发送
4. 查看各模型的回答结果

### 5. 使用向量知识库
1. 点击"向量知识库"
2. 创建新知识库，配置Embedding模型
3. 添加文档：
   - **手动输入**：直接输入或粘贴文本内容
   - **文件上传**：支持PDF、Word、Excel、图片等多种格式
4. 选择分块方式（传统/智能）
5. 等待文档向量化完成
6. 在聊天时选择相关知识库
7. AI将基于知识库内容提供更准确的回答，并显示引用来源

### 6. 智能分块配置
- **传统分块**：按固定大小和重叠度分割文档
- **智能分块**：使用AI模型分析文档结构，保持内容完整性
- **分层分块**：创建父子分块结构，提供多层次搜索
- **内容类型自适应**：自动识别SQL、代码、API文档等不同类型

## ⚙️ 高级设置

### 防死循环设置
- **最大回合数**：8-20轮可调
- **相似度阈值**：内容重复检测的敏感度
- **显示思考过程**：是否显示模型的thinking内容

### API控制
- **请求超时**：30秒（知识库相关操作为3分钟）
- **并发限制**：最多5个并发请求
- **自动重试**：网络错误时自动重试

### 知识库设置
- **向量搜索阈值**：根据内容类型自动调整（0.3-0.7）
- **分块大小**：可配置分块大小和重叠度
- **智能分块模型**：可选择用于智能分块的Chat模型
- **搜索策略**：多层次搜索，支持降级和回退机制

## 🌟 特色功能

### 🚀 高性能优化系统
- **Worker Threads并行计算**：向量搜索性能提升5-10倍
- **智能缓存机制**：多层缓存减少重复计算，提升响应速度
- **优雅降级**：主进程服务失败时自动回退到本地计算
- **实时性能监控**：监控搜索时间、缓存命中率等关键指标

### 📄 多媒体文档处理
- **PDF文档**：使用PDF.js提取文本内容，支持多页文档
- **Word文档**：使用Mammoth.js处理.docx格式文档
- **Excel表格**：使用SheetJS处理.xlsx/.xls格式，支持多工作表
- **图片OCR**：使用Tesseract.js进行中英文OCR文字识别
- **文本文件**：支持.txt、.md、.csv、.json等多种文本格式
- **前端处理**：所有文档处理均在浏览器中完成，保护数据隐私

### 🧠 智能内容识别
- **自动内容类型检测**：智能识别SQL、代码、API文档、技术文档、业务流程等
- **内容感知分块**：根据内容类型采用最适合的分块策略
- **结构保持**：保持代码块、表格、SQL语句等结构化内容的完整性

### 🔍 多层次搜索
- **自适应阈值**：根据查询类型自动调整搜索阈值
- **查询预处理**：智能提取查询中的关键信息并增强搜索词
- **降级搜索**：多轮搜索策略确保找到相关内容
- **并行搜索**：利用Worker Threads实现高效并行搜索

### 📚 知识库引用
- **来源追踪**：显示AI回答的具体知识来源
- **相似度评分**：展示搜索结果的相关性分数
- **原文链接**：可直接查看引用的原始文档内容
- **引用卡片**：美观的引用展示界面，支持展开/折叠

### 📝 版本管理与协作
- **文档版本控制**：自动保存文档版本，支持版本比较和恢复
- **知识图谱可视化**：展示知识库间的关联关系
- **开发者调试工具**：实时日志监控、性能数据可视化、功能测试工具

## 🔒 隐私和安全

- **本地存储**：所有数据存储在本地，不上传到云端
- **API密钥加密**：本地加密存储API密钥
- **无需登录**：完全本地化运行，无需注册账号
- **数据控制**：用户完全控制自己的数据
- **向量数据本地化**：向量数据库文件存储在本地，保护知识产权

## 🐛 故障排除

### 常见问题
1. **模型连接失败**：检查Base URL和API Key是否正确
2. **Electron启动失败**：确保React开发服务器已启动
3. **依赖安装失败**：尝试使用国内镜像源
4. **向量化失败**：检查Embedding模型配置和API连接
5. **智能分块无响应**：确保Chat模型配置正确且有足够的token限制
6. **知识库搜索无结果**：检查搜索阈值设置和文档向量化状态
7. **文档处理失败**：
   - PDF处理失败：检查PDF文件是否损坏或受密码保护
   - Word文档处理失败：确保是.docx格式（不支持.doc）
   - 图片OCR失败：检查网络连接，OCR引擎需要在线加载
   - Excel处理失败：确保文件格式正确且未损坏
8. **性能问题**：
   - 搜索速度慢：检查是否启用了优化功能
   - 内存占用高：清理缓存或重启应用
   - 文档处理慢：大文件处理需要更多时间，请耐心等待

### 错误恢复
- 应用提供完整的错误处理和恢复机制
- 网络中断时会自动重试
- API失败时提供明确的错误信息和解决建议

## 📝 开发说明

### 项目结构
```
src/
├── components/          # React组件
│   ├── ChatRoom.js     # 聊天室组件
│   ├── ModelConfig.js  # 模型配置组件
│   ├── MultiModelAnswer.js # 多模型回答组件
│   ├── PartnerConfig.js # 搭档配置组件
│   ├── KnowledgeBase.js # 向量知识库管理组件
│   └── KnowledgeReferences.js # 知识库引用显示组件
├── utils/              # 工具类
│   ├── dataManager.js  # 数据管理
│   ├── apiManager.js   # API管理
│   ├── loopProtection.js # 循环保护
│   ├── coordinatorManager.js # 调度管理
│   ├── knowledgeBaseManager.js # 知识库管理
│   ├── optimizedVectorDatabase.js # 优化向量数据库
│   ├── optimizedFeatures.js # 优化功能集成
│   ├── documentProcessors.js # 多媒体文档处理器
│   ├── embeddingService.js # 向量化服务
│   ├── intelligentChunker.js # 智能分块
│   └── knowledgeBaseIntegration.js # 知识库集成
├── App.js              # 主应用组件
└── index.js            # 应用入口
```

### 贡献指南
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 支持

如有问题或建议，请提交 Issue 或联系开发团队。
