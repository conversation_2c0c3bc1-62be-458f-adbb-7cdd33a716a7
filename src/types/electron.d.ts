/**
 * Electron API类型声明
 */

interface ElectronWebSearchAPI {
  search: (searchUrl: string, options?: {
    timeout?: number;
    maxRedirects?: number;
    headers?: Record<string, string>;
  }) => Promise<{
    statusCode: number;
    headers: Record<string, string>;
    data: string;
    finalUrl?: string;
    redirectCount?: number;
  }>;
  
  extract: (url: string, options?: {
    timeout?: number;
  }) => Promise<{
    statusCode: number;
    headers: Record<string, string>;
    data: string;
    url: string;
  }>;
}

interface ElectronStoreAPI {
  get: (key: string) => Promise<any>;
  set: (key: string, value: any) => Promise<void>;
  delete: (key: string) => Promise<void>;
  clear: () => Promise<void>;
}

interface ElectronAPI {
  getAppPath: () => Promise<string>;
  showSaveDialog: (options: any) => Promise<any>;
  showOpenDialog: (options: any) => Promise<any>;
  store: ElectronStoreAPI;
  webSearch: ElectronWebSearchAPI;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};
