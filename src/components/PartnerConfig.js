import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Button,
  Modal,
  Form,
  Input,
  Space,
  message,
  Popconfirm,
  Empty,
  Typography
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined
} from '@ant-design/icons';

const { TextArea } = Input;
const { Text, Paragraph } = Typography;

const PartnerConfig = ({ dataManager }) => {
  const [partners, setPartners] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPartner, setEditingPartner] = useState(null);
  
  const [form] = Form.useForm();

  // 加载搭档数据
  useEffect(() => {
    loadPartners();
  }, [dataManager]); // loadPartners 在组件内部定义，不需要添加到依赖

  const loadPartners = () => {
    if (dataManager) {
      setPartners(dataManager.getPartners());
    }
  };

  // 添加/编辑搭档
  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      
      if (editingPartner) {
        dataManager.updatePartner(editingPartner.id, values);
        message.success('搭档更新成功');
      } else {
        dataManager.addPartner(values);
        message.success('搭档添加成功');
      }
      
      loadPartners();
      setModalVisible(false);
      setEditingPartner(null);
      form.resetFields();
    } catch (error) {
      message.error('操作失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 编辑搭档
  const editPartner = (partner) => {
    setEditingPartner(partner);
    form.setFieldsValue(partner);
    setModalVisible(true);
  };

  // 删除搭档
  const deletePartner = (partnerId) => {
    try {
      dataManager.deletePartner(partnerId);
      // 🚀 修复：强制刷新搭档数据
      const updatedPartners = dataManager.getPartners();
      setPartners([...updatedPartners]); // 创建新数组引用以触发重新渲染
      message.success('搭档删除成功');
    } catch (error) {
      message.error('删除失败: ' + error.message);
    }
  };

  // 预设搭档模板
  const presetPartners = [
    {
      name: '代码专家',
      prompt: '你是一个资深的软件工程师，擅长多种编程语言和技术栈。请用专业、准确的方式回答编程相关问题，提供最佳实践建议，并在必要时给出代码示例。'
    },
    {
      name: '产品经理',
      prompt: '你是一个经验丰富的产品经理，擅长需求分析、产品设计和用户体验优化。请从产品角度思考问题，关注用户价值和商业目标。'
    },
    {
      name: '技术架构师',
      prompt: '你是一个技术架构师，专注于系统设计、技术选型和架构优化。请从技术架构的角度分析问题，考虑可扩展性、性能和维护性。'
    },
    {
      name: '数据分析师',
      prompt: '你是一个数据分析专家，擅长数据挖掘、统计分析和可视化。请用数据驱动的方式思考问题，提供基于数据的洞察和建议。'
    },
    {
      name: '创意顾问',
      prompt: '你是一个创意顾问，擅长头脑风暴、创新思维和解决方案设计。请用创造性的方式思考问题，提供多样化的想法和建议。'
    }
  ];

  // 使用预设模板
  const applyPreset = (preset) => {
    form.setFieldsValue(preset);
  };

  return (
    <div style={{ padding: 24, height: '100%', overflow: 'auto' }}>
      <Card
        title="搭档角色管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingPartner(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            添加搭档
          </Button>
        }
      >
        {partners.length === 0 ? (
          <Empty
            description="暂无搭档角色"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingPartner(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              添加第一个搭档
            </Button>
          </Empty>
        ) : (
          <List
            itemLayout="vertical"
            dataSource={partners}
            renderItem={(partner) => (
              <List.Item
                key={partner.id}
                actions={[
                  <Button
                    type="link"
                    icon={<EditOutlined />}
                    onClick={() => editPartner(partner)}
                  >
                    编辑
                  </Button>,
                  <Popconfirm
                    title="确定删除此搭档吗？"
                    onConfirm={() => deletePartner(partner.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      type="link"
                      danger
                      icon={<DeleteOutlined />}
                    >
                      删除
                    </Button>
                  </Popconfirm>
                ]}
              >
                <List.Item.Meta
                  avatar={<UserOutlined style={{ fontSize: 24, color: '#1890ff' }} />}
                  title={<Text strong>{partner.name}</Text>}
                  description={
                    <Paragraph
                      ellipsis={{ rows: 3, expandable: true, symbol: '展开' }}
                      style={{ marginBottom: 0 }}
                    >
                      {partner.prompt}
                    </Paragraph>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </Card>

      {/* 搭档编辑模态框 */}
      <Modal
        title={editingPartner ? '编辑搭档' : '添加搭档'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingPartner(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="搭档名称"
            rules={[{ required: true, message: '请输入搭档名称' }]}
          >
            <Input placeholder="例如：代码专家" />
          </Form.Item>
          
          <Form.Item
            name="prompt"
            label="角色提示词"
            rules={[
              { required: true, message: '请输入角色提示词' },
              { min: 10, message: '提示词至少需要10个字符' }
            ]}
          >
            <TextArea
              rows={8}
              placeholder="请输入详细的角色描述和行为指导..."
              showCount
              maxLength={2000}
            />
          </Form.Item>

          {/* 预设模板 */}
          {!editingPartner && (
            <Form.Item label="快速模板">
              <div style={{ marginBottom: 16 }}>
                <Text type="secondary">选择预设模板快速创建搭档角色：</Text>
              </div>
              <Space wrap>
                {presetPartners.map((preset, index) => (
                  <Button
                    key={index}
                    size="small"
                    onClick={() => applyPreset(preset)}
                  >
                    {preset.name}
                  </Button>
                ))}
              </Space>
            </Form.Item>
          )}
          
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingPartner ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PartnerConfig;
