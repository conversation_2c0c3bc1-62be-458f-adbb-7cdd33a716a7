import React from 'react';
import { Result, Button, Typography, Card, Space, Alert } from 'antd';
import { 
  BugOutlined, 
  ReloadOutlined, 
  HomeOutlined,
  CopyOutlined 
} from '@ant-design/icons';

const { Paragraph, Text } = Typography;

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { 
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    // 记录错误信息
    this.setState({
      error,
      errorInfo
    });

    // 记录错误到控制台
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // 这里可以将错误信息发送到错误报告服务
    this.logErrorToService(error, errorInfo);
  }

  logErrorToService = (error, errorInfo) => {
    // 构建错误报告
    const errorReport = {
      id: this.state.errorId,
      timestamp: new Date().toISOString(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      errorInfo: {
        componentStack: errorInfo.componentStack
      },
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: 'anonymous', // 如果有用户系统可以添加用户ID
      appVersion: process.env.REACT_APP_VERSION || '1.0.0'
    };

    // 存储到本地存储以便调试
    try {
      const existingErrors = JSON.parse(localStorage.getItem('jdc_error_logs') || '[]');
      existingErrors.push(errorReport);
      
      // 只保留最近的10个错误
      if (existingErrors.length > 10) {
        existingErrors.splice(0, existingErrors.length - 10);
      }
      
      localStorage.setItem('jdc_error_logs', JSON.stringify(existingErrors));
    } catch (e) {
      console.warn('无法保存错误日志到本地存储:', e);
    }

    // 在开发环境下显示详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.group('🐛 错误详情');
      console.error('错误ID:', errorReport.id);
      console.error('错误对象:', error);
      console.error('错误信息:', errorInfo);
      console.error('完整报告:', errorReport);
      console.groupEnd();
    }
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  handleCopyError = () => {
    const errorText = `
错误ID: ${this.state.errorId}
时间: ${new Date().toLocaleString()}
错误: ${this.state.error?.message}
堆栈: ${this.state.error?.stack}
组件堆栈: ${this.state.errorInfo?.componentStack}
    `.trim();

    navigator.clipboard.writeText(errorText).then(() => {
      // 这里可以显示复制成功的提示
      console.log('错误信息已复制到剪贴板');
    }).catch(err => {
      console.error('复制失败:', err);
    });
  };

  render() {
    if (this.state.hasError) {
      const isDevelopment = process.env.NODE_ENV === 'development';
      
      return (
        <div style={{ 
          padding: 24, 
          minHeight: '100vh', 
          background: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Card style={{ maxWidth: 800, width: '100%' }}>
            <Result
              status="error"
              icon={<BugOutlined style={{ color: '#ff4d4f' }} />}
              title="应用遇到了一个错误"
              subTitle={`错误ID: ${this.state.errorId} | 我们为此错误感到抱歉，请尝试刷新页面或返回首页。`}
              extra={[
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />}
                  onClick={this.handleReload}
                  key="reload"
                >
                  刷新页面
                </Button>,
                <Button 
                  icon={<HomeOutlined />}
                  onClick={this.handleGoHome}
                  key="home"
                >
                  返回首页
                </Button>,
                <Button 
                  icon={<CopyOutlined />}
                  onClick={this.handleCopyError}
                  key="copy"
                >
                  复制错误信息
                </Button>
              ]}
            />

            {/* 用户友好的错误说明 */}
            <Alert
              message="可能的解决方案"
              description={
                <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
                  <li>刷新页面重新加载应用</li>
                  <li>检查网络连接是否正常</li>
                  <li>清除浏览器缓存和数据</li>
                  <li>如果问题持续存在，请联系技术支持</li>
                </ul>
              }
              type="info"
              showIcon
              style={{ marginTop: 16 }}
            />

            {/* 开发环境下显示详细错误信息 */}
            {isDevelopment && this.state.error && (
              <Card 
                title="开发者信息" 
                size="small" 
                style={{ marginTop: 16 }}
                type="inner"
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>错误消息:</Text>
                    <Paragraph code copyable style={{ marginTop: 8 }}>
                      {this.state.error.message}
                    </Paragraph>
                  </div>
                  
                  <div>
                    <Text strong>错误堆栈:</Text>
                    <Paragraph 
                      code 
                      copyable 
                      style={{ 
                        marginTop: 8,
                        maxHeight: 200,
                        overflow: 'auto',
                        fontSize: 12
                      }}
                    >
                      {this.state.error.stack}
                    </Paragraph>
                  </div>
                  
                  {this.state.errorInfo?.componentStack && (
                    <div>
                      <Text strong>组件堆栈:</Text>
                      <Paragraph 
                        code 
                        copyable 
                        style={{ 
                          marginTop: 8,
                          maxHeight: 200,
                          overflow: 'auto',
                          fontSize: 12
                        }}
                      >
                        {this.state.errorInfo.componentStack}
                      </Paragraph>
                    </div>
                  )}
                </Space>
              </Card>
            )}
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
