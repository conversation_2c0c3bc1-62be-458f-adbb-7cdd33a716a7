import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Drawer,
  List,
  Typography,
  Button,
  Input,
  Space,
  Tag,
  Tooltip,
  Popconfirm,
  message,
  Empty,
  Card,
  Row,
  Col,
  Switch,
  Divider,
  Badge,
  Avatar
} from 'antd';
import {
  CopyOutlined,
  DeleteOutlined,
  SearchOutlined,
  ClearOutlined,
  ClockCircleOutlined,
  CodeOutlined,
  LinkOutlined,
  FileTextOutlined,
  PictureOutlined,
  MailOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;
const { Search } = Input;

/**
 * 🚀 智能剪贴板管理器组件
 * 监听系统剪贴板，自动分类管理复制的内容
 */
const SmartClipboardManager = ({ 
  visible, 
  onClose, 
  onInsertContent,
  style = {} 
}) => {
  const [clipboardHistory, setClipboardHistory] = useState([]);
  const [filteredHistory, setFilteredHistory] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(false);
  const monitoringRef = useRef(false);

  // 内容类别配置
  const categories = {
    all: { label: '全部', icon: <FileTextOutlined />, color: '#1890ff' },
    text: { label: '文本', icon: <FileTextOutlined />, color: '#52c41a' },
    code: { label: '代码', icon: <CodeOutlined />, color: '#722ed1' },
    url: { label: '链接', icon: <LinkOutlined />, color: '#13c2c2' },
    image: { label: '图片', icon: <PictureOutlined />, color: '#fa8c16' },
    json: { label: 'JSON', icon: <CodeOutlined />, color: '#eb2f96' },
    email: { label: '邮箱', icon: <MailOutlined />, color: '#f5222d' }
  };

  // 初始化剪贴板监听
  useEffect(() => {
    if (visible) {
      loadClipboardHistory();
      setupClipboardListener();
    }
    
    return () => {
      if (window.electronAPI?.clipboard) {
        window.electronAPI.clipboard.removeContentListener();
      }
    };
  }, [visible]);

  // 🎯 修复：监听状态变化时自动启动/停止监听
  useEffect(() => {
    const startMonitoring = async () => {
      if (isMonitoring && visible) {
        setLoading(true);
        try {
          await window.electronAPI?.clipboard?.startMonitoring();
          console.log('📋 [剪贴板管理器] 监听已启动');
        } catch (error) {
          console.error('❌ [剪贴板管理器] 启动监听失败:', error);
        } finally {
          setLoading(false);
        }
      } else if (!isMonitoring) {
        try {
          await window.electronAPI?.clipboard?.stopMonitoring();
          console.log('📋 [剪贴板管理器] 监听已停止');
        } catch (error) {
          console.error('❌ [剪贴板管理器] 停止监听失败:', error);
        }
      }
    };

    if (visible) {
      startMonitoring();
    }
  }, [isMonitoring, visible]);

  // 🎯 修复：监听主进程的监听启动通知，同步状态
  useEffect(() => {
    if (window.electronAPI?.clipboard?.onMonitoringStarted) {
      window.electronAPI.clipboard.onMonitoringStarted(() => {
        console.log('📋 [剪贴板管理器] 收到主进程启动通知，同步状态');
        setIsMonitoring(true);
      });

      return () => {
        window.electronAPI?.clipboard?.removeMonitoringListener();
      };
    }
  }, []);

  // 搜索过滤
  useEffect(() => {
    filterHistory();
  }, [clipboardHistory, searchText, selectedCategory]);

  // 加载剪贴板历史
  const loadClipboardHistory = async () => {
    try {
      if (!window.electronAPI?.clipboard) {
        console.warn('⚠️ [剪贴板管理器] Electron API 不可用');
        message.warning('剪贴板功能需要在Electron环境中运行');
        return;
      }

      console.log('📋 [剪贴板管理器] 正在加载历史记录...');
      const history = await window.electronAPI.clipboard.getHistory();
      console.log('📋 [剪贴板管理器] 加载到历史记录:', history?.length || 0, '条');
      setClipboardHistory(history || []);
    } catch (error) {
      console.error('❌ [剪贴板管理器] 加载历史失败:', error);
      message.error('加载剪贴板历史失败: ' + error.message);
    }
  };

  // 设置剪贴板监听器
  const setupClipboardListener = () => {
    if (!window.electronAPI?.clipboard) {
      console.warn('⚠️ [剪贴板管理器] 无法设置监听器：Electron API 不可用');
      return;
    }

    console.log('📋 [剪贴板管理器] 设置剪贴板监听器...');

    // 监听新内容添加
    window.electronAPI.clipboard.onContentAdded((newItem) => {
      console.log('📋 [剪贴板管理器] 收到新内容:', newItem);
      setClipboardHistory(prev => {
        // 检查是否已存在
        const existingIndex = prev.findIndex(item => item.content === newItem.content);
        if (existingIndex !== -1) {
          // 移除旧的，添加新的到开头
          const updated = [...prev];
          updated.splice(existingIndex, 1);
          return [newItem, ...updated];
        }
        return [newItem, ...prev];
      });

      message.success(`已记录${categories[newItem.category]?.label || '内容'}: ${newItem.preview}`);
    });
  };

  // 开始/停止监听
  const toggleMonitoring = async () => {
    if (!window.electronAPI?.clipboard) {
      message.error('剪贴板功能不可用');
      return;
    }

    // 🎯 修复：只需要切换状态，实际的启动/停止由useEffect自动处理
    const newState = !isMonitoring;
    setIsMonitoring(newState);
    monitoringRef.current = newState;
    
    // 显示用户友好的消息
    if (newState) {
      message.success('开始监听系统剪贴板');
    } else {
      message.info('已停止监听系统剪贴板');
    }
  };

  // 过滤历史记录
  const filterHistory = () => {
    let filtered = clipboardHistory;

    // 按类别过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    // 按搜索文本过滤
    if (searchText.trim()) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(item => 
        item.content.toLowerCase().includes(searchLower) ||
        item.preview.toLowerCase().includes(searchLower)
      );
    }

    setFilteredHistory(filtered);
  };

  // 复制内容到剪贴板
  const copyToClipboard = async (item) => {
    try {
      if (!window.electronAPI?.clipboard) {
        // 降级到浏览器API
        await navigator.clipboard.writeText(item.content);
        message.success('已复制到剪贴板');
        return;
      }

      const result = await window.electronAPI.clipboard.copyContent(item.content, item.type);
      if (result.success) {
        message.success('已复制到剪贴板');
      } else {
        throw new Error('复制失败');
      }
    } catch (error) {
      console.error('❌ [剪贴板管理器] 复制失败:', error);
      message.error('复制失败: ' + error.message);
    }
  };

  // 插入内容到当前对话
  const insertContent = (item) => {
    if (onInsertContent) {
      onInsertContent(item.content);
      message.success('已插入到对话框');
    }
  };

  // 删除单个项目
  const deleteItem = async (itemId) => {
    try {
      if (window.electronAPI?.clipboard) {
        await window.electronAPI.clipboard.deleteItem(itemId);
      }
      
      setClipboardHistory(prev => prev.filter(item => item.id !== itemId));
      message.success('已删除');
    } catch (error) {
      console.error('❌ [剪贴板管理器] 删除失败:', error);
      message.error('删除失败');
    }
  };

  // 清空历史记录
  const clearHistory = async () => {
    try {
      if (window.electronAPI?.clipboard) {
        await window.electronAPI.clipboard.clearHistory();
      }
      
      setClipboardHistory([]);
      message.success('历史记录已清空');
    } catch (error) {
      console.error('❌ [剪贴板管理器] 清空失败:', error);
      message.error('清空失败');
    }
  };

  // 格式化时间
  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    return date.toLocaleDateString();
  };

  // 渲染内容预览
  const renderContentPreview = (item) => {
    if (item.type === 'image') {
      return (
        <div style={{ textAlign: 'center', padding: '8px' }}>
          <img 
            src={item.content} 
            alt="剪贴板图片"
            style={{ 
              maxWidth: '100%', 
              maxHeight: '100px',
              borderRadius: '4px',
              border: '1px solid rgba(0, 212, 255, 0.2)'
            }}
          />
        </div>
      );
    }

    return (
      <Paragraph 
        ellipsis={{ rows: 3, expandable: false }}
        style={{ 
          margin: 0, 
          fontSize: '13px',
          color: 'rgba(255, 255, 255, 0.85)'
        }}
      >
        {item.preview}
      </Paragraph>
    );
  };

  return (
    <Drawer
      title={
        <Space>
          <Badge
            count={clipboardHistory.length}
            style={{ backgroundColor: '#00d4ff' }}
          >
            📋 智能剪贴板
          </Badge>
          <Switch
            checked={isMonitoring}
            loading={loading}
            onChange={toggleMonitoring}
            checkedChildren={<PlayCircleOutlined />}
            unCheckedChildren={<PauseCircleOutlined />}
          />
        </Space>
      }
      placement="right"
      width={400}
      open={visible}
      onClose={onClose}
      closable={true}
      maskClosable={false}
      style={{
        background: 'linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0a0e1a 100%)',
        ...style
      }}
      styles={{
        header: {
          background: 'rgba(26, 31, 46, 0.95)',
          borderBottom: '1px solid rgba(0, 212, 255, 0.2)',
          color: '#fff'
        },
        body: {
          background: 'transparent',
          padding: '16px'
        }
      }}
    >
      {/* 搜索和过滤 */}
      <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
        <Search
          placeholder="搜索剪贴板内容..."
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: '100%' }}
          allowClear
        />
        
        {/* 类别过滤 */}
        <Row gutter={[8, 8]}>
          {Object.entries(categories).map(([key, config]) => (
            <Col key={key}>
              <Tag
                icon={config.icon}
                color={selectedCategory === key ? config.color : 'default'}
                style={{ 
                  cursor: 'pointer',
                  border: selectedCategory === key ? `1px solid ${config.color}` : '1px solid rgba(255,255,255,0.2)'
                }}
                onClick={() => setSelectedCategory(key)}
              >
                {config.label}
              </Tag>
            </Col>
          ))}
        </Row>

        {/* 操作按钮 */}
        <Row gutter={8}>
          <Col flex="auto">
            <Button 
              icon={<SettingOutlined />} 
              size="small"
              style={{ 
                background: 'rgba(0, 212, 255, 0.1)',
                border: '1px solid rgba(0, 212, 255, 0.3)',
                color: '#00d4ff'
              }}
            >
              设置
            </Button>
          </Col>
          <Col>
            <Popconfirm
              title="确定要清空所有历史记录吗？"
              onConfirm={clearHistory}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                icon={<ClearOutlined />} 
                size="small"
                danger
              >
                清空
              </Button>
            </Popconfirm>
          </Col>
        </Row>
      </Space>

      <Divider style={{ borderColor: 'rgba(0, 212, 255, 0.2)', margin: '16px 0' }} />

      {/* 剪贴板历史列表 */}
      {filteredHistory.length === 0 ? (
        <Empty
          description={
            <Text style={{ color: 'rgba(255, 255, 255, 0.6)' }}>
              {clipboardHistory.length === 0 ? '暂无剪贴板记录' : '没有匹配的内容'}
            </Text>
          }
          style={{ marginTop: 40 }}
        />
      ) : (
        <List
          dataSource={filteredHistory}
          renderItem={(item) => (
            <List.Item style={{ padding: '8px 0', border: 'none' }}>
              <Card
                size="small"
                style={{
                  width: '100%',
                  background: 'rgba(26, 31, 46, 0.6)',
                  border: '1px solid rgba(0, 212, 255, 0.2)',
                  borderRadius: '8px'
                }}
                bodyStyle={{ padding: '12px' }}
              >
                <div style={{ marginBottom: 8 }}>
                  <Space>
                    <Avatar 
                      size="small" 
                      icon={categories[item.category]?.icon}
                      style={{ 
                        backgroundColor: categories[item.category]?.color,
                        fontSize: '12px'
                      }}
                    />
                    <Tag 
                      color={categories[item.category]?.color}
                      style={{ fontSize: '11px', margin: 0 }}
                    >
                      {categories[item.category]?.label}
                    </Tag>
                    <Text 
                      style={{ 
                        fontSize: '11px', 
                        color: 'rgba(255, 255, 255, 0.6)' 
                      }}
                    >
                      <ClockCircleOutlined style={{ marginRight: 4 }} />
                      {formatTime(item.timestamp)}
                    </Text>
                  </Space>
                </div>

                {renderContentPreview(item)}

                <div style={{ marginTop: 8, textAlign: 'right' }}>
                  <Space size="small">
                    <Tooltip title="复制到剪贴板">
                      <Button
                        type="text"
                        size="small"
                        icon={<CopyOutlined />}
                        onClick={() => copyToClipboard(item)}
                        style={{ color: '#00d4ff' }}
                      />
                    </Tooltip>
                    {onInsertContent && (
                      <Tooltip title="插入到对话">
                        <Button
                          type="text"
                          size="small"
                          icon={<FileTextOutlined />}
                          onClick={() => insertContent(item)}
                          style={{ color: '#52c41a' }}
                        />
                      </Tooltip>
                    )}
                    <Tooltip title="删除">
                      <Popconfirm
                        title="确定删除这个项目吗？"
                        onConfirm={() => deleteItem(item.id)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button
                          type="text"
                          size="small"
                          icon={<DeleteOutlined />}
                          style={{ color: '#ff4d4f' }}
                        />
                      </Popconfirm>
                    </Tooltip>
                  </Space>
                </div>
              </Card>
            </List.Item>
          )}
        />
      )}
    </Drawer>
  );
};

export default SmartClipboardManager;
