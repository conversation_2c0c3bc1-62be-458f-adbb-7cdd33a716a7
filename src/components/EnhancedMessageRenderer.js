import React, { memo, useMemo } from 'react';
import MarkdownRenderer from './MarkdownRenderer';
import ThinkingSection from './ThinkingSection';
import { parseThinkTags } from '../utils/copyUtils';

// 🚀 增强的消息内容渲染器 - 支持思考标签解析和显示
const EnhancedMessageRenderer = memo(({ 
  content, 
  showThinking = true,
  defaultThinkingExpanded = false,
  className = '',
  style = {}
}) => {
  // 🎯 解析消息内容中的思考标签
  const parsedContent = useMemo(() => {
    if (!content || typeof content !== 'string') {
      return { content: content || '', thinkingSections: [] };
    }
    
    return parseThinkTags(content);
  }, [content]);

  // 🎯 渲染处理后的内容段落
  const renderContentSegments = useMemo(() => {
    const { content: processedContent, thinkingSections } = parsedContent;
    
    if (!thinkingSections || thinkingSections.length === 0) {
      // 没有思考标签，直接渲染原内容
      return (
        <MarkdownRenderer 
          content={processedContent} 
          className={className}
        />
      );
    }

    // 有思考标签，需要分段渲染
    const segments = [];
    let currentContent = processedContent;
    
    // 按思考段落分割内容
    thinkingSections.forEach((section, index) => {
      const placeholder = `[THINKING_PLACEHOLDER_${section.id}]`;
      const parts = currentContent.split(placeholder);
      
      if (parts.length >= 2) {
        // 添加思考段落前的内容
        if (parts[0].trim()) {
          segments.push({
            type: 'content',
            content: parts[0],
            key: `content_before_${index}`
          });
        }
        
        // 添加思考段落
        if (showThinking) {
          segments.push({
            type: 'thinking',
            content: section.content,
            sectionId: section.id,
            key: `thinking_${index}`
          });
        }
        
        // 更新剩余内容
        currentContent = parts.slice(1).join(placeholder);
      }
    });
    
    // 添加最后剩余的内容
    if (currentContent.trim()) {
      segments.push({
        type: 'content',
        content: currentContent,
        key: 'content_final'
      });
    }
    
    return segments;
  }, [parsedContent, showThinking, className]);

  // 🎯 如果没有内容，返回空
  if (!content) {
    return null;
  }

  // 🎯 如果解析结果是简单内容，直接渲染
  if (!Array.isArray(renderContentSegments)) {
    return (
      <div style={style}>
        {renderContentSegments}
      </div>
    );
  }

  // 🎯 渲染分段内容
  return (
    <div style={style}>
      {renderContentSegments.map((segment) => {
        if (segment.type === 'content') {
          return (
            <MarkdownRenderer
              key={segment.key}
              content={segment.content}
              className={className}
            />
          );
        } else if (segment.type === 'thinking') {
          return (
            <ThinkingSection
              key={segment.key}
              content={segment.content}
              sectionId={segment.sectionId}
              defaultExpanded={defaultThinkingExpanded}
              showCopyButton={true}
            />
          );
        }
        return null;
      })}
    </div>
  );
});

EnhancedMessageRenderer.displayName = 'EnhancedMessageRenderer';

export default EnhancedMessageRenderer;
