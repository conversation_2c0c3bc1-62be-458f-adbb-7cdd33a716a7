import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Switch,
  Select,
  Space,
  message,
  Popconfirm,
  Tag
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ApiOutlined
} from '@ant-design/icons';
import { APIManager } from '../utils/apiManager';

const { Option } = Select;

const ModelConfig = ({ dataManager }) => {
  const [configs, setConfigs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [modelModalVisible, setModelModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [editingModel, setEditingModel] = useState(null);
  const [selectedConfig, setSelectedConfig] = useState(null);
  const [testingModels, setTestingModels] = useState(new Set());
  
  const [form] = Form.useForm();
  const [modelForm] = Form.useForm();
  const apiManager = new APIManager();

  // 加载配置数据
  useEffect(() => {
    loadConfigs();
  }, [dataManager]); // loadConfigs 在组件内部定义，不需要添加到依赖

  const loadConfigs = () => {
    if (dataManager) {
      setConfigs(dataManager.getConfigs());
    }
  };

  // 配置组表格列定义
  const configColumns = [
    {
      title: '配置组名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Base URL',
      dataIndex: 'baseUrl',
      key: 'baseUrl',
      ellipsis: true,
    },
    {
      title: 'API Key',
      dataIndex: 'apiKey',
      key: 'apiKey',
      render: (text) => text ? `${text.substring(0, 8)}...` : '',
    },
    {
      title: '模型数量',
      key: 'modelCount',
      render: (_, record) => record.models?.length || 0,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => editConfig(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除此配置组吗？"
            onConfirm={() => deleteConfig(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 模型表格列定义
  const modelColumns = [
    {
      title: '模型名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '模型类型',
      dataIndex: 'type',
      key: 'type',
      render: (value) => {
        const type = value || 'chat'; // 默认为chat类型
        return type === 'chat' ?
          <Tag color="blue">对话模型</Tag> :
          <Tag color="orange">向量嵌入</Tag>;
      },
    },
    {
      title: '支持Thinking',
      dataIndex: 'supportThinking',
      key: 'supportThinking',
      render: (value) => value ? <Tag color="green">是</Tag> : <Tag>否</Tag>,
    },
    {
      title: '支持Vision',
      dataIndex: 'supportVision',
      key: 'supportVision',
      render: (value) => value ? <Tag color="blue">是</Tag> : <Tag>否</Tag>,
    },
    {
      title: '支持联网',
      dataIndex: 'supportWebSearch',
      key: 'supportWebSearch',
      render: (value) => value ? <Tag color="purple">是</Tag> : <Tag>否</Tag>,
    },
    {
      title: '上传类型',
      dataIndex: 'uploadTypes',
      key: 'uploadTypes',
      render: (types) => (
        <Space>
          {types?.map(type => (
            <Tag key={type} color="orange">{type}</Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '连接状态',
      key: 'status',
      render: (_, record) => (
        <Button
          size="small"
          icon={<ApiOutlined />}
          loading={testingModels.has(record.id)}
          onClick={() => testModel(record)}
        >
          测试连接
        </Button>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => editModel(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除此模型吗？"
            onConfirm={() => deleteModel(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 添加/编辑配置组
  const handleConfigSubmit = async (values) => {
    try {
      setLoading(true);
      
      if (editingConfig) {
        dataManager.updateConfig(editingConfig.id, values);
        message.success('配置组更新成功');
      } else {
        dataManager.addConfig(values);
        message.success('配置组添加成功');
      }
      
      loadConfigs();
      setModalVisible(false);
      setEditingConfig(null);
      form.resetFields();
    } catch (error) {
      message.error('操作失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 添加/编辑模型
  const handleModelSubmit = async (values) => {
    try {
      setLoading(true);
      
      if (editingModel) {
        dataManager.updateModel(selectedConfig.id, editingModel.id, values);
        message.success('模型更新成功');
      } else {
        dataManager.addModel(selectedConfig.id, values);
        message.success('模型添加成功');
      }
      
      loadConfigs();
      setModelModalVisible(false);
      setEditingModel(null);
      modelForm.resetFields();
    } catch (error) {
      message.error('操作失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 编辑配置组
  const editConfig = (config) => {
    setEditingConfig(config);
    form.setFieldsValue(config);
    setModalVisible(true);
  };

  // 删除配置组
  const deleteConfig = (configId) => {
    try {
      dataManager.deleteConfig(configId);
      // 🚀 修复：强制刷新配置数据
      const updatedConfigs = dataManager.getConfigs();
      setConfigs([...updatedConfigs]); // 创建新数组引用以触发重新渲染
      message.success('配置组删除成功');
    } catch (error) {
      message.error('删除失败: ' + error.message);
    }
  };

  // 编辑模型
  const editModel = (model) => {
    setEditingModel(model);
    // 为没有类型的旧模型设置默认类型
    const modelWithType = {
      ...model,
      type: model.type || 'chat' // 默认为chat类型
    };
    modelForm.setFieldsValue(modelWithType);
    setModelModalVisible(true);
  };

  // 删除模型
  const deleteModel = (modelId) => {
    try {
      dataManager.deleteModel(selectedConfig.id, modelId);
      // 🚀 修复：强制刷新配置数据
      const updatedConfigs = dataManager.getConfigs();
      setConfigs([...updatedConfigs]); // 创建新数组引用以触发重新渲染
      message.success('模型删除成功');
    } catch (error) {
      message.error('删除失败: ' + error.message);
    }
  };

  // 测试模型连接
  const testModel = async (model) => {
    const fullModel = {
      ...model,
      baseUrl: selectedConfig.baseUrl,
      apiKey: selectedConfig.apiKey
    };

    setTestingModels(prev => new Set([...prev, model.id]));
    
    try {
      const result = await apiManager.testModelConnection(fullModel);
      
      if (result.success) {
        message.success(`${model.name} 连接成功`);
      } else {
        message.error(`${model.name} 连接失败: ${result.message}`);
      }
    } catch (error) {
      message.error(`测试失败: ${error.message}`);
    } finally {
      setTestingModels(prev => {
        const newSet = new Set(prev);
        newSet.delete(model.id);
        return newSet;
      });
    }
  };

  return (
    <div style={{ padding: 24, height: '100%', overflow: 'auto' }}>
      <Card
        title="模型配置管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingConfig(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            添加配置组
          </Button>
        }
      >
        <Table
          columns={configColumns}
          dataSource={configs}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          expandable={{
            expandedRowRender: (record) => (
              <div style={{ margin: 0 }}>
                <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <h4>模型列表</h4>
                  <Button
                    type="primary"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setSelectedConfig(record);
                      setEditingModel(null);
                      modelForm.resetFields();
                      setModelModalVisible(true);
                    }}
                  >
                    添加模型
                  </Button>
                </div>
                <Table
                  columns={modelColumns}
                  dataSource={record.models || []}
                  rowKey="id"
                  pagination={false}
                  size="small"
                />
              </div>
            ),
            onExpand: (expanded, record) => {
              if (expanded) {
                setSelectedConfig(record);
              }
            }
          }}
        />
      </Card>

      {/* 配置组编辑模态框 */}
      <Modal
        title={editingConfig ? '编辑配置组' : '添加配置组'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingConfig(null);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleConfigSubmit}
        >
          <Form.Item
            name="name"
            label="配置组名称"
            rules={[{ required: true, message: '请输入配置组名称' }]}
          >
            <Input placeholder="例如：本地模型组" />
          </Form.Item>
          
          <Form.Item
            name="baseUrl"
            label="Base URL"
            rules={[
              { required: true, message: '请输入Base URL' },
              { type: 'url', message: '请输入有效的URL' }
            ]}
          >
            <Input placeholder="例如：http://localhost:8000/v1" />
          </Form.Item>
          
          <Form.Item
            name="apiKey"
            label="API Key"
            rules={[{ required: true, message: '请输入API Key' }]}
          >
            <Input.Password placeholder="请输入API Key" />
          </Form.Item>
          
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingConfig ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 模型编辑模态框 */}
      <Modal
        title={editingModel ? '编辑模型' : '添加模型'}
        open={modelModalVisible}
        onCancel={() => {
          setModelModalVisible(false);
          setEditingModel(null);
          modelForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={modelForm}
          layout="vertical"
          onFinish={handleModelSubmit}
        >
          <Form.Item
            name="name"
            label="模型名称"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input placeholder="例如：gpt-4" />
          </Form.Item>

          <Form.Item
            name="type"
            label="模型类型"
            initialValue="chat"
            rules={[{ required: true, message: '请选择模型类型' }]}
          >
            <Select placeholder="请选择模型类型">
              <Option value="chat">对话模型 (Chat)</Option>
              <Option value="embedding">向量嵌入模型 (Embedding)</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="supportThinking"
            label="支持Thinking"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            name="supportVision"
            label="支持Vision"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="supportWebSearch"
            label="支持联网搜索"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="uploadTypes"
            label="支持的上传类型"
          >
            <Select
              mode="multiple"
              placeholder="选择支持的上传类型"
              allowClear
            >
              <Option value="file">文件</Option>
              <Option value="image">图片</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="maxTokens"
            label="最大Token数"
            initialValue={8192}
            rules={[{ required: true, message: '请选择最大Token数' }]}
          >
            <Select placeholder="选择最大Token数">
              <Option value={8192}>8K (8,192)</Option>
              <Option value={32768}>32K (32,768)</Option>
               <Option value={65536}>64K (65,536)</Option>
              <Option value={131072}>128K (131,072)</Option>
              <Option value={1048576}>1M (1,048,576)</Option>
              <Option value={2097152}>2M (2,097,152)</Option>
            </Select>
          </Form.Item>
          
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModelModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingModel ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ModelConfig;
