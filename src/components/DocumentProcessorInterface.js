import React, { useState, useEffect } from 'react';
import {
  Modal,
  Upload,
  Button,
  Space,
  Typography,
  Alert,
  Progress,
  List,
  Card,
  Tag,
  Divider,
  Row,
  Col,
  Statistic,
  message,
  Tooltip
} from 'antd';
import {
  InboxOutlined,
  FileTextOutlined,
  PictureOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  EyeOutlined,
  DeleteOutlined,
  DownloadOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { documentProcessor } from '../utils/optimizedFeatures';

const { Dragger } = Upload;
const { Text, Title, Paragraph } = Typography;

/**
 * 📄 文档处理器界面组件
 * 提供多媒体文档处理的完整用户界面
 */
const DocumentProcessorInterface = ({ visible, onClose, onDocumentProcessed }) => {
  const [fileList, setFileList] = useState([]);
  const [processing, setProcessing] = useState(false);
  const [processedResults, setProcessedResults] = useState([]);
  const [supportedFormats, setSupportedFormats] = useState(null);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [currentProcessingFile, setCurrentProcessingFile] = useState('');

  // 加载支持的文件格式
  useEffect(() => {
    const loadSupportedFormats = async () => {
      try {
        const formats = await documentProcessor.getSupportedFormats();
        setSupportedFormats(formats);
      } catch (error) {
        console.error('❌ [DocumentProcessor] 获取支持格式失败:', error);
      }
    };

    if (visible) {
      loadSupportedFormats();
    }
  }, [visible]);

  // 监听处理进度
  useEffect(() => {
    const handleProgress = (progress) => {
      setProcessingProgress((progress.current / progress.total) * 100);
      setCurrentProcessingFile(progress.currentFile || '');
    };

    documentProcessor.onProgress(handleProgress);

    return () => {
      documentProcessor.removeProgressListener();
    };
  }, []);

  // 文件上传配置
  const uploadProps = {
    name: 'file',
    multiple: true,
    fileList,
    beforeUpload: (file) => {
      // 检查文件格式
      if (supportedFormats) {
        const fileExt = '.' + file.name.split('.').pop().toLowerCase();
        if (!supportedFormats.all.includes(fileExt)) {
          message.error(`不支持的文件格式: ${fileExt}`);
          return false;
        }
      }

      setFileList(prev => [...prev, file]);
      return false; // 阻止自动上传
    },
    onRemove: (file) => {
      setFileList(prev => prev.filter(f => f.uid !== file.uid));
    },
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
      showDownloadIcon: false
    }
  };

  // 处理文档
  const handleProcessDocuments = async () => {
    if (fileList.length === 0) {
      message.warning('请先选择要处理的文件');
      return;
    }

    setProcessing(true);
    setProcessedResults([]);
    setProcessingProgress(0);

    try {
      const filePaths = fileList.map(file => file.path || file.name);
      const results = await documentProcessor.processDocuments(filePaths, {
        enableOCR: true,
        extractMetadata: true
      });

      setProcessedResults(results.results || []);
      
      if (results.errors && results.errors.length > 0) {
        message.warning(`处理完成，但有 ${results.errors.length} 个文件失败`);
      } else {
        message.success('所有文档处理完成');
      }

      // 通知父组件
      onDocumentProcessed?.(results);

    } catch (error) {
      console.error('❌ [DocumentProcessor] 批量处理失败:', error);
      message.error('文档处理失败');
    } finally {
      setProcessing(false);
      setProcessingProgress(0);
      setCurrentProcessingFile('');
    }
  };

  // 获取文件类型图标
  const getFileIcon = (fileName) => {
    const ext = fileName.split('.').pop().toLowerCase();
    switch (ext) {
      case 'pdf': return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;
      case 'doc':
      case 'docx': return <FileWordOutlined style={{ color: '#1890ff' }} />;
      case 'xls':
      case 'xlsx': return <FileExcelOutlined style={{ color: '#52c41a' }} />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif': return <PictureOutlined style={{ color: '#fa8c16' }} />;
      default: return <FileTextOutlined style={{ color: '#666' }} />;
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  };

  return (
    <Modal
      title={
        <Space>
          <ThunderboltOutlined style={{ color: '#fa8c16' }} />
          <span>多媒体文档处理器</span>
          <Tag color="orange">支持 {supportedFormats?.all?.length || 0} 种格式</Tag>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={900}
      footer={
        <Space>
          <Button onClick={onClose}>关闭</Button>
          <Button
            type="primary"
            icon={<ThunderboltOutlined />}
            loading={processing}
            onClick={handleProcessDocuments}
            disabled={fileList.length === 0}
          >
            {processing ? '处理中...' : '开始处理'}
          </Button>
        </Space>
      }
    >
      {/* 支持格式说明 */}
      {supportedFormats && (
        <Alert
          message="支持的文件格式"
          description={
            <div>
              <Space wrap>
                <Tag color="red">PDF文档 (.pdf)</Tag>
                <Tag color="blue">Word文档 (.docx)</Tag>
                <Tag color="green">Excel表格 (.xlsx, .xls)</Tag>
                <Tag color="orange">图片文件 (.jpg, .png, .gif, .bmp, .webp)</Tag>
                <Tag color="purple">文本文件 (.txt, .md, .csv, .json等)</Tag>
              </Space>
              <Paragraph style={{ marginTop: 8, marginBottom: 0 }}>
                <Text type="secondary">
                  • 图片文件使用Tesseract.js OCR技术提取文本
                  • PDF使用PDF.js解析文档内容
                  • Word文档使用Mammoth.js提取文本
                  • Excel使用SheetJS处理表格数据
                  • 所有处理均在浏览器中完成，无需服务器
                </Text>
              </Paragraph>
            </div>
          }
          type="info"
          style={{ marginBottom: 16 }}
        />
      )}

      <Row gutter={16}>
        {/* 文件上传区域 */}
        <Col span={12}>
          <Card title="文件上传" size="small">
            <Dragger {...uploadProps} style={{ marginBottom: 16 }}>
              <p className="ant-upload-drag-icon">
                <InboxOutlined style={{ color: '#1890ff' }} />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持单个或批量上传，支持多种文档和图片格式
              </p>
            </Dragger>

            {/* 处理进度 */}
            {processing && (
              <div>
                <Progress 
                  percent={Math.round(processingProgress)} 
                  status="active"
                  style={{ marginBottom: 8 }}
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  正在处理: {currentProcessingFile}
                </Text>
              </div>
            )}

            {/* 文件列表 */}
            {fileList.length > 0 && (
              <List
                size="small"
                dataSource={fileList}
                renderItem={(file) => (
                  <List.Item>
                    <Space>
                      {getFileIcon(file.name)}
                      <Text style={{ fontSize: '12px' }}>{file.name}</Text>
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {formatFileSize(file.size)}
                      </Text>
                    </Space>
                  </List.Item>
                )}
              />
            )}
          </Card>
        </Col>

        {/* 处理结果区域 */}
        <Col span={12}>
          <Card title="处理结果" size="small">
            {processedResults.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <FileTextOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">暂无处理结果</Text>
                </div>
              </div>
            ) : (
              <List
                size="small"
                dataSource={processedResults}
                renderItem={(result) => (
                  <List.Item
                    actions={[
                      <Tooltip title="查看内容">
                        <Button 
                          size="small" 
                          icon={<EyeOutlined />}
                          onClick={() => {
                            Modal.info({
                              title: `文档内容 - ${result.filePath}`,
                              content: (
                                <div style={{ maxHeight: '400px', overflow: 'auto' }}>
                                  <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                                    {result.result?.text || '无文本内容'}
                                  </pre>
                                </div>
                              ),
                              width: 800
                            });
                          }}
                        />
                      </Tooltip>,
                      <Tooltip title="添加到知识库">
                        <Button 
                          size="small" 
                          type="primary"
                          onClick={() => {
                            // TODO: 实现添加到知识库功能
                            message.success('已添加到知识库');
                          }}
                        >
                          添加
                        </Button>
                      </Tooltip>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={getFileIcon(result.filePath)}
                      title={
                        <Space>
                          <Text style={{ fontSize: '12px' }}>
                            {result.filePath.split('/').pop()}
                          </Text>
                          <Tag color={result.success ? 'green' : 'red'} size="small">
                            {result.success ? '成功' : '失败'}
                          </Tag>
                        </Space>
                      }
                      description={
                        <div>
                          {result.success ? (
                            <Space direction="vertical" size="small">
                              <Text type="secondary" style={{ fontSize: '11px' }}>
                                提取文本: {result.result?.text?.length || 0} 字符
                              </Text>
                              {result.result?.metadata && (
                                <Text type="secondary" style={{ fontSize: '11px' }}>
                                  处理时间: {result.result.processingTime || 0}ms
                                </Text>
                              )}
                            </Space>
                          ) : (
                            <Text type="danger" style={{ fontSize: '11px' }}>
                              {result.error}
                            </Text>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            )}
          </Card>
        </Col>
      </Row>
    </Modal>
  );
};

export default DocumentProcessorInterface;
