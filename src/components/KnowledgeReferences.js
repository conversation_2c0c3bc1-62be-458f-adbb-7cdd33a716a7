import React, { useState } from 'react';
import {
  Card,
  Tag,
  Space,
  Button,
  Collapse,
  Typography,
  Tooltip,
  Progress,
  Divider
} from 'antd';
import {
  BookOutlined,
  FileTextOutlined,
  DownOutlined,
  UpOutlined,
  DatabaseOutlined,
  BranchesOutlined,
  PercentageOutlined
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

/**
 * 知识库引用组件
 * 显示AI回答中使用的知识库内容引用
 */
const KnowledgeReferences = ({ references = [], style = {} }) => {
  const [expanded, setExpanded] = useState(false);

  if (!references || references.length === 0) {
    return null;
  }

  // 按知识库分组引用
  const groupedReferences = references.reduce((groups, ref) => {
    const key = ref.knowledgeBaseName;
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(ref);
    return groups;
  }, {});

  const totalReferences = references.length;
  const knowledgeBaseCount = Object.keys(groupedReferences).length;

  return (
    <div style={{ 
      marginTop: '12px', 
      padding: '12px',
      background: 'linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(138, 43, 226, 0.05) 100%)',
      border: '1px solid rgba(0, 212, 255, 0.2)',
      borderRadius: '8px',
      backdropFilter: 'blur(10px)',
      ...style 
    }}>
      {/* 引用标题栏 */}
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        marginBottom: expanded ? '12px' : '0'
      }}>
        <Space>
          <DatabaseOutlined style={{ 
            color: '#00d4ff',
            filter: 'drop-shadow(0 0 3px rgba(0, 212, 255, 0.5))'
          }} />
          <Text strong style={{ color: '#ffffff' }}>
            知识库引用
          </Text>
          <Tag color="blue" size="small">
            {totalReferences} 个引用
          </Tag>
          <Tag color="purple" size="small">
            {knowledgeBaseCount} 个知识库
          </Tag>
        </Space>
        
        <Button
          type="text"
          size="small"
          icon={expanded ? <UpOutlined /> : <DownOutlined />}
          onClick={() => setExpanded(!expanded)}
          style={{ 
            color: '#00d4ff',
            border: 'none'
          }}
        >
          {expanded ? '收起' : '展开详情'}
        </Button>
      </div>

      {/* 展开的引用详情 */}
      {expanded && (
        <div>
          <Divider style={{ margin: '8px 0', borderColor: 'rgba(0, 212, 255, 0.3)' }} />
          
          {Object.entries(groupedReferences).map(([kbName, refs], kbIndex) => (
            <Card
              key={kbIndex}
              size="small"
              title={
                <Space>
                  <BookOutlined style={{ color: '#52c41a' }} />
                  <Text strong style={{ color: '#ffffff' }}>{kbName}</Text>
                  <Tag color="green" size="small">{refs.length} 个引用</Tag>
                </Space>
              }
              style={{
                marginBottom: '12px',
                background: 'rgba(0, 0, 0, 0.3)',
                border: '1px solid rgba(82, 196, 26, 0.3)'
              }}
              headStyle={{
                background: 'rgba(82, 196, 26, 0.1)',
                borderBottom: '1px solid rgba(82, 196, 26, 0.3)'
              }}
              bodyStyle={{
                background: 'rgba(0, 0, 0, 0.2)'
              }}
            >
              <Collapse
                ghost
                size="small"
                expandIconPosition="end"
              >
                {refs.map((ref, refIndex) => (
                  <Panel
                    key={refIndex}
                    header={
                      <Space>
                        <FileTextOutlined style={{ color: '#1890ff' }} />
                        <Text style={{ color: '#ffffff' }}>{ref.documentTitle}</Text>
                        <Tag color="blue" size="small">
                          {ref.chunkType === 'parent' ? '父分块' : 
                           ref.chunkType === 'child' ? '子分块' : '分块'}
                        </Tag>
                        <Tooltip title={`相似度: ${(ref.similarity * 100).toFixed(1)}%`}>
                          <Progress
                            percent={ref.similarity * 100}
                            size="small"
                            style={{ width: '60px' }}
                            strokeColor={{
                              '0%': '#108ee9',
                              '100%': '#87d068',
                            }}
                            showInfo={false}
                          />
                        </Tooltip>
                        <Text type="secondary" style={{ fontSize: '11px', color: '#cccccc' }}>
                          {(ref.similarity * 100).toFixed(1)}%
                        </Text>
                      </Space>
                    }
                    style={{
                      background: 'rgba(255, 255, 255, 0.02)',
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: '4px',
                      marginBottom: '8px'
                    }}
                  >
                    <div style={{ padding: '8px 0' }}>
                      {/* 引用内容 */}
                      <div style={{ marginBottom: '12px' }}>
                        <Text strong style={{ color: '#ffffff', fontSize: '12px' }}>引用内容：</Text>
                        <div style={{
                          marginTop: '4px',
                          padding: '8px',
                          background: 'rgba(0, 0, 0, 0.4)',
                          border: '1px solid rgba(255, 255, 255, 0.1)',
                          borderRadius: '4px',
                          maxHeight: '120px',
                          overflow: 'auto'
                        }}>
                          <Paragraph
                            style={{ 
                              color: '#ffffff', 
                              fontSize: '12px', 
                              margin: 0,
                              lineHeight: '1.4'
                            }}
                            ellipsis={{ rows: 4, expandable: true, symbol: '展开' }}
                          >
                            {ref.text}
                          </Paragraph>
                        </div>
                      </div>

                      {/* 父分块上下文 */}
                      {ref.parentContext && (
                        <div style={{ marginBottom: '12px' }}>
                          <Text strong style={{ color: '#ffffff', fontSize: '12px' }}>
                            <BranchesOutlined style={{ marginRight: '4px', color: '#722ed1' }} />
                            父分块上下文：
                          </Text>
                          <div style={{
                            marginTop: '4px',
                            padding: '8px',
                            background: 'rgba(114, 46, 209, 0.1)',
                            border: '1px solid rgba(114, 46, 209, 0.3)',
                            borderRadius: '4px',
                            maxHeight: '100px',
                            overflow: 'auto'
                          }}>
                            <Paragraph
                              style={{ 
                                color: '#cccccc', 
                                fontSize: '11px', 
                                margin: 0,
                                lineHeight: '1.4'
                              }}
                              ellipsis={{ rows: 3, expandable: true, symbol: '展开' }}
                            >
                              {ref.parentContext.text}
                            </Paragraph>
                          </div>
                        </div>
                      )}

                      {/* 元数据信息 */}
                      <div>
                        <Space size="small" wrap>
                          <Tag size="small" color="geekblue">
                            模型: {ref.embeddingModel}
                          </Tag>
                          <Tag size="small" color="cyan">
                            分块 #{ref.chunkIndex + 1}
                          </Tag>
                          <Tooltip title="相似度分数">
                            <Tag size="small" color="orange">
                              <PercentageOutlined /> {(ref.similarity * 100).toFixed(2)}%
                            </Tag>
                          </Tooltip>
                        </Space>
                      </div>
                    </div>
                  </Panel>
                ))}
              </Collapse>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default KnowledgeReferences;
