import React, { memo, useMemo, useCallback, useRef, useEffect, useState } from 'react';
import { VariableSizeList as List } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import MessageItem from './MessageItem';
import '../styles/messageItem.css';

// 🚀 高性能消息列表组件 - 深度优化滚动性能
const HighPerformanceMessageList = memo(({ 
  messages, 
  showThinking,
  onScroll
}) => {
  const listRef = useRef();
  const itemHeightCache = useRef(new Map());
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollTimeoutRef = useRef();

  // 🎯 动态计算消息高度
  const getItemHeight = useCallback((index) => {
    const msg = messages[index];
    if (!msg) return 80;

    // 从缓存中获取高度
    const cacheKey = `${msg.id}-${msg.content?.length || 0}-${showThinking}`;
    if (itemHeightCache.current.has(cacheKey)) {
      return itemHeightCache.current.get(cacheKey);
    }

    // 估算高度
    let estimatedHeight = 80; // 基础高度

    // 根据消息类型调整
    if (msg.sender === 'system') {
      estimatedHeight = 60;
    } else if (msg.sender === 'user') {
      estimatedHeight = Math.min(120 + (msg.content?.length || 0) * 0.3, 300);
    } else {
      // AI消息
      estimatedHeight = 120;
      
      // 内容长度影响
      if (msg.content) {
        const contentLength = msg.content.length;
        if (contentLength > 1000) {
          estimatedHeight += Math.min(contentLength * 0.2, 500);
        } else {
          estimatedHeight += contentLength * 0.4;
        }
      }

      // 思考过程额外高度
      if (msg.thinking && showThinking) {
        estimatedHeight += Math.min(msg.thinking.length * 0.15, 200);
      }

      // 代码块检测
      const codeBlockCount = (msg.content?.match(/```/g) || []).length / 2;
      if (codeBlockCount > 0) {
        estimatedHeight += codeBlockCount * 100;
      }
    }

    // 缓存计算结果
    const finalHeight = Math.max(estimatedHeight, 60);
    itemHeightCache.current.set(cacheKey, finalHeight);
    
    return finalHeight;
  }, [messages, showThinking]);

  // 🎯 优化的消息渲染器
  const renderMessageItem = useCallback(({ index, style, data }) => {
    const { messages, showThinking, isScrolling } = data;
    const msg = messages[index];
    
    if (!msg) return null;

    const isUser = msg.sender === 'user';
    const isSystem = msg.sender === 'system';
    const isStreaming = msg.isStreaming;

    return (
      <div style={style}>
        <MessageItem
          msg={msg}
          index={index}
          isUser={isUser}
          isSystem={isSystem}
          isStreaming={isStreaming}
          showThinking={showThinking}
          isScrolling={isScrolling} // 传递滚动状态
          optimized={true} // 启用优化模式
        />
      </div>
    );
  }, []);

  // 🎯 滚动状态管理
  const handleScroll = useCallback((scrollInfo) => {
    setIsScrolling(true);
    
    // 清除之前的定时器
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    
    // 滚动结束后重置状态
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 150);

    if (onScroll) {
      onScroll(scrollInfo);
    }
  }, [onScroll]);

  // 🎯 缓存消息数据
  const messageData = useMemo(() => ({
    messages,
    showThinking,
    isScrolling
  }), [messages, showThinking, isScrolling]);

  // 🎯 自动滚动到底部
  const scrollToBottom = useCallback(() => {
    if (listRef.current && messages.length > 0) {
      listRef.current.scrollToItem(messages.length - 1, 'end');
    }
  }, [messages.length]);

  // 🎯 当有新消息时自动滚动
  useEffect(() => {
    const timer = setTimeout(scrollToBottom, 100);
    return () => clearTimeout(timer);
  }, [scrollToBottom]);

  // 🎯 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  if (!messages || messages.length === 0) {
    return (
      <div style={{
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'rgba(136, 146, 176, 0.8)',
        fontSize: '14px'
      }}>
        暂无消息
      </div>
    );
  }

  return (
    <div style={{ height: '100%', width: '100%' }}>
      <AutoSizer>
        {({ height, width }) => (
          <List
            ref={listRef}
            height={height}
            width={width}
            itemCount={messages.length}
            itemSize={getItemHeight}
            itemData={messageData}
            onScroll={handleScroll}
            overscanCount={3} // 减少预渲染项目数量
            useIsScrolling={true} // 启用滚动状态优化
            style={{
              // 优化滚动条样式
              scrollbarWidth: 'thin',
              scrollbarColor: 'rgba(0, 212, 255, 0.3) transparent'
            }}
          >
            {renderMessageItem}
          </List>
        )}
      </AutoSizer>
    </div>
  );
});

HighPerformanceMessageList.displayName = 'HighPerformanceMessageList';

export default HighPerformanceMessageList;
