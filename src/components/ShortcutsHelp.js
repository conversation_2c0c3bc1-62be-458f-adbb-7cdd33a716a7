import React from 'react';
import { Modal, Typography, Space, Card, Row, Col, Tag, Divider } from 'antd';
import { 
  ControlOutlined,
  RocketOutlined,
  ThunderboltOutlined,
  EditOutlined,
  SettingOutlined,
  BugOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

/**
 * 🎯 快捷键帮助对话框组件
 * 显示所有可用的快捷键和说明
 */
const ShortcutsHelp = ({ visible, onClose, shortcuts }) => {
  // 类别图标映射
  const categoryIcons = {
    navigation: <RocketOutlined style={{ color: '#00d4ff' }} />,
    actions: <ThunderboltOutlined style={{ color: '#52c41a' }} />,
    input: <EditOutlined style={{ color: '#fa8c16' }} />,
    app: <SettingOutlined style={{ color: '#722ed1' }} />,
    dev: <BugOutlined style={{ color: '#f5222d' }} />
  };

  // 渲染快捷键项
  const renderShortcutItem = (item) => (
    <div 
      key={item.action}
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '8px 0',
        borderBottom: '1px solid rgba(255, 255, 255, 0.06)',
      }}
    >
      <Text style={{ color: '#8892b0', flex: 1 }}>
        {item.description}
      </Text>
      <Tag 
        style={{
          background: 'rgba(0, 212, 255, 0.1)',
          border: '1px solid rgba(0, 212, 255, 0.3)',
          color: '#00d4ff',
          fontFamily: 'Monaco, Consolas, monospace',
          fontSize: '12px',
          fontWeight: 'bold',
          borderRadius: '4px',
          padding: '2px 8px',
          minWidth: '80px',
          textAlign: 'center'
        }}
      >
        {item.shortcut}
      </Tag>
    </div>
  );

  // 渲染类别卡片
  const renderCategory = (categoryKey, category) => (
    <Card
      key={categoryKey}
      style={{
        background: 'rgba(26, 31, 46, 0.6)',
        border: '1px solid rgba(0, 212, 255, 0.2)',
        borderRadius: '8px',
        marginBottom: '16px'
      }}
      bodyStyle={{ padding: '16px' }}
    >
      <Space style={{ marginBottom: '12px' }}>
        {categoryIcons[categoryKey]}
        <Title 
          level={5} 
          style={{ 
            color: '#ffffff', 
            margin: 0,
            fontWeight: 'bold'
          }}
        >
          {category.title}
        </Title>
      </Space>
      <div>
        {category.items.map(renderShortcutItem)}
      </div>
    </Card>
  );

  return (
    <Modal
      title={
        <Space>
          <ControlOutlined style={{ color: '#00d4ff' }} />
          <span style={{ color: '#ffffff' }}>快捷键帮助</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      centered
      styles={{
        mask: {
          background: 'rgba(0, 0, 0, 0.8)',
          backdropFilter: 'blur(8px)'
        },
        content: {
          background: 'linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0a0e1a 100%)',
          border: '1px solid rgba(0, 212, 255, 0.3)',
          borderRadius: '12px',
          boxShadow: '0 20px 40px rgba(0, 212, 255, 0.2)'
        },
        header: {
          background: 'rgba(26, 31, 46, 0.8)',
          borderBottom: '1px solid rgba(0, 212, 255, 0.2)',
          borderRadius: '12px 12px 0 0'
        },
        body: {
          background: 'transparent',
          maxHeight: '70vh',
          overflowY: 'auto',
          padding: '20px'
        }
      }}
    >
      <div style={{ color: '#ffffff' }}>
        {/* 顶部说明 */}
        <div style={{ 
          textAlign: 'center', 
          marginBottom: '24px',
          padding: '16px',
          background: 'rgba(0, 212, 255, 0.05)',
          borderRadius: '8px',
          border: '1px solid rgba(0, 212, 255, 0.1)'
        }}>
          <Text style={{ color: '#8892b0', fontSize: '14px' }}>
            💡 使用快捷键可以大幅提升您的操作效率
          </Text>
        </div>

        {/* 快捷键列表 */}
        <Row gutter={[16, 0]}>
          <Col span={12}>
            {shortcuts.navigation && renderCategory('navigation', shortcuts.navigation)}
            {shortcuts.actions && renderCategory('actions', shortcuts.actions)}
            {shortcuts.input && renderCategory('input', shortcuts.input)}
          </Col>
          <Col span={12}>
            {shortcuts.app && renderCategory('app', shortcuts.app)}
            {shortcuts.dev && renderCategory('dev', shortcuts.dev)}
            
            {/* 平台说明 */}
            <Card
              style={{
                background: 'rgba(255, 193, 7, 0.1)',
                border: '1px solid rgba(255, 193, 7, 0.3)',
                borderRadius: '8px'
              }}
              bodyStyle={{ padding: '16px' }}
            >
              <Space direction="vertical" size="small">
                <Text style={{ color: '#ffc107', fontWeight: 'bold' }}>
                  📱 平台说明
                </Text>
                <Text style={{ color: '#8892b0', fontSize: '12px' }}>
                  • macOS: Cmd 键替代 Ctrl 键
                </Text>
                <Text style={{ color: '#8892b0', fontSize: '12px' }}>
                  • Windows/Linux: 使用 Ctrl 键
                </Text>
                <Text style={{ color: '#8892b0', fontSize: '12px' }}>
                  • 在输入框中部分快捷键可能不可用
                </Text>
              </Space>
            </Card>
          </Col>
        </Row>

        <Divider style={{ borderColor: 'rgba(0, 212, 255, 0.2)', margin: '24px 0 16px 0' }} />
        
        {/* 底部提示 */}
        <div style={{ textAlign: 'center' }}>
          <Text style={{ color: '#8892b0', fontSize: '13px' }}>
            按 <Tag style={{ 
              background: 'rgba(0, 212, 255, 0.1)', 
              border: '1px solid rgba(0, 212, 255, 0.3)', 
              color: '#00d4ff',
              margin: '0 4px'
            }}>Ctrl + O</Tag> 可随时打开此帮助
          </Text>
        </div>
      </div>
    </Modal>
  );
};

export default ShortcutsHelp; 