import React, { useState } from 'react';
import {
  Button,
  Dropdown,
  Tooltip,
  Popconfirm,
  message,
  Menu
} from 'antd';
import {
  ClearOutlined,
  DeleteOutlined,
  MoreOutlined,
  ExportOutlined,
  CopyOutlined,
  SaveOutlined,
  ShareAltOutlined,
  PrinterOutlined
} from '@ant-design/icons';

/**
 * 🚀 一键操作工具栏组件
 * 提供清空对话、重新生成、删除指定回答等快捷操作功能
 */
const QuickActionToolbar = ({
  messages = [],
  onClearConversation,
  onDeleteMessage,
  onExportConversation,
  onCopyAllMessages = null,
  onSaveConversation = null,
  onPrintConversation = null,
  style = {},
  className = ''
}) => {
  const [loading, setLoading] = useState({
    clear: false,
    delete: false,
    export: false
  });

  // 设置加载状态
  const setLoadingState = (action, isLoading) => {
    setLoading(prev => ({ ...prev, [action]: isLoading }));
  };

  // 清空当前对话
  const handleClearConversation = async () => {
    try {
      setLoadingState('clear', true);
      await onClearConversation?.();
      message.success('对话已清空');
    } catch (error) {
      console.error('❌ [快捷操作] 清空对话失败:', error);
      message.error('清空对话失败: ' + error.message);
    } finally {
      setLoadingState('clear', false);
    }
  };



  // 导出对话
  const handleExportConversation = async () => {
    try {
      setLoadingState('export', true);
      await onExportConversation?.();
      message.success('对话导出成功');
    } catch (error) {
      console.error('❌ [快捷操作] 导出对话失败:', error);
      message.error('导出对话失败: ' + error.message);
    } finally {
      setLoadingState('export', false);
    }
  };

  // 删除指定消息的菜单
  const getDeleteMessageMenu = () => {
    const aiMessages = messages.filter(msg => msg.sender !== 'user');
    
    if (aiMessages.length === 0) {
      return (
        <Menu>
          <Menu.Item disabled>
            <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>
              没有可删除的AI回复
            </span>
          </Menu.Item>
        </Menu>
      );
    }

    return (
      <Menu
        style={{
          background: 'rgba(26, 31, 46, 0.95)',
          border: '1px solid rgba(0, 212, 255, 0.2)',
          borderRadius: '8px',
          maxHeight: '300px',
          overflowY: 'auto'
        }}
      >
        {aiMessages.slice(-10).reverse().map((msg, index) => (
          <Menu.Item 
            key={msg.id || index}
            style={{
              color: 'rgba(255, 255, 255, 0.85)',
              borderBottom: index < Math.min(aiMessages.length, 10) - 1 ? '1px solid rgba(0, 212, 255, 0.1)' : 'none'
            }}
          >
            <Popconfirm
              title="确定删除这条回复吗？"
              description={
                <div style={{ maxWidth: 200 }}>
                  <div style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.6)' }}>
                    来自: {msg.sender}
                  </div>
                  <div style={{ fontSize: '12px', marginTop: 4 }}>
                    {msg.content?.substring(0, 50)}...
                  </div>
                </div>
              }
              onConfirm={() => handleDeleteMessage(msg)}
              okText="删除"
              cancelText="取消"
              okButtonProps={{ danger: true }}
            >
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'space-between',
                width: '100%'
              }}>
                <div style={{ flex: 1, minWidth: 0 }}>
                  <div style={{ 
                    fontSize: '12px', 
                    color: '#00d4ff',
                    marginBottom: 2
                  }}>
                    {msg.sender}
                  </div>
                  <div style={{ 
                    fontSize: '11px', 
                    color: 'rgba(255, 255, 255, 0.7)',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {msg.content?.substring(0, 30)}...
                  </div>
                </div>
                <DeleteOutlined 
                  style={{ 
                    color: '#ff4d4f', 
                    fontSize: '12px',
                    marginLeft: 8
                  }} 
                />
              </div>
            </Popconfirm>
          </Menu.Item>
        ))}
      </Menu>
    );
  };

  // 删除指定消息
  const handleDeleteMessage = async (messageToDelete) => {
    try {
      setLoadingState('delete', true);
      await onDeleteMessage?.(messageToDelete);
      message.success('消息已删除');
    } catch (error) {
      console.error('❌ [快捷操作] 删除消息失败:', error);
      message.error('删除消息失败: ' + error.message);
    } finally {
      setLoadingState('delete', false);
    }
  };

  // 复制所有消息
  const handleCopyAllMessages = async () => {
    try {
      if (onCopyAllMessages) {
        await onCopyAllMessages();
        message.success('所有消息已复制到剪贴板');
      } else {
        // 默认实现：将所有消息转换为文本格式
        const allText = messages.map(msg => {
          const timestamp = new Date(msg.timestamp).toLocaleString();
          const sender = msg.sender === 'user' ? '用户' : msg.sender;
          return `[${timestamp}] ${sender}:\n${msg.content}\n`;
        }).join('\n');

        await navigator.clipboard.writeText(allText);
        message.success('所有消息已复制到剪贴板');
      }
    } catch (error) {
      console.error('❌ [快捷操作] 复制所有消息失败:', error);
      message.error('复制失败: ' + error.message);
    }
  };

  // 保存对话
  const handleSaveConversation = async () => {
    try {
      if (onSaveConversation) {
        await onSaveConversation();
        message.success('对话已保存');
      } else {
        message.info('保存功能暂未实现');
      }
    } catch (error) {
      console.error('❌ [快捷操作] 保存对话失败:', error);
      message.error('保存失败: ' + error.message);
    }
  };

  // 打印对话
  const handlePrintConversation = async () => {
    try {
      if (onPrintConversation) {
        await onPrintConversation();
      } else {
        // 默认实现：打开打印对话框
        window.print();
      }
    } catch (error) {
      console.error('❌ [快捷操作] 打印对话失败:', error);
      message.error('打印失败: ' + error.message);
    }
  };

  // 更多操作菜单
  const getMoreActionsMenu = () => (
    <Menu
      style={{
        background: 'rgba(26, 31, 46, 0.95)',
        border: '1px solid rgba(0, 212, 255, 0.2)',
        borderRadius: '8px'
      }}
    >
      <Menu.Item
        key="copy-all"
        icon={<CopyOutlined />}
        onClick={handleCopyAllMessages}
        disabled={!hasMessages}
        style={{ color: 'rgba(255, 255, 255, 0.85)' }}
      >
        复制所有消息
      </Menu.Item>
      <Menu.Item
        key="save"
        icon={<SaveOutlined />}
        onClick={handleSaveConversation}
        disabled={!hasMessages}
        style={{ color: 'rgba(255, 255, 255, 0.85)' }}
      >
        保存对话
      </Menu.Item>
      <Menu.Item
        key="print"
        icon={<PrinterOutlined />}
        onClick={handlePrintConversation}
        disabled={!hasMessages}
        style={{ color: 'rgba(255, 255, 255, 0.85)' }}
      >
        打印对话
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item
        key="share"
        icon={<ShareAltOutlined />}
        disabled={!hasMessages}
        style={{ color: 'rgba(255, 255, 255, 0.85)' }}
      >
        分享对话
      </Menu.Item>
    </Menu>
  );

  // 检查是否有消息
  const hasMessages = messages && messages.length > 0;
  const hasAIMessages = messages && messages.some(msg => msg.sender !== 'user');

  return (
    <div 
      className={`quick-action-toolbar ${className}`}
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        padding: '8px 12px',
        background: 'rgba(26, 31, 46, 0.8)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(0, 212, 255, 0.2)',
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
        ...style
      }}
    >
      {/* 清空对话 */}
      <Popconfirm
        title="确定要清空当前对话吗？"
        description="此操作不可撤销，所有消息将被删除"
        onConfirm={handleClearConversation}
        okText="确定清空"
        cancelText="取消"
        okButtonProps={{ danger: true }}
        disabled={!hasMessages}
      >
        <Tooltip title="清空当前对话">
          <Button
            icon={<ClearOutlined />}
            size="small"
            loading={loading.clear}
            disabled={!hasMessages}
            style={{
              background: 'rgba(255, 77, 79, 0.1)',
              border: '1px solid rgba(255, 77, 79, 0.3)',
              color: hasMessages ? '#ff4d4f' : 'rgba(255, 255, 255, 0.3)',
              borderRadius: '8px'
            }}
          >
            清空
          </Button>
        </Tooltip>
      </Popconfirm>



      {/* 删除指定回答 */}
      <Dropdown
        overlay={getDeleteMessageMenu()}
        trigger={['click']}
        disabled={!hasAIMessages}
        placement="topLeft"
      >
        <Tooltip title="删除指定AI回答">
          <Button
            icon={<DeleteOutlined />}
            size="small"
            loading={loading.delete}
            disabled={!hasAIMessages}
            style={{
              background: 'rgba(245, 34, 45, 0.1)',
              border: '1px solid rgba(245, 34, 45, 0.3)',
              color: hasAIMessages ? '#f5222d' : 'rgba(255, 255, 255, 0.3)',
              borderRadius: '8px'
            }}
          >
            删除回答
          </Button>
        </Tooltip>
      </Dropdown>

      {/* 导出对话 */}
      <Tooltip title="导出当前对话">
        <Button
          icon={<ExportOutlined />}
          size="small"
          loading={loading.export}
          disabled={!hasMessages}
          onClick={handleExportConversation}
          style={{
            background: 'rgba(82, 196, 26, 0.1)',
            border: '1px solid rgba(82, 196, 26, 0.3)',
            color: hasMessages ? '#52c41a' : 'rgba(255, 255, 255, 0.3)',
            borderRadius: '8px'
          }}
        >
          导出
        </Button>
      </Tooltip>

      {/* 分隔线 */}
      <div style={{
        width: '1px',
        height: '20px',
        background: 'rgba(0, 212, 255, 0.2)',
        margin: '0 4px'
      }} />

      {/* 更多操作 */}
      <Dropdown
        overlay={getMoreActionsMenu()}
        trigger={['click']}
        disabled={!hasMessages}
        placement="topLeft"
      >
        <Tooltip title="更多操作">
          <Button
            icon={<MoreOutlined />}
            size="small"
            disabled={!hasMessages}
            style={{
              background: 'rgba(0, 212, 255, 0.1)',
              border: '1px solid rgba(0, 212, 255, 0.3)',
              color: hasMessages ? '#00d4ff' : 'rgba(255, 255, 255, 0.3)',
              borderRadius: '8px'
            }}
          >
            更多
          </Button>
        </Tooltip>
      </Dropdown>
    </div>
  );
};

export default QuickActionToolbar;
