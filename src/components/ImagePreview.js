import React, { useState } from 'react';
import { Modal, Image, Button, message, Space, Tooltip } from 'antd';
import { EyeOutlined, CopyOutlined, DownloadOutlined } from '@ant-design/icons';

// 🖼️ 图片预览组件
const ImagePreview = ({ src, alt, style = {}, ...props }) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // 复制图片到剪贴板
  const handleCopyImage = async () => {
    try {
      // 创建canvas来处理图片
      const img = new window.Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = async () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        
        canvas.toBlob(async (blob) => {
          try {
            await navigator.clipboard.write([
              new ClipboardItem({ 'image/png': blob })
            ]);
            message.success('图片已复制到剪贴板');
          } catch (err) {
            console.error('复制图片失败:', err);
            // 降级方案：复制图片URL
            await navigator.clipboard.writeText(src);
            message.success('图片链接已复制到剪贴板');
          }
        });
      };
      
      img.onerror = async () => {
        // 降级方案：复制图片URL
        await navigator.clipboard.writeText(src);
        message.success('图片链接已复制到剪贴板');
      };
      
      img.src = src;
    } catch (err) {
      console.error('复制失败:', err);
      message.error('复制失败');
    }
  };

  // 下载图片
  const handleDownloadImage = () => {
    try {
      const link = document.createElement('a');
      link.href = src;
      link.download = alt || `image-${Date.now()}.png`;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      message.success('图片下载已开始');
    } catch (err) {
      console.error('下载失败:', err);
      message.error('下载失败');
    }
  };

  return (
    <>
      {/* 原图片 - 添加预览提示 */}
      <div 
        style={{ 
          position: 'relative', 
          display: 'inline-block',
          cursor: 'pointer',
          ...style 
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <img
          src={src}
          alt={alt}
          style={{
            maxWidth: '100%',
            borderRadius: '8px',
            boxShadow: isHovered ? '0 8px 25px rgba(0, 212, 255, 0.3)' : '0 4px 15px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease',
            transform: isHovered ? 'scale(1.02)' : 'scale(1)',
            ...style
          }}
          {...props}
        />
        
        {/* 操作按钮组 */}
        <div
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            opacity: isHovered ? 1 : 0,
            transition: 'opacity 0.3s ease',
            pointerEvents: isHovered ? 'auto' : 'none'
          }}
        >
          <Space size="small">
            <Tooltip title="预览图片">
              <Button
                type="text"
                icon={<EyeOutlined />}
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  setPreviewVisible(true);
                }}
                style={{
                  background: 'rgba(0, 0, 0, 0.7)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '50%',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />
            </Tooltip>
            <Tooltip title="复制图片">
              <Button
                type="text"
                icon={<CopyOutlined />}
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleCopyImage();
                }}
                style={{
                  background: 'rgba(0, 0, 0, 0.7)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '50%',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />
            </Tooltip>
          </Space>
        </div>
      </div>

      {/* 图片预览模态框 */}
      <Modal
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        title={
          <div style={{ color: '#00d4ff', display: 'flex', alignItems: 'center', gap: '8px' }}>
            <EyeOutlined />
            图片预览
          </div>
        }
        width="auto"
        centered
        style={{ maxWidth: '90vw', maxHeight: '90vh' }}
        bodyStyle={{ padding: 0, background: '#1a1a1a' }}
        footer={[
          <Space key="actions">
            <Tooltip title="复制图片">
              <Button 
                icon={<CopyOutlined />} 
                onClick={handleCopyImage}
                style={{
                  background: 'rgba(114, 46, 209, 0.1)',
                  borderColor: 'rgba(114, 46, 209, 0.3)',
                  color: '#722ed1'
                }}
              >
                复制
              </Button>
            </Tooltip>
            <Tooltip title="下载图片">
              <Button 
                type="primary"
                icon={<DownloadOutlined />} 
                onClick={handleDownloadImage}
                style={{
                  background: 'rgba(0, 212, 255, 0.2)',
                  borderColor: '#00d4ff',
                  color: '#00d4ff'
                }}
              >
                下载
              </Button>
            </Tooltip>
          </Space>
        ]}
        mask={true}
        maskStyle={{ backgroundColor: 'rgba(0, 0, 0, 0.8)' }}
      >
        <div style={{ textAlign: 'center', padding: '20px', background: '#1a1a1a' }}>
          <Image
            src={src}
            alt={alt}
            preview={false}
            style={{
              maxWidth: '80vw',
              maxHeight: '70vh',
              objectFit: 'contain',
              borderRadius: '8px'
            }}
          />
        </div>
      </Modal>


    </>
  );
};

export default ImagePreview; 