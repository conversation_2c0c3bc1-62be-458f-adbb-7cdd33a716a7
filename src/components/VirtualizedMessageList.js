import React, { memo, useMemo, useCallback, useRef, useEffect } from 'react';
import { VariableSizeList as List } from 'react-window';
import MessageItem from './MessageItem';
import '../styles/messageItem.css';

// 🚀 虚拟化消息列表组件 - 解决大量消息时的性能问题
const VirtualizedMessageList = memo(({ 
  messages, 
  showThinking, 
  containerHeight = 400,
  itemHeight = 150, // 预估每个消息项的高度
  onScroll
}) => {
  const listRef = useRef();
  const messagesEndRef = useRef();

  // 🎯 缓存消息数据，避免不必要的重新计算
  const messageData = useMemo(() => ({
    messages,
    showThinking
  }), [messages, showThinking]);

  // 🎯 渲染单个消息项的回调函数
  const renderMessageItem = useCallback(({ index, style, data }) => {
    const { messages, showThinking } = data;
    const msg = messages[index];
    
    if (!msg) return null;

    const isUser = msg.sender === 'user';
    const isSystem = msg.sender === 'system';
    const isStreaming = msg.isStreaming;

    return (
      <div style={style}>
        <MessageItem
          msg={msg}
          index={index}
          isUser={isUser}
          isSystem={isSystem}
          isStreaming={isStreaming}
          showThinking={showThinking}
        />
      </div>
    );
  }, []);

  // 🎯 自动滚动到底部
  const scrollToBottom = useCallback(() => {
    if (listRef.current && messages.length > 0) {
      listRef.current.scrollToItem(messages.length - 1, 'end');
    }
  }, [messages.length]);

  // 🎯 当有新消息时自动滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [scrollToBottom]);

  // 🎯 处理滚动事件
  const handleScroll = useCallback(({ scrollDirection, scrollOffset, scrollUpdateWasRequested }) => {
    if (onScroll) {
      onScroll({ scrollDirection, scrollOffset, scrollUpdateWasRequested });
    }
  }, [onScroll]);

  // 🎯 动态计算消息项高度（可选的高级优化）
  const getItemSize = useCallback((index) => {
    const msg = messages[index];
    if (!msg) return itemHeight;

    // 根据消息类型和内容长度估算高度
    let estimatedHeight = 80; // 基础高度

    // 内容长度影响
    if (msg.content) {
      estimatedHeight += Math.min(msg.content.length * 0.5, 200);
    }

    // 思考过程额外高度
    if (msg.thinking && showThinking) {
      estimatedHeight += Math.min(msg.thinking.length * 0.3, 150);
    }

    // 系统消息通常较短
    if (msg.sender === 'system') {
      estimatedHeight = Math.min(estimatedHeight, 100);
    }

    return Math.max(estimatedHeight, 60); // 最小高度
  }, [messages, showThinking, itemHeight]);

  if (!messages || messages.length === 0) {
    return (
      <div style={{
        height: containerHeight,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'rgba(136, 146, 176, 0.8)',
        fontSize: '14px'
      }}>
        暂无消息
      </div>
    );
  }

  return (
    <div style={{ height: containerHeight, width: '100%' }}>
      <List
        ref={listRef}
        height={containerHeight}
        itemCount={messages.length}
        itemSize={getItemSize}
        itemData={messageData}
        onScroll={handleScroll}
        overscanCount={5} // 预渲染5个额外项目以提升滚动体验
        style={{
          // 自定义滚动条样式
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgba(0, 212, 255, 0.3) transparent'
        }}
      >
        {renderMessageItem}
      </List>
      
      {/* 隐藏的底部引用，用于非虚拟化场景的兼容 */}
      <div ref={messagesEndRef} style={{ height: 0, overflow: 'hidden' }} />
    </div>
  );
});

VirtualizedMessageList.displayName = 'VirtualizedMessageList';

export default VirtualizedMessageList;
