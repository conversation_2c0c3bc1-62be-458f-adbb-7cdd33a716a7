import React, { useState, useCallback } from 'react';
import { CopyOutlined, CheckOutlined } from '@ant-design/icons';
import { copyToClipboard, getCopyButtonStyle, getCopyButtonHoverStyle } from '../utils/copyUtils';

// 🎯 通用复制按钮组件
const CopyButton = ({ 
  text, 
  type = 'hover', // 'hover' | 'corner'
  successMessage = '已复制到剪贴板',
  className = '',
  style = {},
  onCopy = null,
  children = null
}) => {
  const [copied, setCopied] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // 🎯 处理复制操作
  const handleCopy = useCallback(async (e) => {
    e.stopPropagation(); // 防止事件冒泡
    
    if (!text) {
      console.warn('⚠️ [复制] 没有可复制的内容');
      return;
    }

    try {
      const success = await copyToClipboard(text, successMessage);
      
      if (success) {
        setCopied(true);
        
        // 调用回调函数
        if (onCopy) {
          onCopy(text, success);
        }
        
        // 2秒后重置状态
        setTimeout(() => {
          setCopied(false);
        }, 2000);
      }
    } catch (error) {
      console.error('❌ [复制] 复制操作失败:', error);
    }
  }, [text, successMessage, onCopy]);

  // 🎯 获取按钮样式
  const buttonStyle = {
    ...getCopyButtonStyle(type),
    ...style,
    ...(isHovered ? getCopyButtonHoverStyle() : {}),
    ...(copied ? {
      background: 'rgba(82, 196, 26, 0.1)',
      borderColor: 'rgba(82, 196, 26, 0.5)',
      color: '#52c41a'
    } : {})
  };

  return (
    <div
      className={`copy-button ${className}`}
      style={buttonStyle}
      onClick={handleCopy}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      title={copied ? '已复制!' : '复制内容'}
    >
      {copied ? (
        <>
          <CheckOutlined style={{ fontSize: '12px' }} />
          <span>已复制</span>
        </>
      ) : (
        <>
          <CopyOutlined style={{ fontSize: '12px' }} />
          <span>{children || '复制'}</span>
        </>
      )}
    </div>
  );
};

// 🎯 消息悬浮复制按钮
export const MessageCopyButton = ({ content, className = '' }) => {
  return (
    <CopyButton
      text={content}
      type="hover"
      successMessage="消息已复制到剪贴板"
      className={`message-copy-btn ${className}`}
      onCopy={(text, success) => {
        console.log('📋 [复制] 消息复制:', success ? '成功' : '失败', `长度: ${text.length}`);
      }}
    >
      复制消息
    </CopyButton>
  );
};

// 🎯 代码块复制按钮
export const CodeCopyButton = ({ code, language = '', className = '' }) => {
  return (
    <CopyButton
      text={code}
      type="corner"
      successMessage={`${language ? language.toUpperCase() + ' ' : ''}代码已复制到剪贴板`}
      className={`code-copy-btn ${className}`}
      style={{
        fontSize: '11px',
        padding: '3px 6px',
        minWidth: 'auto'
      }}
      onCopy={(text, success) => {
        console.log('📋 [复制] 代码复制:', success ? '成功' : '失败', `语言: ${language}, 长度: ${text.length}`);
      }}
    >
      复制
    </CopyButton>
  );
};

// 🎯 思考过程复制按钮
export const ThinkingCopyButton = ({ content, className = '' }) => {
  return (
    <CopyButton
      text={content}
      type="corner"
      successMessage="思考过程已复制到剪贴板"
      className={`thinking-copy-btn ${className}`}
      style={{
        background: 'rgba(138, 43, 226, 0.1)',
        borderColor: 'rgba(138, 43, 226, 0.3)',
        color: '#8a2be2'
      }}
      onCopy={(text, success) => {
        console.log('📋 [复制] 思考过程复制:', success ? '成功' : '失败', `长度: ${text.length}`);
      }}
    >
      复制思考
    </CopyButton>
  );
};

export default CopyButton;
