import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Badge,
  Button,
  Tooltip,
  Progress,
  Space,
  Typography,
  Alert,
  Collapse,
  Tag,
  Divider
} from 'antd';
import {
  ThunderboltOutlined,
  DatabaseOutlined,
  FileTextOutlined,
  BranchesOutlined,
  ShareAltOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';

const { Text, Title } = Typography;
const { Panel } = Collapse;

/**
 * 🚀 优化状态面板组件
 * 显示所有优化功能的状态和性能统计
 */
const OptimizationStatusPanel = ({ 
  optimizationStatus, 
  performanceStats, 
  onRefresh,
  style = {} 
}) => {
  const [refreshing, setRefreshing] = useState(false);

  // 刷新状态
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await onRefresh?.();
    } finally {
      setRefreshing(false);
    }
  };

  // 获取功能状态图标和颜色
  const getFeatureStatus = (available) => {
    if (available) {
      return {
        icon: <CheckCircleOutlined />,
        color: 'success',
        text: '可用'
      };
    } else {
      return {
        icon: <CloseCircleOutlined />,
        color: 'default',
        text: '不可用'
      };
    }
  };

  // 渲染功能状态卡片
  const renderFeatureCard = (title, icon, available, description) => {
    const status = getFeatureStatus(available);
    
    return (
      <Card 
        size="small" 
        style={{ 
          background: available ? 'linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(138, 43, 226, 0.05) 100%)' : 'rgba(0,0,0,0.02)',
          border: available ? '1px solid rgba(0, 212, 255, 0.2)' : '1px solid rgba(0,0,0,0.1)'
        }}
      >
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <Space>
            {icon}
            <Text strong>{title}</Text>
            <Badge 
              status={status.color} 
              text={status.text}
            />
          </Space>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {description}
          </Text>
        </Space>
      </Card>
    );
  };

  if (!optimizationStatus) {
    return (
      <Card title="🚀 优化功能状态" loading style={style}>
        <Text>正在加载优化状态...</Text>
      </Card>
    );
  }

  return (
    <Card 
      title={
        <Space>
          <ThunderboltOutlined style={{ color: '#1890ff' }} />
          <span>优化功能状态</span>
          <Badge 
            status={optimizationStatus.available ? 'success' : 'default'} 
            text={optimizationStatus.available ? '优化模式' : '基础模式'}
          />
        </Space>
      }
      extra={
        <Button 
          size="small" 
          icon={<SettingOutlined />} 
          loading={refreshing}
          onClick={handleRefresh}
        >
          刷新
        </Button>
      }
      style={style}
    >
      {/* 总体状态提示 */}
      {optimizationStatus.available ? (
        <Alert
          message="🚀 优化功能已启用"
          description="主进程服务正在运行，享受更快的向量搜索、智能缓存和高级功能。"
          type="success"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <Alert
          message="📝 基础模式运行"
          description="优化服务不可用，系统将使用基础功能。所有核心功能仍然正常工作。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 功能状态网格 */}
      <Row gutter={[12, 12]} style={{ marginBottom: 16 }}>
        <Col span={12}>
          {renderFeatureCard(
            '向量搜索',
            <DatabaseOutlined style={{ color: '#52c41a' }} />,
            optimizationStatus.features.vectorSearch,
            'Worker Threads并行计算'
          )}
        </Col>
        <Col span={12}>
          {renderFeatureCard(
            '智能缓存',
            <ThunderboltOutlined style={{ color: '#1890ff' }} />,
            optimizationStatus.features.cache,
            '多层缓存优化'
          )}
        </Col>
        <Col span={12}>
          {renderFeatureCard(
            '文档处理',
            <FileTextOutlined style={{ color: '#fa8c16' }} />,
            optimizationStatus.features.documentProcessor,
            '多媒体文档解析'
          )}
        </Col>
        <Col span={12}>
          {renderFeatureCard(
            '版本管理',
            <BranchesOutlined style={{ color: '#722ed1' }} />,
            optimizationStatus.features.versionManager,
            '文档版本控制'
          )}
        </Col>
      </Row>

      {/* 性能统计 */}
      {performanceStats && (
        <Collapse ghost>
          <Panel 
            header={
              <Space>
                <InfoCircleOutlined />
                <Text strong>性能统计</Text>
              </Space>
            } 
            key="performance"
          >
            {performanceStats.local && (
              <>
                <Title level={5}>本地统计</Title>
                <Row gutter={16} style={{ marginBottom: 16 }}>
                  <Col span={6}>
                    <Statistic
                      title="平均搜索时间"
                      value={performanceStats.local.avgSearchTime}
                      suffix="ms"
                      precision={1}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="总搜索次数"
                      value={performanceStats.local.totalSearches}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="缓存命中率"
                      value={performanceStats.local.cacheHitRate}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="主进程使用"
                      value={performanceStats.local.mainProcessUsage}
                      suffix="次"
                    />
                  </Col>
                </Row>
              </>
            )}

            {performanceStats.service && (
              <>
                <Divider />
                <Title level={5}>服务状态</Title>
                <Row gutter={16}>
                  {Object.entries(performanceStats.service).map(([serviceName, serviceInfo]) => (
                    <Col span={8} key={serviceName}>
                      <Card size="small">
                        <Space direction="vertical" size="small">
                          <Text strong>{serviceName}</Text>
                          <Badge 
                            status={serviceInfo.initialized ? 'success' : 'error'} 
                            text={serviceInfo.initialized ? '运行中' : '未初始化'}
                          />
                          {serviceInfo.stats && (
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              统计可用
                            </Text>
                          )}
                        </Space>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </>
            )}
          </Panel>
        </Collapse>
      )}

      {/* 功能说明 */}
      <Collapse ghost style={{ marginTop: 16 }}>
        <Panel 
          header={
            <Space>
              <InfoCircleOutlined />
              <Text strong>功能说明</Text>
            </Space>
          } 
          key="info"
        >
          <Space direction="vertical" size="small">
            <div>
              <Tag color="green">向量搜索优化</Tag>
              <Text>使用Worker Threads并行计算，搜索速度提升5-10倍</Text>
            </div>
            <div>
              <Tag color="blue">智能缓存</Tag>
              <Text>多层缓存策略，减少重复计算，提升响应速度</Text>
            </div>
            <div>
              <Tag color="orange">文档处理</Tag>
              <Text>支持PDF、Word、Excel、图片OCR等多种格式</Text>
            </div>
            <div>
              <Tag color="purple">版本管理</Tag>
              <Text>文档版本控制、差异对比、历史恢复功能</Text>
            </div>
            <div>
              <Tag color="cyan">知识图谱</Tag>
              <Text>自动发现文档关联、构建知识网络</Text>
            </div>
          </Space>
        </Panel>
      </Collapse>
    </Card>
  );
};

export default OptimizationStatusPanel;
