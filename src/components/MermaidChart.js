import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Button, Modal, message, Space, Tooltip } from 'antd';
import { EyeOutlined, DownloadOutlined, CopyOutlined, PictureOutlined } from '@ant-design/icons';
import mermaid from 'mermaid';
import html2canvas from 'html2canvas';

// 🎨 Mermaid图表组件
const MermaidChart = ({ code, title = 'Mermaid 图表' }) => {
  const chartRef = useRef(null);
  const previewRef = useRef(null);
  const [chartId] = useState(`mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  const [isRendered, setIsRendered] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [svgContent, setSvgContent] = useState('');
  const [error, setError] = useState(null);

  // 初始化Mermaid配置
  useEffect(() => {
    // 确保在浏览器环境中运行
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }
    
    try {
      mermaid.initialize({
        startOnLoad: false,
        theme: 'base',
        suppressErrorRendering: true,  // 🎯 禁用错误渲染
        logLevel: 'fatal',  // 🎯 只显示致命错误
        deterministicIds: true,  // 🎯 确定性ID
        themeVariables: {
          background: '#1a1a1a',
          primaryColor: '#00d4ff',
          primaryTextColor: '#ffffff',
          primaryBorderColor: '#00d4ff',
          lineColor: '#00d4ff',
          secondaryColor: '#722ed1',
          tertiaryColor: '#ffffff',
          mainBkg: '#2a2a2a',
          secondBkg: '#3a3a3a',
          tertiaryBkg: '#1a1a1a',
          nodeBkg: '#2a2a2a',
          nodeBorder: '#00d4ff',
          clusterBkg: '#3a3a3a',
          clusterBorder: '#00d4ff',
          defaultLinkColor: '#00d4ff',
          titleColor: '#ffffff',
          edgeLabelBackground: '#1a1a1a',
          actorBorder: '#00d4ff',
          actorBkg: '#2a2a2a',
          actorTextColor: '#ffffff',
          actorLineColor: '#00d4ff',
          signalColor: '#ffffff',
          signalTextColor: '#ffffff',
          c0: '#00d4ff',
          c1: '#722ed1',
          c2: '#52c41a',
          c3: '#faad14'
        },
        flowchart: {
          htmlLabels: true,
          curve: 'basis',
          useMaxWidth: true
        },
        sequence: {
          diagramMarginX: 50,
          diagramMarginY: 10,
          actorMargin: 50,
          width: 150,
          height: 65,
          boxMargin: 10,
          boxTextMargin: 5,
          noteMargin: 10,
          messageMargin: 35,
          useMaxWidth: true
        },
        gantt: {
          titleTopMargin: 25,
          barHeight: 20,
          fontsize: 11,
          gridLineStartPadding: 35,
          bottomPadding: 25,
          useMaxWidth: true
        },
        securityLevel: 'loose'
      });
      console.log('🎯 [Mermaid] 初始化完成');
    } catch (err) {
      console.error('🚨 [Mermaid] 初始化失败:', err);
    }
  }, []);

  // 渲染Mermaid图表
  const renderChart = useCallback(async () => {
    if (!chartRef.current || !code?.trim()) {
      console.log('🎯 [Mermaid] 没有容器或代码为空');
      return;
    }

    try {
      setError(null);
      console.log('🎯 [Mermaid] 开始渲染图表，代码:', code);
      
      // 清理之前的内容
      chartRef.current.innerHTML = '<div style="color: #00d4ff; text-align: center; padding: 20px;">正在渲染图表...</div>';
      
      // 直接渲染，不做预先验证
      const { svg } = await mermaid.render(chartId, code);
      console.log('🎯 [Mermaid] 渲染成功，SVG长度:', svg.length);
      
      // 🎯 使用官方配置禁用错误信息，无需手动清理
      console.log('🎯 [Mermaid] 使用官方配置已禁用错误信息');
      
      chartRef.current.innerHTML = svg;
      setSvgContent(svg);
      setIsRendered(true);
      
      // 确保SVG样式正确
      const svgElement = chartRef.current.querySelector('svg');
      if (svgElement) {
        svgElement.style.maxWidth = '100%';
        svgElement.style.height = 'auto';
        svgElement.style.overflow = 'hidden';
        console.log('🎯 [Mermaid] SVG样式已设置');
      }
    } catch (err) {
      console.error('🚨 [Mermaid] 渲染错误:', err);
      setError(err.message || '图表渲染失败');
      chartRef.current.innerHTML = `
        <div style="
          padding: 20px;
          border: 2px dashed #ff4d4f;
          border-radius: 8px;
          color: #ff4d4f;
          text-align: center;
          background: rgba(255, 77, 79, 0.1);
        ">
          <div style="font-weight: bold; margin-bottom: 8px;">🚫 图表渲染失败</div>
          <div style="font-size: 12px;">${err.message || '请检查Mermaid语法'}</div>
          <div style="font-size: 11px; margin-top: 8px; opacity: 0.8;">代码: ${code.substring(0, 100)}...</div>
        </div>
      `;
      setIsRendered(false);
    }
  }, [code, chartId]);

  // 组件挂载时渲染图表
  useEffect(() => {
    renderChart();
  }, [renderChart]);

  // 复制Mermaid代码
  const handleCopyCode = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(code);
      message.success('Mermaid代码已复制到剪贴板');
    } catch (err) {
      message.error('复制失败');
    }
  }, [code]);

  // 保存为图片
  const handleSaveImage = useCallback(async () => {
    if (!chartRef.current || !isRendered) {
      message.error('图表未正确渲染，无法保存');
      return;
    }

    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: '#1a1a1a',
        scale: 2, // 提高清晰度
        logging: false
      });
      
      // 创建下载链接
      const link = document.createElement('a');
      link.download = `mermaid-chart-${Date.now()}.png`;
      link.href = canvas.toDataURL();
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      message.success('图表已保存为图片');
    } catch (err) {
      console.error('保存图片失败:', err);
      message.error('保存图片失败');
    }
  }, [isRendered]);

  // 预览图表
  const handlePreview = useCallback(() => {
    if (!isRendered) {
      message.error('图表未正确渲染');
      return;
    }
    setPreviewVisible(true);
  }, [isRendered]);

  return (
    <div style={{ margin: '16px 0' }}>
      {/* 图表容器 */}
      <div
        style={{
          background: 'rgba(26, 31, 46, 0.9)',
          border: '1px solid rgba(0, 212, 255, 0.3)',
          borderRadius: '12px',
          padding: '20px',
          position: 'relative',
          overflow: 'auto'
        }}
      >
        {/* 操作按钮组 */}
        <div
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            zIndex: 10
          }}
        >
          <Space size="small">
            <Tooltip title="预览图表">
              <Button
                type="text"
                icon={<EyeOutlined />}
                size="small"
                onClick={handlePreview}
                disabled={!isRendered}
                style={{
                  color: '#00d4ff',
                  border: '1px solid rgba(0, 212, 255, 0.3)',
                  background: 'rgba(0, 212, 255, 0.1)'
                }}
              />
            </Tooltip>
            <Tooltip title="保存为图片">
              <Button
                type="text"
                icon={<DownloadOutlined />}
                size="small"
                onClick={handleSaveImage}
                disabled={!isRendered}
                style={{
                  color: '#52c41a',
                  border: '1px solid rgba(82, 196, 26, 0.3)',
                  background: 'rgba(82, 196, 26, 0.1)'
                }}
              />
            </Tooltip>
            <Tooltip title="复制代码">
              <Button
                type="text"
                icon={<CopyOutlined />}
                size="small"
                onClick={handleCopyCode}
                style={{
                  color: '#722ed1',
                  border: '1px solid rgba(114, 46, 209, 0.3)',
                  background: 'rgba(114, 46, 209, 0.1)'
                }}
              />
            </Tooltip>
          </Space>
        </div>

        {/* 标题 */}
        <div
          style={{
            fontSize: '14px',
            color: '#00d4ff',
            marginBottom: '12px',
            fontWeight: '600',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <PictureOutlined />
          {title}
        </div>

        {/* 图表内容 */}
        <div
          ref={chartRef}
          style={{
            textAlign: 'center',
            minHeight: '100px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          className="mermaid-chart-container"
        />
        
        {/* 基础样式 */}
        <style>{`
          .mermaid-chart-container {
            overflow: hidden;
          }
          
          .mermaid-chart-container svg {
            overflow: hidden;
            max-width: 100%;
            height: auto;
          }
        `}</style>
      </div>

      {/* 预览模态框 */}
      <Modal
        title={
          <div style={{ color: '#00d4ff', display: 'flex', alignItems: 'center', gap: '8px' }}>
            <PictureOutlined />
            图表预览
          </div>
        }
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width="90vw"
        style={{ top: 20 }}
        footer={[
          <Button key="copy" icon={<CopyOutlined />} onClick={handleCopyCode}>
            复制代码
          </Button>,
          <Button key="download" type="primary" icon={<DownloadOutlined />} onClick={handleSaveImage}>
            保存图片
          </Button>
        ]}
      >
        <div
          ref={previewRef}
          style={{
            background: '#1a1a1a',
            padding: '20px',
            borderRadius: '8px',
            textAlign: 'center',
            maxHeight: '70vh',
            overflow: 'auto'
          }}
          className="mermaid-preview-container"
          dangerouslySetInnerHTML={{ __html: svgContent }}
        />
        
        {/* 预览样式 */}
        <style>{`
          .mermaid-preview-container svg {
            overflow: hidden;
            max-width: 100%;
            height: auto;
          }
        `}</style>
      </Modal>
    </div>
  );
};

export default MermaidChart; 