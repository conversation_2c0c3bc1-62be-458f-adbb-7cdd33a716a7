import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Space,
  message,
  Popconfirm,
  Tag,
  Progress,
  Select,
  Typography,
  Divider,
  List,
  Tooltip,
  InputNumber,
  Switch,
  Tabs,
  Alert,
  Radio,
  Upload
} from 'antd';
import { APIManager } from '../utils/apiManager'; // 🚀 新增：导入APIManager
import {
  isOptimizedFeaturesAvailable,
  getOptimizationStatus,
  optimizedVectorSearch,
  smartCache,
  documentProcessor,
  versionManager,
  performanceMonitor
} from '../utils/optimizedFeatures'; // 🚀 新增：优化功能
import OptimizationStatusPanel from './OptimizationStatusPanel'; // 🚀 新增：优化状态面板
import DocumentVersionManager from './DocumentVersionManager'; // 🚀 新增：版本管理
import DeveloperDebugPanel from './DeveloperDebugPanel'; // 🚀 新增：调试面板
import DocumentProcessorInterface from './DocumentProcessorInterface'; // 🚀 新增：文档处理界面
import KnowledgeGraphVisualization from './KnowledgeGraphVisualization'; // 🚀 新增：知识图谱
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  FileTextOutlined,
  SearchOutlined,
  DatabaseOutlined,
  BulbOutlined,
  SettingOutlined,
  SaveOutlined,
  ShareAltOutlined,
  BugOutlined,
  HistoryOutlined,
  ThunderboltOutlined,
  InboxOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;
const { Text, Title } = Typography;

const KnowledgeBase = ({ dataManager }) => {
  // 🚀 新增：创建APIManager实例用于智能分块
  const apiManagerRef = React.useRef(new APIManager());

  const [knowledgeBases, setKnowledgeBases] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [documentModalVisible, setDocumentModalVisible] = useState(false);
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [editingKB, setEditingKB] = useState(null);
  const [selectedKB, setSelectedKB] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [searchResults, setSearchResults] = useState([]);
  const [embeddingModels, setEmbeddingModels] = useState([]);
  const [vectorStats, setVectorStats] = useState(null); // 🚀 新增：向量数据库统计

  // 🚀 新增：优化功能状态
  const [optimizationStatus, setOptimizationStatus] = useState(null);
  const [performanceStats, setPerformanceStats] = useState(null);
  const [showOptimizationPanel, setShowOptimizationPanel] = useState(false);

  // 🚀 新增：版本管理和调试面板状态
  const [versionManagerVisible, setVersionManagerVisible] = useState(false);
  const [debugPanelVisible, setDebugPanelVisible] = useState(false);
  const [currentDocumentForVersion, setCurrentDocumentForVersion] = useState(null);

  // 🚀 新增：文档处理和知识图谱状态
  const [documentProcessorVisible, setDocumentProcessorVisible] = useState(false);
  const [knowledgeGraphVisible, setKnowledgeGraphVisible] = useState(false);

  // 🚀 新增：文档创建方式状态
  const [documentCreationMode, setDocumentCreationMode] = useState('text'); // 'text' | 'file'
  const [uploadedFileContent, setUploadedFileContent] = useState(null);
  const [processingFile, setProcessingFile] = useState(false);
  const [supportedFormats, setSupportedFormats] = useState(null);

  // 🚀 新增：智能分块相关状态（默认推荐智能分块以处理多种内容类型）
  const [chunkingMethod, setChunkingMethod] = useState('intelligent'); // 'traditional' | 'intelligent'
  const [selectedChatModel, setSelectedChatModel] = useState(null);

  // 🚀 新增：统一进度弹框状态
  const [progressModalVisible, setProgressModalVisible] = useState(false);
  const [progressInfo, setProgressInfo] = useState({
    title: '',
    current: 0,
    total: 0,
    message: '',
    percent: 0
  });

  // 🚀 新增：页面状态管理
  const [currentView, setCurrentView] = useState('list'); // 'list', 'documents', 'detail', 'vectors'
  const [selectedKBForManagement, setSelectedKBForManagement] = useState(null);
  const [selectedDocument, setSelectedDocument] = useState(null);

  // 🚀 新增：编辑状态管理
  const [editingDocument, setEditingDocument] = useState(null);
  const [documentEditModalVisible, setDocumentEditModalVisible] = useState(false);
  const [chunkEditModalVisible, setChunkEditModalVisible] = useState(false);
  const [editingChunk, setEditingChunk] = useState(null);
  const [editingChunkIndex, setEditingChunkIndex] = useState(-1);

  const [form] = Form.useForm();
  const [documentForm] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [documentEditForm] = Form.useForm();
  const [chunkEditForm] = Form.useForm();

  // 🚀 新增：加载优化状态
  const loadOptimizationStatus = async () => {
    try {
      const status = getOptimizationStatus();
      setOptimizationStatus(status);

      if (status.available) {
        const stats = await performanceMonitor.getStats();
        setPerformanceStats(stats);
        console.log('✅ [KnowledgeBase] 优化功能可用:', status);
      } else {
        console.log('📝 [KnowledgeBase] 使用基础模式');
      }
    } catch (error) {
      console.error('❌ [KnowledgeBase] 加载优化状态失败:', error);
    }
  };

  // 🚀 新增：处理文件上传
  const handleFileUpload = async (file) => {
    setProcessingFile(true);
    try {
      console.log('📄 [DocumentUpload] 开始处理文件:', file.name);

      // 动态获取支持的文件格式
      const supportedFormats = await documentProcessor.getSupportedFormats();
      const fileExt = '.' + file.name.split('.').pop().toLowerCase();

      if (!supportedFormats.all.includes(fileExt)) {
        message.error(`不支持的文件格式: ${fileExt}。${supportedFormats.note || ''}`);
        return;
      }

      // 使用文档处理器处理文件
      try {
        const result = await documentProcessor.processDocument(file, {
          enableOCR: true,
          extractMetadata: true
        });

        if (result && result.success && result.text) {
          const processedContent = {
            fileName: file.name,
            text: result.text,
            metadata: result.metadata,
            processingTime: result.metadata?.processingTime || 0
          };

          setUploadedFileContent(processedContent);
          documentForm.setFieldsValue({ content: result.text });

          message.success(`文件处理完成，提取了 ${result.text.length} 个字符`);
          console.log('✅ [DocumentUpload] 文件处理成功:', processedContent);
        } else {
          throw new Error('无法提取文件内容');
        }
      } catch (error) {
        console.warn('⚠️ [DocumentUpload] 文档处理失败:', error);
        message.error(`文件处理失败: ${error.message}`);
      }
    } catch (error) {
      console.error('❌ [DocumentUpload] 文件上传失败:', error);
      message.error('文件上传失败');
    } finally {
      setProcessingFile(false);
    }
  };

  // 🚀 新增：加载支持的文件格式
  const loadSupportedFormats = async () => {
    try {
      const formats = await documentProcessor.getSupportedFormats();
      setSupportedFormats(formats);
      console.log('📄 [KnowledgeBase] 支持的文件格式:', formats);
    } catch (error) {
      console.error('❌ [KnowledgeBase] 加载支持格式失败:', error);
    }
  };

  // 加载数据
  useEffect(() => {
    loadKnowledgeBases();
    loadEmbeddingModels();
    loadVectorStats(); // 🚀 新增：加载向量统计
    loadOptimizationStatus(); // 🚀 新增：加载优化状态
    loadSupportedFormats(); // 🚀 新增：加载支持格式

    // 🚀 新增：为知识库管理器设置API管理器（用于智能分块）
    if (dataManager && dataManager.knowledgeBaseManager && apiManagerRef.current) {
      dataManager.knowledgeBaseManager.setAPIManager(apiManagerRef.current);
      console.log('✅ [KnowledgeBase] 已为知识库管理器设置APIManager');
    }
  }, [dataManager]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadKnowledgeBases = () => {
    if (dataManager) {
      const kbs = dataManager.getKnowledgeBases() || [];
      setKnowledgeBases(kbs);
    }
  };

  // 🚀 新增：完整刷新所有数据的函数
  const refreshAllData = () => {
    console.log('🔄 [KnowledgeBase] 开始刷新所有数据...');

    // 重新加载知识库列表
    loadKnowledgeBases();

    // 如果当前有选中的知识库，更新它
    if (selectedKB) {
      const updatedKBs = dataManager.getKnowledgeBases() || [];
      const updatedSelectedKB = updatedKBs.find(kb => kb.id === selectedKB.id);
      if (updatedSelectedKB) {
        setSelectedKB(updatedSelectedKB);
        console.log('✅ [KnowledgeBase] 已更新选中的知识库:', updatedSelectedKB.name);
      }
    }

    // 如果当前有管理中的知识库，更新它
    if (selectedKBForManagement) {
      const updatedKBs = dataManager.getKnowledgeBases() || [];
      const updatedKBForManagement = updatedKBs.find(kb => kb.id === selectedKBForManagement.id);
      if (updatedKBForManagement) {
        setSelectedKBForManagement(updatedKBForManagement);
        console.log('✅ [KnowledgeBase] 已更新管理中的知识库:', updatedKBForManagement.name);

        // 如果当前有选中的文档，也需要更新
        if (selectedDocument) {
          const updatedDocument = updatedKBForManagement.documents?.find(doc => doc.id === selectedDocument.id);
          if (updatedDocument) {
            setSelectedDocument(updatedDocument);
            console.log('✅ [KnowledgeBase] 已更新选中的文档:', updatedDocument.title);
          }
        }
      }
    }

    // 重新加载向量统计
    loadVectorStats();

    console.log('✅ [KnowledgeBase] 数据刷新完成');
  };

  const loadEmbeddingModels = () => {
    if (dataManager) {
      const configs = dataManager.getConfigs();
      const models = [];
      configs.forEach(config => {
        if (config.models) {
          config.models.forEach(model => {
            if (model.type === 'embedding') {
              models.push({
                ...model,
                configId: config.id,
                baseUrl: config.baseUrl,
                apiKey: config.apiKey
              });
            }
          });
        }
      });
      setEmbeddingModels(models);
    }
  };

  // 🚀 新增：获取可用的Chat模型（用于智能分块）
  const getChatModels = () => {
    if (dataManager) {
      const allModels = dataManager.getAllModels();
      return allModels.filter(model => model.type !== 'embedding');
    }
    return [];
  };

  // 🚀 新增：统一的进度更新函数
  const updateProgress = (title, current, total, message) => {
    const percent = total > 0 ? Math.round((current / total) * 100) : 0;
    setProgressInfo({
      title,
      current,
      total,
      message,
      percent
    });

    if (!progressModalVisible) {
      setProgressModalVisible(true);
    }
  };

  // 🚀 新增：关闭进度弹框
  const closeProgress = () => {
    setProgressModalVisible(false);
    setProgressInfo({
      title: '',
      current: 0,
      total: 0,
      message: '',
      percent: 0
    });
  };

  // 🚀 新增：加载向量数据库统计信息
  const loadVectorStats = async () => {
    if (dataManager) {
      try {
        const stats = await dataManager.getVectorDBStats();
        setVectorStats(stats);
      } catch (error) {
        console.warn('⚠️ [KnowledgeBase] 无法获取向量统计:', error);
      }
    }
  };

  // 知识库表格列定义
  const kbColumns = [
    {
      title: '知识库名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text) => (
        <Space>
          <DatabaseOutlined style={{ color: '#1890ff' }} />
          <Text strong>{text}</Text>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 250,
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '向量模型',
      dataIndex: 'embeddingModel',
      key: 'embeddingModel',
      width: 150,
      render: (modelId) => {
        const model = embeddingModels.find(m => m.id === modelId);
        return model ? <Tag color="orange">{model.name}</Tag> : <Tag>未配置</Tag>;
      },
    },
    {
      title: '文档数量',
      dataIndex: 'documents',
      key: 'documentCount',
      width: 120,
      render: (documents, record) => (
        <Button
          type="link"
          style={{ padding: 0 }}
          onClick={() => openDocumentManagement(record)}
        >
          <Tag color="blue" style={{ cursor: 'pointer' }}>
            {documents ? documents.length : 0} 个文档
          </Tag>
        </Button>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (time) => new Date(time).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small" wrap>
          <Tooltip title="搜索知识库">
            <Button
              type="text"
              size="small"
              icon={<SearchOutlined />}
              onClick={() => openSearchModal(record)}
              style={{ color: '#1890ff' }}
            />
          </Tooltip>
          <Tooltip title="添加文档">
            <Button
              type="text"
              size="small"
              icon={<UploadOutlined />}
              onClick={() => openDocumentModal(record)}
              style={{ color: '#52c41a' }}
            />
          </Tooltip>
          <Tooltip title="文档管理">
            <Button
              type="text"
              size="small"
              icon={<FileTextOutlined />}
              onClick={() => openDocumentManagement(record)}
              style={{ color: '#722ed1' }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => editKnowledgeBase(record)}
              style={{ color: '#fa8c16' }}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除此知识库吗？"
            description="删除后将无法恢复，包括所有文档和向量数据。"
            onConfirm={() => deleteKnowledgeBase(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 添加/编辑知识库
  const handleKBSubmit = async (values) => {
    try {
      setLoading(true);
      
      const kbData = {
        ...values,
        id: editingKB ? editingKB.id : `kb_${Date.now()}`,
        createdAt: editingKB ? editingKB.createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        documents: editingKB ? editingKB.documents : []
      };
      
      if (editingKB) {
        dataManager.updateKnowledgeBase(kbData);
        message.success('知识库更新成功');
      } else {
        dataManager.addKnowledgeBase(kbData);
        message.success('知识库创建成功');
      }
      
      loadKnowledgeBases();
      setModalVisible(false);
      setEditingKB(null);
      form.resetFields();
    } catch (error) {
      message.error('操作失败：' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 编辑知识库
  const editKnowledgeBase = (kb) => {
    setEditingKB(kb);
    form.setFieldsValue(kb);
    setModalVisible(true);
  };

  // 删除知识库（包括向量数据）
  const deleteKnowledgeBase = async (kbId) => {
    try {
      setLoading(true);

      // 🚀 使用新的删除方法，同时删除向量数据
      await dataManager.deleteKnowledgeBaseWithVectors(kbId);

      // 🚀 修复：使用统一的刷新函数
      refreshAllData();

      // 清除选中状态
      setSelectedKB(null);
      setSelectedKBForManagement(null);
      setSelectedDocument(null);
      setCurrentView('list');

      message.success('知识库及其向量数据删除成功');
    } catch (error) {
      console.error('❌ [KnowledgeBase] 删除失败:', error);
      message.error('删除失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 打开文档上传模态框
  const openDocumentModal = (kb) => {
    setSelectedKB(kb);
    setDocumentModalVisible(true);
  };

  // 打开搜索模态框
  const openSearchModal = (kb) => {
    setSelectedKB(kb);
    setSearchModalVisible(true);
  };

  // 🚀 新增：打开文档管理页面
  const openDocumentManagement = (kb) => {
    setSelectedKBForManagement(kb);
    setCurrentView('documents');
  };

  // 🚀 新增：打开文档详情页面
  const openDocumentDetail = (document, kb) => {
    setSelectedDocument(document);
    setSelectedKBForManagement(kb);
    setCurrentView('detail');
  };

  // 🚀 新增：打开向量数据页面
  const openVectorData = (kb) => {
    setSelectedKBForManagement(kb);
    setCurrentView('vectors');
    // 自动加载向量统计信息
    loadVectorStats();
  };

  // 🚀 新增：返回主列表
  const backToList = () => {
    setCurrentView('list');
    setSelectedKBForManagement(null);
    setSelectedDocument(null);
  };

  // 🚀 新增：编辑文档
  const editDocument = (document) => {
    setEditingDocument(document);

    // 🚀 修复：设置分块方式状态
    const docChunkingMethod = document.chunkingMethod || 'traditional';
    setChunkingMethod(docChunkingMethod);
    setSelectedChatModel(document.chatModel || null);

    documentEditForm.setFieldsValue({
      title: document.title,
      content: document.content,
      embeddingModel: document.embeddingModel || selectedKBForManagement?.embeddingModel || '',
      // 🚀 新增：分块方式配置
      chunkingMethod: docChunkingMethod,
      chatModel: document.chatModel || null,
      // 分块配置
      hierarchical: document.hierarchical || false,
      chunkSize: document.chunkSize || 500,
      chunkOverlap: document.chunkOverlap || 50,
      parentChunkSize: document.parentChunkSize || 800,
      childChunkSize: document.childChunkSize || 200,
      // 分割符配置
      separatorType: document.separatorType || 'smart',
      customSeparator: document.customSeparator || '\n\n',
      // 元数据配置
      tags: document.metadata?.tags || [],
      category: document.metadata?.category || '',
      author: document.metadata?.author || '',
      importance: document.metadata?.importance || 'medium',
      source: document.metadata?.source || '',
      language: document.metadata?.language || 'zh-CN',
      // 处理选项
      autoReprocess: false
    });
    setDocumentEditModalVisible(true);
  };

  // 🚀 新增：编辑分块
  const editChunk = (chunk, index) => {
    setEditingChunk(chunk);
    setEditingChunkIndex(index);
    chunkEditForm.setFieldsValue({
      text: chunk.text,
      reprocessVector: false
    });
    setChunkEditModalVisible(true);
  };

  // 🚀 新增：编辑文档分块
  const editDocumentChunks = (document) => {
    // 可以打开一个专门的分块编辑界面
    message.info('分块编辑功能：可以在下方列表中单独编辑每个分块');
  };

  // 🚀 新增：删除分块
  const deleteChunk = async (chunk, index) => {
    try {
      setLoading(true);

      // 从文档中删除分块
      const updatedChunks = selectedDocument.chunks.filter((_, i) => i !== index);
      const updatedDocument = {
        ...selectedDocument,
        chunks: updatedChunks
      };

      // 如果有向量ID，删除向量数据
      if (chunk.vectorId) {
        await dataManager.deleteVector(selectedKBForManagement.id, chunk.vectorId);
      }

      // 更新文档
      await dataManager.updateDocument(selectedKBForManagement.id, updatedDocument);

      // 🚀 修复：使用统一的刷新函数
      refreshAllData();

      message.success('分块删除成功');
    } catch (error) {
      console.error('❌ [KnowledgeBase] 删除分块失败:', error);
      message.error('删除分块失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 文档上传处理（使用真实向量化）
  const handleDocumentUpload = async (values) => {
    try {
      setLoading(true);
      setUploadProgress(0);

      // 🚀 修复：使用统一进度显示
      updateProgress('文档处理', 0, 100, '准备处理文档...');

      const {
        title,
        content,
        embeddingModel,
        chunkSize,
        chunkOverlap,
        autoVectorize,
        // 🚀 新增：智能分块字段
        chunkingMethod,
        chatModel,
        hierarchical,
        parentChunkSize,
        childChunkSize,
        separatorType,
        customSeparator,
        // 元数据字段
        tags,
        category,
        author,
        importance,
        source,
        language
      } = values;

      // 🚀 修复：根据创建方式获取正确的内容
      let documentContent = content;
      if (documentCreationMode === 'file' && uploadedFileContent) {
        documentContent = uploadedFileContent.text;
        console.log('📄 [DocumentUpload] 使用文件上传内容:', documentContent?.length, '字符');
      }

      // 验证文档内容
      if (!documentContent || typeof documentContent !== 'string' || documentContent.trim().length === 0) {
        console.error('❌ [DocumentUpload] 文档内容验证失败:', {
          documentContent: typeof documentContent,
          contentLength: documentContent?.length,
          creationMode: documentCreationMode,
          uploadedFileContent: uploadedFileContent ? 'exists' : 'null',
          formContent: content ? content.length : 'null'
        });
        throw new Error('文档内容不能为空');
      }

      console.log('✅ [DocumentUpload] 文档内容验证通过:', {
        contentLength: documentContent.length,
        creationMode: documentCreationMode,
        title: title
      });

      const document = {
        id: `doc_${Date.now()}`,
        title,
        content: documentContent,
        // 🚀 新增：智能分块配置
        chunkingMethod: chunkingMethod || 'traditional',
        chatModel: chunkingMethod === 'intelligent' ? chatModel : null,
        hierarchical: hierarchical || false,
        chunkSize: chunkSize || 500,
        chunkOverlap: chunkOverlap || 50,
        parentChunkSize: parentChunkSize || 800,
        childChunkSize: childChunkSize || 200,
        separatorType: separatorType || 'smart',
        customSeparator: customSeparator || '\n\n',
        embeddingModel: embeddingModel || selectedKB.embeddingModel,
        // 元数据
        metadata: {
          tags: tags || [],
          category: category || '',
          author: author || '',
          importance: importance || 'medium',
          source: source || '',
          language: language || 'zh-CN',
          createdBy: 'user', // 可以从用户系统获取
          lastModified: new Date().toISOString()
        },
        createdAt: new Date().toISOString()
      };

      if (autoVectorize) {
        // 🚀 使用真实的向量化处理
        const result = await dataManager.addDocumentWithVectorization(
          selectedKB.id,
          document,
          (progress) => {
            // 🚀 修复：使用统一进度显示，避免多个弹框
            const current = progress.current || 0;
            const total = progress.total || 100;
            const message = progress.message || '正在处理...';

            updateProgress('文档向量化', current, total, message);
            setUploadProgress(progress.percentage || Math.round((current / total) * 100));
            console.log(`📊 [KnowledgeBase] ${message}`);
          }
        );

        if (result.success) {
          // 🚀 修复：完成时更新进度并关闭弹框
          updateProgress('文档向量化', result.totalChunks, result.totalChunks, '向量化完成！');
          setUploadProgress(100);

          setTimeout(() => {
            closeProgress(); // 关闭进度弹框
            setDocumentModalVisible(false);
            setUploadProgress(0);
            documentForm.resetFields();

            // 🚀 新增：清理文件上传状态
            setDocumentCreationMode('text');
            setUploadedFileContent(null);
            setProcessingFile(false);

            // 🚀 修复：完整刷新页面数据
            refreshAllData();

            message.success(`文档添加成功！向量化了 ${result.vectorCount}/${result.totalChunks} 个文本块`);
          }, 1000);
        } else {
          closeProgress(); // 关闭进度弹框
          throw new Error('文档向量化失败');
        }
      } else {
        // 只添加文档，不进行向量化
        const newDoc = dataManager.addDocumentToKB(selectedKB.id, {
          ...document,
          vectorized: false,
          chunks: [],
          vectorCount: 0
        });

        if (newDoc) {
          setDocumentModalVisible(false);
          documentForm.resetFields();

          // 🚀 新增：清理文件上传状态
          setDocumentCreationMode('text');
          setUploadedFileContent(null);
          setProcessingFile(false);

          // 🚀 修复：完整刷新页面数据
          refreshAllData();

          message.success('文档添加成功！可稍后进行向量化处理');
        } else {
          throw new Error('文档添加失败');
        }
      }

    } catch (error) {
      console.error('❌ [KnowledgeBase] 文档处理失败:', error);
      closeProgress(); // 🚀 修复：错误时关闭进度弹框
      message.error('文档处理失败：' + error.message);
      setUploadProgress(0);
    } finally {
      setLoading(false);
    }
  };

  // 知识库搜索（使用真实向量搜索，支持分层搜索）
  const handleSearch = async (values) => {
    try {
      setLoading(true);
      const { query, searchLevel, threshold, includeContext } = values;

      console.log(`🔍 [KnowledgeBase] 开始分层向量搜索: "${query}", 级别: ${searchLevel}`);

      // 🚀 使用分层向量搜索
      const searchResult = await dataManager.vectorSearchKnowledgeBase(
        selectedKB.id,
        query,
        {
          limit: 5,
          threshold: threshold || 0.7,
          includeChunks: true,
          searchLevel: searchLevel || 'all',
          includeContext: includeContext !== false
        }
      );

      // 转换搜索结果格式以适配UI
      const results = searchResult.results.map(result => {
        // 查找对应的文档以获取完整信息
        const document = selectedKB.documents?.find(doc => doc.id === result.documentId);

        return {
          id: result.documentId,
          title: result.documentTitle,
          relevance: result.maxSimilarity,
          avgSimilarity: result.avgSimilarity,
          relevantChunks: result.relevantChunks,
          chunks: result.chunks || [],
          createdAt: document?.createdAt || Date.now(),
          // 生成摘要内容
          content: result.chunks ?
            result.chunks.slice(0, 2).map(chunk => chunk.text).join('\n\n') +
            (result.chunks.length > 2 ? '\n\n...' : '') :
            document?.content?.substring(0, 200) + '...' || '无内容预览'
        };
      });

      setSearchResults(results);

      const message_text = `找到 ${results.length} 个相关文档，共匹配 ${searchResult.totalVectorMatches} 个文本块`;
      message.success(message_text);

      console.log(`✅ [KnowledgeBase] 搜索完成:`, searchResult);

    } catch (error) {
      console.error('❌ [KnowledgeBase] 搜索失败:', error);
      message.error('搜索失败：' + error.message);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  // 🚀 新增：渲染文档管理页面
  const renderDocumentManagement = () => {
    if (!selectedKBForManagement) return null;

    const documents = selectedKBForManagement.documents || [];

    const documentColumns = [
      {
        title: '文档标题',
        dataIndex: 'title',
        key: 'title',
        width: 200,
        render: (text, record) => (
          <div>
            <Button
              type="link"
              onClick={() => openDocumentDetail(record, selectedKBForManagement)}
              style={{ padding: 0, textAlign: 'left' }}
            >
              <Space>
                <FileTextOutlined style={{ color: '#1890ff' }} />
                <Text strong>{text}</Text>
              </Space>
            </Button>
            {record.metadata?.tags && record.metadata.tags.length > 0 && (
              <div style={{ marginTop: '4px', marginLeft: '22px' }}>
                {record.metadata.tags.slice(0, 2).map(tag => (
                  <Tag key={tag} size="small" color="blue">{tag}</Tag>
                ))}
                {record.metadata.tags.length > 2 && (
                  <Tag size="small" color="default">+{record.metadata.tags.length - 2}</Tag>
                )}
              </div>
            )}
          </div>
        ),
      },
      {
        title: '分类/作者',
        key: 'metadata',
        width: 120,
        render: (_, record) => (
          <div>
            {record.metadata?.category && (
              <Tag color="green" size="small">{record.metadata.category}</Tag>
            )}
            {record.metadata?.author && (
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                作者: {record.metadata.author}
              </div>
            )}
            {record.metadata?.importance && (
              <div style={{ marginTop: '4px' }}>
                <Tag
                  color={
                    record.metadata.importance === 'high' ? 'red' :
                    record.metadata.importance === 'medium' ? 'orange' : 'green'
                  }
                  size="small"
                >
                  {record.metadata.importance === 'high' ? '高' :
                   record.metadata.importance === 'medium' ? '中' : '低'}
                </Tag>
              </div>
            )}
          </div>
        ),
      },
      {
        title: '向量化状态',
        dataIndex: 'vectorized',
        key: 'vectorized',
        width: 140,
        render: (vectorized, record) => (
          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              {vectorized ? (
                <Tag color="green">已向量化</Tag>
              ) : (
                <Tag color="orange">未向量化</Tag>
              )}
              {record.hierarchical && (
                <Tag color="purple" size="small">分层</Tag>
              )}
            </div>
            {record.vectorCount && (
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                {record.vectorCount} 个向量
              </div>
            )}
          </div>
        ),
      },
      {
        title: '分块数量',
        dataIndex: 'chunks',
        key: 'chunks',
        width: 120,
        render: (chunks, record) => (
          <div>
            <Tag color="blue">{chunks ? chunks.length : 0} 块</Tag>
            {record.hierarchical && (
              <div style={{ fontSize: '11px', color: '#666', marginTop: '4px' }}>
                父:{record.parentChunkCount || 0} 子:{record.childChunkCount || 0}
              </div>
            )}
          </div>
        ),
      },
      {
        title: '内容预览',
        dataIndex: 'content',
        key: 'content',
        ellipsis: {
          showTitle: false,
        },
        render: (content) => (
          <Tooltip placement="topLeft" title={content}>
            <Text type="secondary">
              {content ? content.substring(0, 100) + '...' : '无内容'}
            </Text>
          </Tooltip>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 150,
        render: (time) => new Date(time).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }),
      },
      {
        title: '操作',
        key: 'action',
        width: 150,
        fixed: 'right',
        render: (_, record) => (
          <Space size="small">
            <Tooltip title="查看详情">
              <Button
                type="text"
                size="small"
                icon={<FileTextOutlined />}
                onClick={() => openDocumentDetail(record, selectedKBForManagement)}
                style={{ color: '#1890ff' }}
              />
            </Tooltip>
            <Tooltip title="重新向量化">
              <Button
                type="text"
                size="small"
                icon={<BulbOutlined />}
                onClick={() => reprocessDocument(record)}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
            <Tooltip title="版本管理">
              <Button
                type="text"
                size="small"
                icon={<HistoryOutlined />}
                onClick={() => {
                  setCurrentDocumentForVersion({
                    id: record.id,
                    content: record.content
                  });
                  setVersionManagerVisible(true);
                }}
                style={{ color: '#722ed1' }}
              />
            </Tooltip>
            <Popconfirm
              title="确定删除此文档吗？"
              description="删除后将无法恢复，包括相关的向量数据。"
              onConfirm={() => deleteDocument(record)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        ),
      },
    ];

    return (
      <div style={{
        height: 'calc(100vh - 120px)',
        overflow: 'auto',
        padding: '0 4px'
      }}>
        <Card
          title={
            <Space>
              <Button
                type="text"
                icon={<DatabaseOutlined />}
                onClick={backToList}
                style={{ color: '#1890ff' }}
              >
                返回知识库列表
              </Button>
              <Divider type="vertical" />
              <FileTextOutlined style={{ color: '#1890ff' }} />
              <span>{selectedKBForManagement.name} - 文档管理</span>
            </Space>
          }
        extra={
          <Space>
            <Button
              icon={<BulbOutlined />}
              onClick={() => openVectorData(selectedKBForManagement)}
            >
              向量数据
            </Button>
            <Button
              icon={<ThunderboltOutlined />}
              onClick={() => setDocumentProcessorVisible(true)}
            >
              文档处理
            </Button>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={() => openDocumentModal(selectedKBForManagement)}
            >
              添加文档
            </Button>
          </Space>
        }
      >
        <Table
          columns={documentColumns}
          dataSource={documents}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          scroll={{ x: 1000 }}
          locale={{
            emptyText: '暂无文档，点击上方按钮添加文档'
          }}
        />
      </Card>
      </div>
    );
  };

  // 🚀 新增：删除文档
  const deleteDocument = async (document) => {
    try {
      setLoading(true);
      await dataManager.deleteDocumentWithVectors(selectedKBForManagement.id, document.id);

      // 🚀 修复：使用统一的刷新函数
      refreshAllData();

      message.success('文档删除成功');
    } catch (error) {
      console.error('❌ [KnowledgeBase] 删除文档失败:', error);
      message.error('删除失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 🚀 新增：重新处理文档
  const reprocessDocument = async (document) => {
    try {
      setLoading(true);

      // 🚀 修复：使用统一进度显示
      updateProgress('重新处理文档', 0, 100, '准备删除旧数据...');

      // 先删除旧的向量数据
      await dataManager.deleteDocumentWithVectors(selectedKBForManagement.id, document.id);

      updateProgress('重新处理文档', 10, 100, '开始重新向量化...');

      // 重新向量化
      const result = await dataManager.addDocumentWithVectorization(
        selectedKBForManagement.id,
        document,
        (progress) => {
          console.log(`📊 [KnowledgeBase] 重新处理进度: ${progress.message}`);
          // 🚀 修复：使用统一进度显示，避免多个弹框
          const current = progress.current || 0;
          const total = progress.total || 100;
          const message = progress.message || '正在重新处理...';

          updateProgress('重新处理文档', current, total, message);
        }
      );

      if (result && result.success) {
        // 🚀 修复：使用统一的刷新函数
        refreshAllData();

        // 🚀 修复：确保显示正确的数值，避免undefined
        const vectorCount = result.vectorCount || result.processedChunks || 0;
        const totalChunks = result.totalChunks || result.chunkCount || vectorCount;

        // 🚀 修复：完成时更新进度并关闭弹框
        updateProgress('重新处理文档', totalChunks, totalChunks, '重新处理完成！');

        setTimeout(() => {
          closeProgress(); // 关闭进度弹框
          message.success(`文档重新处理成功！向量化了 ${vectorCount}/${totalChunks} 个文本块`);
        }, 1000);
      } else {
        closeProgress(); // 关闭进度弹框
        message.error('重新处理失败：返回结果无效');
      }
    } catch (error) {
      console.error('❌ [KnowledgeBase] 重新处理失败:', error);
      closeProgress(); // 🚀 修复：错误时关闭进度弹框
      message.error('重新处理失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 🚀 新增：保存文档编辑
  const saveDocumentEdit = async (values) => {
    try {
      setLoading(true);

      const updatedDocument = {
        ...editingDocument,
        title: values.title,
        content: values.content,
        // 🚀 新增：分块方式配置
        chunkingMethod: values.chunkingMethod || 'traditional',
        chatModel: values.chunkingMethod === 'intelligent' ? values.chatModel : null,
        // 分块配置
        hierarchical: values.hierarchical || false,
        chunkSize: values.chunkSize || 500,
        chunkOverlap: values.chunkOverlap || 50,
        parentChunkSize: values.parentChunkSize || 800,
        childChunkSize: values.childChunkSize || 200,
        // 分割符配置
        separatorType: values.separatorType || 'smart',
        customSeparator: values.customSeparator || '\n\n',
        // 向量模型
        embeddingModel: values.embeddingModel || editingDocument.embeddingModel,
        // 元数据
        metadata: {
          ...editingDocument.metadata,
          tags: values.tags || [],
          category: values.category || '',
          author: values.author || '',
          importance: values.importance || 'medium',
          source: values.source || '',
          language: values.language || 'zh-CN',
          lastModified: new Date().toISOString()
        },
        updatedAt: Date.now()
      };

      // 如果内容发生变化且选择了自动重新处理
      const contentChanged = editingDocument.content !== values.content;
      if (contentChanged && values.autoReprocess) {
        // 先删除旧的向量数据
        await dataManager.deleteDocumentWithVectors(selectedKBForManagement.id, editingDocument.id);

        // 重新向量化
        const result = await dataManager.addDocumentWithVectorization(
          selectedKBForManagement.id,
          updatedDocument,
          (progress) => {
            console.log(`📊 [KnowledgeBase] 重新处理进度: ${progress.message}`);
          }
        );

        if (result.success) {
          message.success(`文档更新并重新向量化成功！处理了 ${result.vectorCount}/${result.totalChunks} 个文本块`);
        }
      } else {
        // 只更新文档信息
        await dataManager.updateDocument(selectedKBForManagement.id, updatedDocument);
        message.success('文档更新成功');
      }

      // 🚀 修复：使用统一的刷新函数
      refreshAllData();

      setDocumentEditModalVisible(false);
      setEditingDocument(null);
      documentEditForm.resetFields();

      message.success('文档编辑保存成功！');

    } catch (error) {
      console.error('❌ [KnowledgeBase] 保存文档编辑失败:', error);
      message.error('保存失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 🚀 新增：保存分块编辑
  const saveChunkEdit = async (values) => {
    try {
      setLoading(true);

      const updatedChunks = [...selectedDocument.chunks];
      updatedChunks[editingChunkIndex] = {
        ...editingChunk,
        text: values.text,
        updatedAt: Date.now()
      };

      const updatedDocument = {
        ...selectedDocument,
        chunks: updatedChunks
      };

      // 如果选择重新处理向量
      if (values.reprocessVector && editingChunk.vectorId) {
        // 删除旧向量
        await dataManager.deleteVector(selectedKBForManagement.id, editingChunk.vectorId);

        // 重新生成向量
        const embeddingModel = embeddingModels.find(m => m.id === selectedKBForManagement.embeddingModel);
        if (embeddingModel) {
          const vectorResult = await dataManager.generateEmbedding(values.text, embeddingModel);
          if (vectorResult.success) {
            updatedChunks[editingChunkIndex].vectorId = vectorResult.vectorId;
            message.success('分块内容和向量更新成功');
          }
        }
      } else {
        message.success('分块内容更新成功');
      }

      // 更新文档
      await dataManager.updateDocument(selectedKBForManagement.id, updatedDocument);

      // 刷新数据
      const updatedKBs = dataManager.getKnowledgeBases();
      setKnowledgeBases([...updatedKBs]);

      const updatedKB = updatedKBs.find(kb => kb.id === selectedKBForManagement.id);
      setSelectedKBForManagement(updatedKB);

      const updatedDoc = updatedKB.documents.find(d => d.id === selectedDocument.id);
      setSelectedDocument(updatedDoc);

      setChunkEditModalVisible(false);
      setEditingChunk(null);
      setEditingChunkIndex(-1);
      chunkEditForm.resetFields();

    } catch (error) {
      console.error('❌ [KnowledgeBase] 保存分块编辑失败:', error);
      message.error('保存失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 🚀 新增：渲染文档详情页面
  const renderDocumentDetail = () => {
    if (!selectedDocument || !selectedKBForManagement) return null;

    const chunks = selectedDocument.chunks || [];

    return (
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        height: 'calc(100vh - 120px)',
        overflow: 'auto',
        padding: '0 4px' // 为滚动条留出空间
      }}>
        <Card
          title={
            <Space>
              <Button
                type="text"
                icon={<FileTextOutlined />}
                onClick={() => setCurrentView('documents')}
                style={{ color: '#1890ff' }}
              >
                返回文档列表
              </Button>
              <Divider type="vertical" />
              <FileTextOutlined style={{ color: '#1890ff' }} />
              <span>文档详情</span>
            </Space>
          }
          extra={
            <Space>
              <Button
                icon={<EditOutlined />}
                onClick={() => editDocument(selectedDocument)}
              >
                编辑文档
              </Button>
              <Button
                icon={<BulbOutlined />}
                onClick={() => reprocessDocument(selectedDocument)}
                loading={loading}
              >
                重新向量化
              </Button>
              <Popconfirm
                title="确定删除此文档吗？"
                description="删除后将无法恢复，包括相关的向量数据。"
                onConfirm={() => {
                  deleteDocument(selectedDocument);
                  setCurrentView('documents');
                }}
                okText="确定"
                cancelText="取消"
              >
                <Button danger icon={<DeleteOutlined />}>
                  删除文档
                </Button>
              </Popconfirm>
            </Space>
          }
        >
          {/* 文档基本信息 */}
          <div style={{ marginBottom: '24px' }}>
            <Title level={3}>{selectedDocument.title}</Title>

            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '24px', marginBottom: '16px' }}>
              {/* 基本信息 */}
              <div style={{ flex: '1', minWidth: '300px' }}>
                <Card size="small" title="基本信息" style={{ height: '100%' }}>
                  <Space size="large" wrap>
                    <div>
                      <Text type="secondary">创建时间：</Text>
                      <Text>{new Date(selectedDocument.createdAt).toLocaleString()}</Text>
                    </div>
                    <div>
                      <Text type="secondary">向量化状态：</Text>
                      {selectedDocument.vectorized ? (
                        <Tag color="green">已向量化</Tag>
                      ) : (
                        <Tag color="orange">未向量化</Tag>
                      )}
                      {selectedDocument.hierarchical && (
                        <Tag color="purple" style={{ marginLeft: '4px' }}>分层分块</Tag>
                      )}
                    </div>
                    <div>
                      <Text type="secondary">分块数量：</Text>
                      <Tag color="blue">{chunks.length} 个分块</Tag>
                    </div>
                    {selectedDocument.vectorCount && (
                      <div>
                        <Text type="secondary">向量数量：</Text>
                        <Tag color="purple">{selectedDocument.vectorCount} 个向量</Tag>
                      </div>
                    )}
                  </Space>
                </Card>
              </div>

              {/* 元数据信息 */}
              {selectedDocument.metadata && (
                <div style={{ flex: '1', minWidth: '300px' }}>
                  <Card size="small" title="元数据信息" style={{ height: '100%' }}>
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
                      {selectedDocument.metadata.category && (
                        <div>
                          <Text type="secondary">分类：</Text>
                          <Tag color="green">{selectedDocument.metadata.category}</Tag>
                        </div>
                      )}
                      {selectedDocument.metadata.author && (
                        <div>
                          <Text type="secondary">作者：</Text>
                          <Text>{selectedDocument.metadata.author}</Text>
                        </div>
                      )}
                      {selectedDocument.metadata.importance && (
                        <div>
                          <Text type="secondary">重要性：</Text>
                          <Tag color={
                            selectedDocument.metadata.importance === 'high' ? 'red' :
                            selectedDocument.metadata.importance === 'medium' ? 'orange' : 'green'
                          }>
                            {selectedDocument.metadata.importance === 'high' ? '高' :
                             selectedDocument.metadata.importance === 'medium' ? '中' : '低'}
                          </Tag>
                        </div>
                      )}
                      {selectedDocument.metadata.source && (
                        <div>
                          <Text type="secondary">来源：</Text>
                          <Text>{selectedDocument.metadata.source}</Text>
                        </div>
                      )}
                      {selectedDocument.metadata.language && (
                        <div>
                          <Text type="secondary">语言：</Text>
                          <Tag color="blue">
                            {selectedDocument.metadata.language === 'zh-CN' ? '中文' :
                             selectedDocument.metadata.language === 'en-US' ? 'English' :
                             selectedDocument.metadata.language}
                          </Tag>
                        </div>
                      )}
                    </div>
                    {selectedDocument.metadata.tags && selectedDocument.metadata.tags.length > 0 && (
                      <div style={{ marginTop: '8px' }}>
                        <Text type="secondary">标签：</Text>
                        <div style={{ marginTop: '4px' }}>
                          {selectedDocument.metadata.tags.map(tag => (
                            <Tag key={tag} color="blue" style={{ marginBottom: '4px' }}>
                              {tag}
                            </Tag>
                          ))}
                        </div>
                      </div>
                    )}
                  </Card>
                </div>
              )}
            </div>
          </div>

          {/* 文档内容 */}
          <Card
            title={
              <Space>
                <span>文档内容</span>
                <Button
                  type="link"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => editDocument(selectedDocument)}
                >
                  编辑内容
                </Button>
              </Space>
            }
            size="small"
            style={{ marginBottom: '24px' }}
          >
            <div
              style={{
                maxHeight: '500px',
                overflow: 'auto',
                padding: '16px',
                background: '#1f1f1f',
                color: '#ffffff',
                borderRadius: '6px',
                whiteSpace: 'pre-wrap',
                lineHeight: '1.6',
                border: '1px solid #303030',
                // 自定义滚动条样式
                scrollbarWidth: 'thin',
                scrollbarColor: '#666 #333'
              }}
              className="custom-scrollbar"
            >
              {selectedDocument.content || '暂无内容'}
            </div>
          </Card>

          {/* 分块信息 */}
          {chunks.length > 0 && (
            <Card
              title={
                <Space>
                  <span>
                    文档分块 ({chunks.length} 个
                    {selectedDocument.hierarchical && (
                      <>
                        ，{selectedDocument.parentChunkCount || 0} 个父分块，
                        {selectedDocument.childChunkCount || 0} 个子分块
                      </>
                    )}
                    )
                  </span>
                  {selectedDocument.hierarchical && (
                    <Tag color="purple" icon={<BulbOutlined />}>
                      分层分块
                    </Tag>
                  )}
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => editDocumentChunks(selectedDocument)}
                  >
                    编辑分块
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    icon={<BulbOutlined />}
                    onClick={() => reprocessDocument(selectedDocument)}
                    loading={loading}
                  >
                    重新分块
                  </Button>
                </Space>
              }
              size="small"
              bodyStyle={{
                maxHeight: '600px',
                overflow: 'auto',
                padding: '16px'
              }}
            >
              <List
                dataSource={chunks}
                renderItem={(chunk, index) => {
                  // 只渲染顶级分块（父分块或平面分块）
                  // 子分块会在父分块内部递归渲染
                  if (chunk.level === 1 || chunk.parentId) {
                    return null; // 子分块不在这里渲染
                  }

                  const isParent = chunk.level === 0 && selectedDocument.hierarchical;
                  const isChild = chunk.level === 1;
                  const hasChildren = chunk.children && chunk.children.length > 0;

                  return (
                    <div key={chunk.id || index} style={{ marginBottom: '16px' }}>
                      {/* 父分块或平面分块 */}
                      <List.Item
                        style={{
                          backgroundColor: isParent ? '#2a2a2a' : '#1f1f1f',
                          border: isParent ? '2px solid #722ed1' : '1px solid #404040',
                          borderRadius: '8px',
                          padding: '16px',
                          marginBottom: hasChildren ? '8px' : '16px'
                        }}
                        actions={[
                          <Button
                            type="link"
                            size="small"
                            icon={<EditOutlined />}
                            onClick={() => editChunk(chunk, index)}
                            style={{ color: '#1890ff' }}
                          >
                            编辑
                          </Button>,
                          <Popconfirm
                            title="确定删除此分块吗？"
                            onConfirm={() => deleteChunk(chunk, index)}
                            okText="确定"
                            cancelText="取消"
                          >
                            <Button
                              type="link"
                              size="small"
                              danger
                              icon={<DeleteOutlined />}
                              style={{ color: '#ff4d4f' }}
                            >
                              删除
                            </Button>
                          </Popconfirm>
                        ]}
                      >
                        <List.Item.Meta
                          avatar={
                            <div
                              style={{
                                width: '36px',
                                height: '36px',
                                borderRadius: isParent ? '8px' : '50%',
                                background: isParent ?
                                  (chunk.vectorId ? 'linear-gradient(135deg, #722ed1, #9254de)' : 'linear-gradient(135deg, #5b3cc4, #7356d0)') :
                                  (chunk.vectorId ? 'linear-gradient(135deg, #52c41a, #73d13d)' : 'linear-gradient(135deg, #fa8c16, #ffa940)'),
                                color: 'white',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '14px',
                                fontWeight: 'bold',
                                border: isParent ? '2px solid #f0f0f0' : 'none',
                                boxShadow: '0 2px 6px rgba(0,0,0,0.15)'
                              }}
                            >
                              {isParent ? 'P' : isChild ? 'C' : index + 1}
                            </div>
                          }
                          title={
                            <Space>
                              <Text strong style={{ color: '#ffffff' }}>
                                {isParent ? '父分块' : isChild ? '子分块' : '分块'} {index + 1}
                              </Text>
                              {isParent && (
                                <Tag size="small" color="purple">父级</Tag>
                              )}
                              {isChild && (
                                <Tag size="small" color="cyan">子级</Tag>
                              )}
                              {chunk.vectorId ? (
                                <Tag size="small" color="green">已向量化</Tag>
                              ) : (
                                <Tag size="small" color="orange">未向量化</Tag>
                              )}
                              {chunk.similarity && (
                                <Tag size="small" color="blue">
                                  相似度: {(chunk.similarity * 100).toFixed(1)}%
                                </Tag>
                              )}
                              {hasChildren && (
                                <Tag size="small" color="geekblue">
                                  {chunk.children.length} 个子分块
                                </Tag>
                              )}
                            </Space>
                          }
                      description={
                        <div>
                          <div
                            style={{
                              maxHeight: '120px',
                              overflow: 'auto',
                              padding: '12px',
                              background: '#1f1f1f',
                              color: '#ffffff',
                              borderRadius: '6px',
                              fontSize: '13px',
                              lineHeight: '1.5',
                              border: '1px solid #303030',
                              marginBottom: '8px'
                            }}
                          >
                            {chunk.text || '暂无内容'}
                          </div>
                          <Space size="large" wrap>
                            <span>
                              <Text style={{ fontSize: '11px', color: '#cccccc' }}>
                                字符数: {chunk.text ? chunk.text.length : 0}
                              </Text>
                            </span>
                            {chunk.vectorId && (
                              <span>
                                <Text style={{ fontSize: '11px', color: '#cccccc' }}>
                                  向量ID: {chunk.vectorId.slice(-8)}
                                </Text>
                              </span>
                            )}
                            {chunk.createdAt && (
                              <span>
                                <Text style={{ fontSize: '11px', color: '#cccccc' }}>
                                  创建: {new Date(chunk.createdAt).toLocaleString()}
                                </Text>
                              </span>
                            )}
                          </Space>
                        </div>
                      }
                    />
                  </List.Item>

                  {/* 递归显示子分块 */}
                  {hasChildren && chunk.children.map((childChunk, childIndex) => (
                    <List.Item
                      key={childChunk.id || `${index}_${childIndex}`}
                      style={{
                        marginLeft: '40px',
                        borderLeft: '3px solid #1890ff',
                        paddingLeft: '16px',
                        backgroundColor: '#1a1a1a',
                        borderRadius: '0 6px 6px 0',
                        marginTop: '8px',
                        marginBottom: '8px',
                        border: '1px solid #303030'
                      }}
                      actions={[
                        <Button
                          type="link"
                          size="small"
                          icon={<EditOutlined />}
                          onClick={() => editChunk(childChunk, `${index}_${childIndex}`)}
                          style={{ color: '#1890ff' }}
                        >
                          编辑
                        </Button>,
                        <Popconfirm
                          title="确定删除此子分块吗？"
                          onConfirm={() => deleteChunk(childChunk, `${index}_${childIndex}`)}
                          okText="确定"
                          cancelText="取消"
                        >
                          <Button
                            type="link"
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                            style={{ color: '#ff4d4f' }}
                          >
                            删除
                          </Button>
                        </Popconfirm>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={
                          <div
                            style={{
                              width: '32px',
                              height: '32px',
                              borderRadius: '8px',
                              background: childChunk.vectorId ?
                                'linear-gradient(135deg, #52c41a, #73d13d)' :
                                'linear-gradient(135deg, #fa8c16, #ffa940)',
                              color: 'white',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              fontSize: '11px',
                              fontWeight: 'bold',
                              border: '2px solid #fff',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}
                          >
                            C{childIndex + 1}
                          </div>
                        }
                        title={
                          <Space>
                            <Text strong style={{ color: '#ffffff' }}>子分块 {childIndex + 1}</Text>
                            <Tag size="small" color="cyan">子级</Tag>
                            {childChunk.vectorId ? (
                              <Tag size="small" color="green">已向量化</Tag>
                            ) : (
                              <Tag size="small" color="orange">未向量化</Tag>
                            )}
                            {childChunk.similarity && (
                              <Tag size="small" color="blue">
                                相似度: {(childChunk.similarity * 100).toFixed(1)}%
                              </Tag>
                            )}
                          </Space>
                        }
                        description={
                          <div>
                            <div
                              style={{
                                maxHeight: '100px',
                                overflow: 'auto',
                                padding: '12px',
                                background: '#1f1f1f',
                                color: '#ffffff',
                                borderRadius: '6px',
                                fontSize: '13px',
                                lineHeight: '1.5',
                                border: '1px solid #303030',
                                marginBottom: '8px',
                                scrollbarWidth: 'thin',
                                scrollbarColor: '#666 #333'
                              }}
                              className="custom-scrollbar"
                            >
                              {childChunk.text || '暂无内容'}
                            </div>
                            <Space size="large" wrap>
                              <span>
                                <Text style={{ fontSize: '11px', color: '#cccccc' }}>
                                  字符数: {childChunk.text ? childChunk.text.length : 0}
                                </Text>
                              </span>
                              {childChunk.vectorId && (
                                <span>
                                  <Text style={{ fontSize: '11px', color: '#cccccc' }}>
                                    向量ID: {childChunk.vectorId.slice(-8)}
                                  </Text>
                                </span>
                              )}
                              {childChunk.createdAt && (
                                <span>
                                  <Text style={{ fontSize: '11px', color: '#cccccc' }}>
                                    创建: {new Date(childChunk.createdAt).toLocaleString()}
                                  </Text>
                                </span>
                              )}
                            </Space>
                          </div>
                        }
                      />
                    </List.Item>
                  ))}
                </div>
              );
            }}
            pagination={{
              pageSize: 3,
              size: 'small',
              showSizeChanger: true,
              showQuickJumper: true
            }}
          />
            </Card>
          )}
        </Card>
      </div>
    );
  };

  // 🚀 新增：渲染向量数据页面
  const renderVectorData = () => {
    if (!selectedKBForManagement) return null;

    return (
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        height: 'calc(100vh - 120px)',
        overflow: 'auto',
        padding: '0 4px'
      }}>
        <Card
          title={
            <Space>
              <Button
                type="text"
                icon={<DatabaseOutlined />}
                onClick={() => setCurrentView('documents')}
                style={{ color: '#1890ff' }}
              >
                返回文档管理
              </Button>
              <Divider type="vertical" />
              <BulbOutlined style={{ color: '#1890ff' }} />
              <span>{selectedKBForManagement.name} - 向量数据</span>
            </Space>
          }
          extra={
            <Button
              icon={<DatabaseOutlined />}
              onClick={() => loadVectorStats()}
              loading={loading}
            >
              刷新统计
            </Button>
          }
        >
          {/* 向量数据库统计 */}
          {vectorStats && (
            <div style={{ marginBottom: '24px' }}>
              <Title level={4}>向量数据库统计</Title>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px' }}>
                <Card size="small" style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                    {vectorStats.vectorDatabase?.vectorCount || 0}
                  </div>
                  <div style={{ color: '#666' }}>总向量数量</div>
                </Card>
                <Card size="small" style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                    {vectorStats.vectorDatabase?.dimension || 0}
                  </div>
                  <div style={{ color: '#666' }}>向量维度</div>
                </Card>
                <Card size="small" style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                    {vectorStats.knowledgeBases || 0}
                  </div>
                  <div style={{ color: '#666' }}>知识库数量</div>
                </Card>
                <Card size="small" style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                    {vectorStats.embeddingCache?.size || 0}
                  </div>
                  <div style={{ color: '#666' }}>缓存条目</div>
                </Card>
              </div>
            </div>
          )}

          {/* 知识库向量信息 */}
          <Card
            title="知识库向量信息"
            size="small"
            style={{ marginBottom: '24px' }}
          >
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
              <div>
                <Text type="secondary">知识库名称：</Text>
                <Text strong>{selectedKBForManagement.name}</Text>
              </div>
              <div>
                <Text type="secondary">文档数量：</Text>
                <Text>{selectedKBForManagement.documents?.length || 0} 个</Text>
              </div>
              <div>
                <Text type="secondary">向量模型：</Text>
                <Tag color="orange">
                  {embeddingModels.find(m => m.id === selectedKBForManagement.embeddingModel)?.name || '未知'}
                </Tag>
              </div>
              <div>
                <Text type="secondary">创建时间：</Text>
                <Text>{new Date(selectedKBForManagement.createdAt).toLocaleString()}</Text>
              </div>
            </div>
          </Card>

          {/* 文档向量详情 */}
          <Card
            title="文档向量详情"
            size="small"
          >
            {selectedKBForManagement.documents && selectedKBForManagement.documents.length > 0 ? (
              <List
                dataSource={selectedKBForManagement.documents}
                renderItem={(doc) => (
                  <List.Item
                    actions={[
                      <Button
                        type="link"
                        onClick={() => openDocumentDetail(doc, selectedKBForManagement)}
                      >
                        查看详情
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <div
                          style={{
                            width: '40px',
                            height: '40px',
                            borderRadius: '8px',
                            background: doc.vectorized ? '#52c41a' : '#fa8c16',
                            color: 'white',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <FileTextOutlined />
                        </div>
                      }
                      title={
                        <Space>
                          <Text strong>{doc.title}</Text>
                          {doc.vectorized ? (
                            <Tag color="green">已向量化</Tag>
                          ) : (
                            <Tag color="orange">未向量化</Tag>
                          )}
                        </Space>
                      }
                      description={
                        <div>
                          <Space size="large" wrap>
                            <span>
                              <Text type="secondary">分块数量：</Text>
                              <Text>{doc.chunks?.length || 0}</Text>
                            </span>
                            <span>
                              <Text type="secondary">向量数量：</Text>
                              <Text>{doc.vectorCount || 0}</Text>
                            </span>
                            <span>
                              <Text type="secondary">处理时间：</Text>
                              <Text>{doc.processedAt ? new Date(doc.processedAt).toLocaleString() : '未处理'}</Text>
                            </span>
                          </Space>
                        </div>
                      }
                    />
                  </List.Item>
                )}
                pagination={{
                  pageSize: 5,
                  size: 'small'
                }}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
                <FileTextOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <div>暂无文档数据</div>
              </div>
            )}
          </Card>
        </Card>
      </div>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 🚀 新增：优化状态面板 */}
      {showOptimizationPanel && (
        <OptimizationStatusPanel
          optimizationStatus={optimizationStatus}
          performanceStats={performanceStats}
          onRefresh={loadOptimizationStatus}
          style={{ marginBottom: 24 }}
        />
      )}

      {/* 自定义滚动条样式 */}
      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #888;
          border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #555;
        }
        .ant-list-item {
          border-bottom: 1px solid #f0f0f0 !important;
        }
        .ant-card-body {
          scrollbar-width: thin;
          scrollbar-color: #888 #f1f1f1;
        }
      `}</style>
      {currentView === 'list' && (
        <div style={{
          height: 'calc(100vh - 200px)', // 为优化面板留出空间
          overflow: 'auto',
          padding: '0 4px'
        }} className="custom-scrollbar">
          <Card
          title={
            <Space>
              <BulbOutlined style={{ color: '#1890ff' }} />
              <span>知识库管理</span>
          </Space>
        }
        extra={
          <Space>
            {/* 🚀 新增：优化状态按钮 */}
            <Button
              icon={<SettingOutlined />}
              onClick={() => setShowOptimizationPanel(!showOptimizationPanel)}
              type={showOptimizationPanel ? 'primary' : 'default'}
            >
              {showOptimizationPanel ? '隐藏' : '显示'}优化状态
            </Button>

            {/* 🚀 新增：文档处理按钮 */}
            <Button
              icon={<ThunderboltOutlined />}
              onClick={() => setDocumentProcessorVisible(true)}
            >
              文档处理
            </Button>

            {/* 🚀 新增：知识图谱按钮 */}
            <Button
              icon={<ShareAltOutlined />}
              onClick={() => setKnowledgeGraphVisible(true)}
            >
              知识图谱
            </Button>

            {/* 🚀 新增：调试面板按钮（开发模式） */}
            {process.env.NODE_ENV === 'development' && (
              <Button
                icon={<BugOutlined />}
                onClick={() => setDebugPanelVisible(!debugPanelVisible)}
                type={debugPanelVisible ? 'primary' : 'default'}
              >
                调试面板
              </Button>
            )}
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setModalVisible(true)}
              disabled={embeddingModels.length === 0}
            >
              创建知识库
            </Button>
          </Space>
        }
      >
        {embeddingModels.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px',
            background: '#fafafa',
            borderRadius: '8px',
            marginBottom: '16px'
          }}>
            <DatabaseOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
            <Title level={4} type="secondary">暂无可用的向量嵌入模型</Title>
            <Text type="secondary">
              请先在模型配置中添加类型为"embedding"的模型，才能创建知识库。
            </Text>
          </div>
        )}
        
        <Table
          columns={kbColumns}
          dataSource={knowledgeBases}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          scroll={{ x: 1200 }}
          locale={{
            emptyText: embeddingModels.length > 0 ? '暂无知识库，点击上方按钮创建' : '请先配置向量嵌入模型'
          }}
        />
      </Card>
        </div>
      )}

      {currentView === 'documents' && renderDocumentManagement()}

      {currentView === 'detail' && renderDocumentDetail()}

      {currentView === 'vectors' && renderVectorData()}

      {/* 创建/编辑知识库模态框 */}
      <Modal
        title={editingKB ? '编辑知识库' : '创建知识库'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingKB(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleKBSubmit}
        >
          <Form.Item
            name="name"
            label="知识库名称"
            rules={[{ required: true, message: '请输入知识库名称' }]}
          >
            <Input placeholder="例如：产品文档库" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入知识库描述' }]}
          >
            <TextArea
              rows={3}
              placeholder="请描述这个知识库的用途和内容范围"
            />
          </Form.Item>

          <Form.Item
            name="embeddingModel"
            label="向量嵌入模型"
            rules={[{ required: true, message: '请选择向量嵌入模型' }]}
          >
            <Select placeholder="选择用于向量化的模型">
              {embeddingModels.map(model => (
                <Option key={model.id} value={model.id}>
                  <Space>
                    <Tag color="orange">{model.name}</Tag>
                    <Text type="secondary">({model.baseUrl})</Text>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setModalVisible(false);
                setEditingKB(null);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingKB ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加文档模态框 */}
      <Modal
        title={
          <Space>
            <UploadOutlined style={{ color: '#1890ff' }} />
            <span>添加文档到知识库</span>
            {selectedKB && (
              <Tag color="blue">{selectedKB.name}</Tag>
            )}
          </Space>
        }
        open={documentModalVisible}
        onCancel={() => {
          setDocumentModalVisible(false);
          setUploadProgress(0);
          documentForm.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={documentForm}
          layout="vertical"
          onFinish={handleDocumentUpload}
          initialValues={{
            chunkSize: 500,
            chunkOverlap: 50,
            autoVectorize: true,
            // 🚀 新增：智能分块默认值（推荐使用智能分块）
            chunkingMethod: 'intelligent',
            chatModel: getChatModels()[0]?.id || null,
            hierarchical: false,
            parentChunkSize: 800,
            childChunkSize: 200,
            separatorType: 'smart',
            customSeparator: '\n\n',
            embeddingModel: selectedKB?.embeddingModel || '',
            // 元数据默认值
            tags: [],
            category: '',
            author: '',
            importance: 'medium',
            source: '',
            language: 'zh-CN'
          }}
        >
          {/* 🚀 新增：文档创建方式选择 */}
          <Card
            title="选择文档创建方式"
            size="small"
            style={{ marginBottom: 16 }}
          >
            <Radio.Group
              value={documentCreationMode}
              onChange={(e) => {
                setDocumentCreationMode(e.target.value);
                if (e.target.value === 'text') {
                  setUploadedFileContent(null);
                  documentForm.setFieldsValue({ content: '' });
                }
              }}
              style={{ width: '100%' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Radio value="text">
                  <Space>
                    <EditOutlined style={{ color: '#1890ff' }} />
                    <span>手动输入文本内容</span>
                    <Text type="secondary">（直接在文本框中输入或粘贴内容）</Text>
                  </Space>
                </Radio>
                <Radio value="file">
                  <Space>
                    <ThunderboltOutlined style={{ color: '#fa8c16' }} />
                    <span>上传文档文件</span>
                    <Text type="secondary">
                      {supportedFormats ?
                        `（${supportedFormats.note}）` :
                        '（正在检测支持的格式...）'
                      }
                    </Text>
                  </Space>
                </Radio>
              </Space>
            </Radio.Group>
          </Card>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <Form.Item
              name="title"
              label="文档标题"
              rules={[{ required: true, message: '请输入文档标题' }]}
            >
              <Input placeholder="例如：用户使用手册" />
            </Form.Item>

            <Form.Item
              name="embeddingModel"
              label="向量模型"
              rules={[{ required: true, message: '请选择向量模型' }]}
              tooltip="选择用于向量化此文档的模型，可以与知识库默认模型不同"
            >
              <Select placeholder="选择向量模型">
                {embeddingModels.map(model => (
                  <Select.Option key={model.id} value={model.id}>
                    <Space>
                      <BulbOutlined style={{ color: '#fa8c16' }} />
                      {model.name}
                      <Tag size="small" color="orange">{model.provider}</Tag>
                    </Space>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          {/* 🚀 新增：根据创建方式显示不同的内容输入区域 */}
          {documentCreationMode === 'text' ? (
            <Form.Item
              name="content"
              label="文档内容"
              rules={[
                { required: true, message: '请输入文档内容' },
                { min: 50, message: '文档内容至少需要50个字符' }
              ]}
            >
              <TextArea
                rows={10}
                placeholder="请粘贴或输入文档内容..."
                showCount
                maxLength={50000}
              />
            </Form.Item>
          ) : (
            <Form.Item
              name="fileUpload"
              label="上传文档文件"
              rules={[
                { required: !uploadedFileContent, message: '请上传文档文件' }
              ]}
            >
              <Upload.Dragger
                name="file"
                multiple={false}
                beforeUpload={async (file) => {
                  await handleFileUpload(file);
                  return false; // 阻止自动上传
                }}
                showUploadList={false}
                accept={supportedFormats?.all?.join(',') || '.txt'}
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined style={{ color: '#1890ff' }} />
                </p>
                <p className="ant-upload-text">
                  {processingFile ? '正在处理文件...' : '点击或拖拽文件到此区域上传'}
                </p>
                <p className="ant-upload-hint">
                  支持PDF、Word、Excel、图片等格式，系统将自动提取文本内容
                </p>
              </Upload.Dragger>

              {uploadedFileContent && (
                <Card
                  title="文件处理结果"
                  size="small"
                  style={{ marginTop: 16 }}
                  extra={
                    <Button
                      size="small"
                      onClick={() => {
                        setUploadedFileContent(null);
                        documentForm.setFieldsValue({ content: '' });
                      }}
                    >
                      重新上传
                    </Button>
                  }
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Text strong>文件名：{uploadedFileContent.fileName}</Text>
                    <Text type="secondary">
                      提取文本：{uploadedFileContent.text?.length || 0} 字符
                    </Text>
                    <TextArea
                      value={uploadedFileContent.text}
                      rows={6}
                      placeholder="提取的文本内容将显示在这里..."
                      onChange={(e) => {
                        setUploadedFileContent(prev => ({
                          ...prev,
                          text: e.target.value
                        }));
                        documentForm.setFieldsValue({ content: e.target.value });
                      }}
                    />
                  </Space>
                </Card>
              )}
            </Form.Item>
          )}

          <Card
            title={
              <Space>
                <SettingOutlined style={{ color: '#722ed1' }} />
                <span>向量化配置</span>
              </Space>
            }
            size="small"
            style={{ marginBottom: '16px' }}
          >
            {/* 🚀 新增：分块方式选择 */}
            <Form.Item
              name="chunkingMethod"
              label="分块方式"
              tooltip="智能分块使用AI模型保持内容结构完整性"
              style={{ marginBottom: '16px' }}
            >
              <Radio.Group
                value={chunkingMethod}
                onChange={(e) => setChunkingMethod(e.target.value)}
              >
                <Radio value="traditional">传统分块</Radio>
                <Radio value="intelligent">智能分块</Radio>
              </Radio.Group>
            </Form.Item>

            {/* 智能分块模型选择 */}
            <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
              prevValues.chunkingMethod !== currentValues.chunkingMethod
            }>
              {({ getFieldValue }) => {
                const currentChunkingMethod = getFieldValue('chunkingMethod');

                if (currentChunkingMethod === 'intelligent') {
                  return (
                    <>
                      <Form.Item
                        name="chatModel"
                        label="分块模型"
                        rules={[{ required: true, message: '请选择用于智能分块的Chat模型' }]}
                        tooltip="选择用于智能分块的Chat模型"
                        style={{ marginBottom: '16px' }}
                      >
                        <Select
                          placeholder="选择Chat模型"
                          value={selectedChatModel}
                          onChange={setSelectedChatModel}
                        >
                          {getChatModels().map(model => (
                            <Select.Option key={model.id} value={model.id}>
                              <Space>
                                <span>{model.name}</span>
                                <Tag size="small" color="blue">{model.provider}</Tag>
                              </Space>
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>

                      <Alert
                        message="智能分块说明"
                        description="智能分块使用AI模型分析文档结构，自动识别并适配不同内容类型（SQL、代码、API文档、技术文档、业务流程、配置文件等），保持结构化内容的完整性，避免在重要边界处分割。推荐用于包含多种内容类型的复杂文档。"
                        type="info"
                        showIcon
                        style={{ marginBottom: '16px' }}
                      />
                    </>
                  );
                }
                return null;
              }}
            </Form.Item>

            {/* 分块模式选择 */}
            <Form.Item
              name="hierarchical"
              label="分块模式"
              valuePropName="checked"
              tooltip="分层分块可以提供更精细的搜索粒度"
              style={{ marginBottom: '16px' }}
            >
              <Switch
                checkedChildren="分层分块"
                unCheckedChildren="平面分块"
                onChange={(checked) => {
                  // 可以在这里添加动态表单逻辑
                }}
              />
            </Form.Item>

            <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
              prevValues.hierarchical !== currentValues.hierarchical
            }>
              {({ getFieldValue }) => {
                const isHierarchical = getFieldValue('hierarchical');

                if (isHierarchical) {
                  // 分层分块配置
                  return (
                    <>
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr 1fr', gap: '16px', marginBottom: '16px' }}>
                        <Form.Item
                          name="parentChunkSize"
                          label="父分块大小"
                          tooltip="父分块的字符数，提供文档上下文"
                        >
                          <InputNumber
                            min={400}
                            max={1500}
                            step={100}
                            placeholder="800"
                            style={{ width: '100%' }}
                            addonAfter="字符"
                          />
                        </Form.Item>

                        <Form.Item
                          name="childChunkSize"
                          label="子分块大小"
                          tooltip="子分块的字符数，提供精细搜索"
                        >
                          <InputNumber
                            min={100}
                            max={500}
                            step={50}
                            placeholder="200"
                            style={{ width: '100%' }}
                            addonAfter="字符"
                          />
                        </Form.Item>

                        <Form.Item
                          name="chunkOverlap"
                          label="重叠字符"
                          tooltip="相邻分块的重叠字符数"
                        >
                          <InputNumber
                            min={0}
                            max={200}
                            step={10}
                            placeholder="50"
                            style={{ width: '100%' }}
                            addonAfter="字符"
                          />
                        </Form.Item>

                        <Form.Item
                          name="autoVectorize"
                          label="自动向量化"
                          valuePropName="checked"
                          tooltip="添加文档后立即进行向量化处理"
                        >
                          <Switch
                            checkedChildren="开启"
                            unCheckedChildren="关闭"
                          />
                        </Form.Item>
                      </div>

                      {/* 分割符配置 */}
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                        <Form.Item
                          name="separatorType"
                          label="分割符类型"
                          tooltip="选择文本分割的方式"
                        >
                          <Select placeholder="选择分割符类型">
                            <Select.Option value="smart">
                              <Space>
                                <BulbOutlined style={{ color: '#722ed1' }} />
                                智能分割
                              </Space>
                            </Select.Option>
                            <Select.Option value="paragraph">段落分割</Select.Option>
                            <Select.Option value="sentence">句子分割</Select.Option>
                            <Select.Option value="line">行分割</Select.Option>
                            <Select.Option value="custom">自定义分割符</Select.Option>
                          </Select>
                        </Form.Item>

                        <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                          prevValues.separatorType !== currentValues.separatorType
                        }>
                          {({ getFieldValue }) => {
                            const separatorType = getFieldValue('separatorType');
                            return separatorType === 'custom' ? (
                              <Form.Item
                                name="customSeparator"
                                label="自定义分割符"
                                tooltip="输入自定义的分割符，如 \\n\\n 或 --- 等"
                              >
                                <Input placeholder="例如: \\n\\n 或 ---" />
                              </Form.Item>
                            ) : (
                              <div style={{ height: '32px' }} /> // 占位符保持布局
                            );
                          }}
                        </Form.Item>
                      </div>
                    </>
                  );
                } else {
                  // 平面分块配置
                  return (
                    <>
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px', marginBottom: '16px' }}>
                        <Form.Item
                          name="chunkSize"
                          label="分块大小"
                          tooltip="每个文本块的字符数，影响搜索精度和性能"
                        >
                          <InputNumber
                            min={100}
                            max={2000}
                            step={50}
                            placeholder="500"
                            style={{ width: '100%' }}
                            addonAfter="字符"
                          />
                        </Form.Item>

                        <Form.Item
                          name="chunkOverlap"
                          label="重叠字符"
                          tooltip="相邻文本块的重叠字符数，提高搜索连续性"
                        >
                          <InputNumber
                            min={0}
                            max={200}
                            step={10}
                            placeholder="50"
                            style={{ width: '100%' }}
                            addonAfter="字符"
                          />
                        </Form.Item>

                        <Form.Item
                          name="autoVectorize"
                          label="自动向量化"
                          valuePropName="checked"
                          tooltip="添加文档后立即进行向量化处理"
                        >
                          <Switch
                            checkedChildren="开启"
                            unCheckedChildren="关闭"
                          />
                        </Form.Item>
                      </div>

                      {/* 分割符配置 */}
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                        <Form.Item
                          name="separatorType"
                          label="分割符类型"
                          tooltip="选择文本分割的方式"
                        >
                          <Select placeholder="选择分割符类型">
                            <Select.Option value="smart">
                              <Space>
                                <BulbOutlined style={{ color: '#722ed1' }} />
                                智能分割
                              </Space>
                            </Select.Option>
                            <Select.Option value="paragraph">段落分割</Select.Option>
                            <Select.Option value="sentence">句子分割</Select.Option>
                            <Select.Option value="line">行分割</Select.Option>
                            <Select.Option value="custom">自定义分割符</Select.Option>
                          </Select>
                        </Form.Item>

                        <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                          prevValues.separatorType !== currentValues.separatorType
                        }>
                          {({ getFieldValue }) => {
                            const separatorType = getFieldValue('separatorType');
                            return separatorType === 'custom' ? (
                              <Form.Item
                                name="customSeparator"
                                label="自定义分割符"
                                tooltip="输入自定义的分割符，如 \\n\\n 或 --- 等"
                              >
                                <Input placeholder="例如: \\n\\n 或 ---" />
                              </Form.Item>
                            ) : (
                              <div style={{ height: '32px' }} /> // 占位符保持布局
                            );
                          }}
                        </Form.Item>
                      </div>
                    </>
                  );
                }
              }}
            </Form.Item>
          </Card>

          {/* 元数据配置 */}
          <Card
            title={
              <Space>
                <DatabaseOutlined style={{ color: '#52c41a' }} />
                <span>文档元数据</span>
              </Space>
            }
            size="small"
            style={{ marginBottom: '16px' }}
          >
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
              <Form.Item
                name="category"
                label="文档分类"
                tooltip="为文档设置分类，便于管理和筛选"
              >
                <Select
                  placeholder="选择或输入分类"
                  mode="tags"
                  maxTagCount={1}
                  options={[
                    { value: '技术文档', label: '技术文档' },
                    { value: '用户手册', label: '用户手册' },
                    { value: '产品说明', label: '产品说明' },
                    { value: '政策文件', label: '政策文件' },
                    { value: '培训资料', label: '培训资料' },
                    { value: '其他', label: '其他' }
                  ]}
                />
              </Form.Item>

              <Form.Item
                name="author"
                label="作者"
                tooltip="文档的创建者或负责人"
              >
                <Input placeholder="输入作者姓名" />
              </Form.Item>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px', marginBottom: '16px' }}>
              <Form.Item
                name="importance"
                label="重要性等级"
                tooltip="设置文档的重要性级别"
              >
                <Select placeholder="选择重要性">
                  <Select.Option value="low">
                    <Space>
                      <span style={{ color: '#52c41a' }}>●</span>
                      低
                    </Space>
                  </Select.Option>
                  <Select.Option value="medium">
                    <Space>
                      <span style={{ color: '#fa8c16' }}>●</span>
                      中
                    </Space>
                  </Select.Option>
                  <Select.Option value="high">
                    <Space>
                      <span style={{ color: '#f5222d' }}>●</span>
                      高
                    </Space>
                  </Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="source"
                label="来源"
                tooltip="文档的来源或出处"
              >
                <Input placeholder="例如：官方网站、内部文档等" />
              </Form.Item>

              <Form.Item
                name="language"
                label="语言"
                tooltip="文档的主要语言"
              >
                <Select placeholder="选择语言">
                  <Select.Option value="zh-CN">中文</Select.Option>
                  <Select.Option value="en-US">English</Select.Option>
                  <Select.Option value="ja-JP">日本語</Select.Option>
                  <Select.Option value="ko-KR">한국어</Select.Option>
                </Select>
              </Form.Item>
            </div>

            <Form.Item
              name="tags"
              label="标签"
              tooltip="为文档添加标签，支持多个标签"
            >
              <Select
                mode="tags"
                placeholder="输入标签，按回车添加"
                style={{ width: '100%' }}
                tokenSeparators={[',']}
                options={[
                  { value: 'API', label: 'API' },
                  { value: '教程', label: '教程' },
                  { value: '指南', label: '指南' },
                  { value: '参考', label: '参考' },
                  { value: '示例', label: '示例' },
                  { value: '最佳实践', label: '最佳实践' }
                ]}
              />
            </Form.Item>
          </Card>

          {uploadProgress > 0 && (
            <Form.Item label="向量化处理进度">
              <Progress
                percent={uploadProgress}
                status={uploadProgress === 100 ? 'success' : 'active'}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
            </Form.Item>
          )}

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setDocumentModalVisible(false);
                setUploadProgress(0);
                documentForm.resetFields();
              }}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                disabled={uploadProgress > 0 && uploadProgress < 100}
              >
                添加文档
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 文档编辑模态框 */}
      <Modal
        title={
          <Space>
            <EditOutlined style={{ color: '#fa8c16' }} />
            <span>编辑文档</span>
            {editingDocument && (
              <Tag color="orange">{editingDocument.title}</Tag>
            )}
          </Space>
        }
        open={documentEditModalVisible}
        onCancel={() => {
          setDocumentEditModalVisible(false);
          setEditingDocument(null);
          documentEditForm.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={documentEditForm}
          layout="vertical"
          onFinish={saveDocumentEdit}
        >
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <Form.Item
              name="title"
              label="文档标题"
              rules={[{ required: true, message: '请输入文档标题' }]}
            >
              <Input placeholder="请输入文档标题" />
            </Form.Item>

            <Form.Item
              name="embeddingModel"
              label="向量模型"
              rules={[{ required: true, message: '请选择向量模型' }]}
              tooltip="更改向量模型需要重新向量化"
            >
              <Select placeholder="选择向量模型">
                {embeddingModels.map(model => (
                  <Select.Option key={model.id} value={model.id}>
                    <Space>
                      <BulbOutlined style={{ color: '#fa8c16' }} />
                      {model.name}
                      <Tag size="small" color="orange">{model.provider}</Tag>
                    </Space>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <Form.Item
            name="content"
            label="文档内容"
            rules={[
              { required: true, message: '请输入文档内容' },
              { min: 50, message: '文档内容至少需要50个字符' }
            ]}
          >
            <TextArea
              rows={12}
              placeholder="请输入文档内容..."
              showCount
              maxLength={50000}
            />
          </Form.Item>

          <Card
            title={
              <Space>
                <SettingOutlined style={{ color: '#722ed1' }} />
                <span>向量化配置</span>
              </Space>
            }
            size="small"
            style={{ marginBottom: '16px' }}
          >
            {/* 🚀 新增：分块方式选择 */}
            <Form.Item
              name="chunkingMethod"
              label="分块方式"
              tooltip="智能分块使用AI模型保持内容结构完整性"
              style={{ marginBottom: '16px' }}
            >
              <Radio.Group
                value={chunkingMethod}
                onChange={(e) => setChunkingMethod(e.target.value)}
              >
                <Radio value="traditional">传统分块</Radio>
                <Radio value="intelligent">智能分块</Radio>
              </Radio.Group>
            </Form.Item>

            {/* 智能分块模型选择 */}
            {chunkingMethod === 'intelligent' && (
              <Form.Item
                name="chatModel"
                label="分块模型"
                rules={[{ required: true, message: '请选择用于智能分块的Chat模型' }]}
                tooltip="选择用于智能分块的Chat模型"
                style={{ marginBottom: '16px' }}
              >
                <Select
                  placeholder="选择Chat模型"
                  value={selectedChatModel}
                  onChange={setSelectedChatModel}
                >
                  {getChatModels().map(model => (
                    <Select.Option key={model.id} value={model.id}>
                      <Space>
                        <span>{model.name}</span>
                        <Tag size="small" color="blue">{model.provider}</Tag>
                      </Space>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}

            {chunkingMethod === 'intelligent' && (
              <Alert
                message="智能分块说明"
                description="智能分块会使用AI模型分析文档结构，保持代码块、表格、SQL语句等结构化内容的完整性，避免在重要边界处分割。"
                type="info"
                showIcon
                style={{ marginBottom: '16px' }}
              />
            )}

            {/* 分块模式选择 */}
            <Form.Item
              name="hierarchical"
              label="分块模式"
              valuePropName="checked"
              tooltip="分层分块可以提供更精细的搜索粒度"
              style={{ marginBottom: '16px' }}
            >
              <Switch
                checkedChildren="分层分块"
                unCheckedChildren="平面分块"
              />
            </Form.Item>

            <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
              prevValues.hierarchical !== currentValues.hierarchical
            }>
              {({ getFieldValue }) => {
                const isHierarchical = getFieldValue('hierarchical');

                if (isHierarchical) {
                  // 分层分块配置
                  return (
                    <>
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr 1fr', gap: '16px', marginBottom: '16px' }}>
                        <Form.Item
                          name="parentChunkSize"
                          label="父分块大小"
                          tooltip="父分块的字符数，提供文档上下文"
                        >
                          <InputNumber
                            min={400}
                            max={1500}
                            step={100}
                            style={{ width: '100%' }}
                            addonAfter="字符"
                          />
                        </Form.Item>

                        <Form.Item
                          name="childChunkSize"
                          label="子分块大小"
                          tooltip="子分块的字符数，提供精细搜索"
                        >
                          <InputNumber
                            min={100}
                            max={500}
                            step={50}
                            style={{ width: '100%' }}
                            addonAfter="字符"
                          />
                        </Form.Item>

                        <Form.Item
                          name="chunkOverlap"
                          label="重叠字符"
                          tooltip="相邻分块的重叠字符数"
                        >
                          <InputNumber
                            min={0}
                            max={200}
                            step={10}
                            style={{ width: '100%' }}
                            addonAfter="字符"
                          />
                        </Form.Item>

                        <Form.Item
                          name="autoReprocess"
                          label="自动重新处理"
                          valuePropName="checked"
                          tooltip="内容变化时自动重新向量化"
                        >
                          <Switch
                            checkedChildren="开启"
                            unCheckedChildren="关闭"
                          />
                        </Form.Item>
                      </div>

                      {/* 分割符配置 */}
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                        <Form.Item
                          name="separatorType"
                          label="分割符类型"
                          tooltip="选择文本分割的方式"
                        >
                          <Select placeholder="选择分割符类型">
                            <Select.Option value="smart">
                              <Space>
                                <BulbOutlined style={{ color: '#722ed1' }} />
                                智能分割
                              </Space>
                            </Select.Option>
                            <Select.Option value="paragraph">段落分割</Select.Option>
                            <Select.Option value="sentence">句子分割</Select.Option>
                            <Select.Option value="line">行分割</Select.Option>
                            <Select.Option value="custom">自定义分割符</Select.Option>
                          </Select>
                        </Form.Item>

                        <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                          prevValues.separatorType !== currentValues.separatorType
                        }>
                          {({ getFieldValue }) => {
                            const separatorType = getFieldValue('separatorType');
                            return separatorType === 'custom' ? (
                              <Form.Item
                                name="customSeparator"
                                label="自定义分割符"
                                tooltip="输入自定义的分割符，如 \\n\\n 或 --- 等"
                              >
                                <Input placeholder="例如: \\n\\n 或 ---" />
                              </Form.Item>
                            ) : (
                              <div style={{ height: '32px' }} />
                            );
                          }}
                        </Form.Item>
                      </div>
                    </>
                  );
                } else {
                  // 平面分块配置
                  return (
                    <>
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px', marginBottom: '16px' }}>
                        <Form.Item
                          name="chunkSize"
                          label="分块大小"
                          tooltip="每个文本块的字符数"
                        >
                          <InputNumber
                            min={100}
                            max={2000}
                            step={50}
                            style={{ width: '100%' }}
                            addonAfter="字符"
                          />
                        </Form.Item>

                        <Form.Item
                          name="chunkOverlap"
                          label="重叠字符"
                          tooltip="相邻文本块的重叠字符数"
                        >
                          <InputNumber
                            min={0}
                            max={200}
                            step={10}
                            style={{ width: '100%' }}
                            addonAfter="字符"
                          />
                        </Form.Item>

                        <Form.Item
                          name="autoReprocess"
                          label="自动重新处理"
                          valuePropName="checked"
                          tooltip="内容变化时自动重新向量化"
                        >
                          <Switch
                            checkedChildren="开启"
                            unCheckedChildren="关闭"
                          />
                        </Form.Item>
                      </div>

                      {/* 分割符配置 */}
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                        <Form.Item
                          name="separatorType"
                          label="分割符类型"
                          tooltip="选择文本分割的方式"
                        >
                          <Select placeholder="选择分割符类型">
                            <Select.Option value="smart">
                              <Space>
                                <BulbOutlined style={{ color: '#722ed1' }} />
                                智能分割
                              </Space>
                            </Select.Option>
                            <Select.Option value="paragraph">段落分割</Select.Option>
                            <Select.Option value="sentence">句子分割</Select.Option>
                            <Select.Option value="line">行分割</Select.Option>
                            <Select.Option value="custom">自定义分割符</Select.Option>
                          </Select>
                        </Form.Item>

                        <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                          prevValues.separatorType !== currentValues.separatorType
                        }>
                          {({ getFieldValue }) => {
                            const separatorType = getFieldValue('separatorType');
                            return separatorType === 'custom' ? (
                              <Form.Item
                                name="customSeparator"
                                label="自定义分割符"
                                tooltip="输入自定义的分割符，如 \\n\\n 或 --- 等"
                              >
                                <Input placeholder="例如: \\n\\n 或 ---" />
                              </Form.Item>
                            ) : (
                              <div style={{ height: '32px' }} />
                            );
                          }}
                        </Form.Item>
                      </div>
                    </>
                  );
                }
              }}
            </Form.Item>
          </Card>

          {/* 元数据配置 */}
          <Card
            title={
              <Space>
                <DatabaseOutlined style={{ color: '#52c41a' }} />
                <span>文档元数据</span>
              </Space>
            }
            size="small"
            style={{ marginBottom: '16px' }}
          >
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
              <Form.Item
                name="category"
                label="文档分类"
                tooltip="为文档设置分类，便于管理和筛选"
              >
                <Select
                  placeholder="选择或输入分类"
                  mode="tags"
                  maxTagCount={1}
                  options={[
                    { value: '技术文档', label: '技术文档' },
                    { value: '用户手册', label: '用户手册' },
                    { value: '产品说明', label: '产品说明' },
                    { value: '政策文件', label: '政策文件' },
                    { value: '培训资料', label: '培训资料' },
                    { value: '其他', label: '其他' }
                  ]}
                />
              </Form.Item>

              <Form.Item
                name="author"
                label="作者"
                tooltip="文档的创建者或负责人"
              >
                <Input placeholder="输入作者姓名" />
              </Form.Item>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px', marginBottom: '16px' }}>
              <Form.Item
                name="importance"
                label="重要性等级"
                tooltip="设置文档的重要性级别"
              >
                <Select placeholder="选择重要性">
                  <Select.Option value="low">
                    <Space>
                      <span style={{ color: '#52c41a' }}>●</span>
                      低
                    </Space>
                  </Select.Option>
                  <Select.Option value="medium">
                    <Space>
                      <span style={{ color: '#fa8c16' }}>●</span>
                      中
                    </Space>
                  </Select.Option>
                  <Select.Option value="high">
                    <Space>
                      <span style={{ color: '#f5222d' }}>●</span>
                      高
                    </Space>
                  </Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="source"
                label="来源"
                tooltip="文档的来源或出处"
              >
                <Input placeholder="例如：官方网站、内部文档等" />
              </Form.Item>

              <Form.Item
                name="language"
                label="语言"
                tooltip="文档的主要语言"
              >
                <Select placeholder="选择语言">
                  <Select.Option value="zh-CN">中文</Select.Option>
                  <Select.Option value="en-US">English</Select.Option>
                  <Select.Option value="ja-JP">日本語</Select.Option>
                  <Select.Option value="ko-KR">한국어</Select.Option>
                </Select>
              </Form.Item>
            </div>

            <Form.Item
              name="tags"
              label="标签"
              tooltip="为文档添加标签，支持多个标签"
            >
              <Select
                mode="tags"
                placeholder="输入标签，按回车添加"
                style={{ width: '100%' }}
                tokenSeparators={[',']}
                options={[
                  { value: 'API', label: 'API' },
                  { value: '教程', label: '教程' },
                  { value: '指南', label: '指南' },
                  { value: '参考', label: '参考' },
                  { value: '示例', label: '示例' },
                  { value: '最佳实践', label: '最佳实践' }
                ]}
              />
            </Form.Item>
          </Card>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setDocumentEditModalVisible(false);
                setEditingDocument(null);
                documentEditForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                <Space>
                  <SaveOutlined />
                  保存更改
                </Space>
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 分块编辑模态框 */}
      <Modal
        title={
          <Space>
            <EditOutlined style={{ color: '#52c41a' }} />
            <span>编辑文本分块</span>
            {editingChunk && (
              <Tag color="green">分块 {editingChunkIndex + 1}</Tag>
            )}
          </Space>
        }
        open={chunkEditModalVisible}
        onCancel={() => {
          setChunkEditModalVisible(false);
          setEditingChunk(null);
          setEditingChunkIndex(-1);
          chunkEditForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={chunkEditForm}
          layout="vertical"
          onFinish={saveChunkEdit}
        >
          <Form.Item
            name="text"
            label="分块内容"
            rules={[
              { required: true, message: '请输入分块内容' },
              { min: 10, message: '分块内容至少需要10个字符' }
            ]}
          >
            <TextArea
              rows={10}
              placeholder="请输入分块内容..."
              showCount
              maxLength={2000}
            />
          </Form.Item>

          <Form.Item
            name="reprocessVector"
            label="重新生成向量"
            valuePropName="checked"
            tooltip="内容变化后重新生成向量数据"
          >
            <Switch
              checkedChildren="重新生成"
              unCheckedChildren="保持原有"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setChunkEditModalVisible(false);
                setEditingChunk(null);
                setEditingChunkIndex(-1);
                chunkEditForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                <Space>
                  <SaveOutlined />
                  保存分块
                </Space>
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 知识库搜索模态框 */}
      <Modal
        title={`搜索知识库 "${selectedKB?.name}"`}
        open={searchModalVisible}
        onCancel={() => {
          setSearchModalVisible(false);
          setSearchResults([]);
          searchForm.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={searchForm}
          layout="vertical"
          onFinish={handleSearch}
          initialValues={{
            searchLevel: 'all',
            includeContext: true,
            threshold: 0.7,
            filterByCategory: false,
            filterByTags: false,
            filterByImportance: false,
            category: '',
            tags: [],
            importance: ''
          }}
        >
          <Form.Item
            name="query"
            label="搜索内容"
            rules={[{ required: true, message: '请输入搜索内容' }]}
          >
            <Input.Search
              placeholder="输入关键词搜索相关文档..."
              enterButton="搜索"
              size="large"
              loading={loading}
              onSearch={() => searchForm.submit()}
            />
          </Form.Item>

          {/* 高级搜索选项 */}
          <Card
            title="搜索选项"
            size="small"
            style={{ marginTop: '16px' }}
            bodyStyle={{ padding: '12px' }}
          >
            <Tabs defaultActiveKey="1">
              <Tabs.TabPane tab="向量搜索选项" key="1">
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>
                  <Form.Item
                    name="searchLevel"
                    label="搜索粒度"
                    tooltip="选择搜索的分块级别"
                  >
                    <Select placeholder="选择搜索级别">
                      <Select.Option value="all">
                        <Space>
                          <BulbOutlined style={{ color: '#722ed1' }} />
                          全部分块
                        </Space>
                      </Select.Option>
                      <Select.Option value="parent">
                        <Space>
                          <DatabaseOutlined style={{ color: '#1890ff' }} />
                          仅父分块
                        </Space>
                      </Select.Option>
                      <Select.Option value="child">
                        <Space>
                          <SearchOutlined style={{ color: '#52c41a' }} />
                          仅子分块
                        </Space>
                      </Select.Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="threshold"
                    label="相似度阈值"
                    tooltip="设置最低相似度要求"
                  >
                    <InputNumber
                      min={0.1}
                      max={1.0}
                      step={0.1}
                      placeholder="0.7"
                      style={{ width: '100%' }}
                      formatter={value => `${(value * 100).toFixed(0)}%`}
                      parser={value => value.replace('%', '') / 100}
                    />
                  </Form.Item>

                  <Form.Item
                    name="includeContext"
                    label="包含上下文"
                    valuePropName="checked"
                    tooltip="为子分块结果包含父分块上下文"
                  >
                    <Switch
                      checkedChildren="开启"
                      unCheckedChildren="关闭"
                    />
                  </Form.Item>
                </div>
              </Tabs.TabPane>

              <Tabs.TabPane tab="元数据筛选" key="2">
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
                  <Form.Item
                    name="filterByCategory"
                    label="按分类筛选"
                    valuePropName="checked"
                    tooltip="启用分类筛选"
                  >
                    <Switch
                      checkedChildren="开启"
                      unCheckedChildren="关闭"
                    />
                  </Form.Item>

                  <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                    prevValues.filterByCategory !== currentValues.filterByCategory
                  }>
                    {({ getFieldValue }) => {
                      const filterByCategory = getFieldValue('filterByCategory');
                      return filterByCategory ? (
                        <Form.Item
                          name="category"
                          label="选择分类"
                        >
                          <Select
                            placeholder="选择分类"
                            allowClear
                            options={[
                              { value: '技术文档', label: '技术文档' },
                              { value: '用户手册', label: '用户手册' },
                              { value: '产品说明', label: '产品说明' },
                              { value: '政策文件', label: '政策文件' },
                              { value: '培训资料', label: '培训资料' },
                              { value: '其他', label: '其他' }
                            ]}
                          />
                        </Form.Item>
                      ) : null;
                    }}
                  </Form.Item>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
                  <Form.Item
                    name="filterByTags"
                    label="按标签筛选"
                    valuePropName="checked"
                    tooltip="启用标签筛选"
                  >
                    <Switch
                      checkedChildren="开启"
                      unCheckedChildren="关闭"
                    />
                  </Form.Item>

                  <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                    prevValues.filterByTags !== currentValues.filterByTags
                  }>
                    {({ getFieldValue }) => {
                      const filterByTags = getFieldValue('filterByTags');
                      return filterByTags ? (
                        <Form.Item
                          name="tags"
                          label="选择标签"
                        >
                          <Select
                            mode="multiple"
                            placeholder="选择标签"
                            allowClear
                            options={[
                              { value: 'API', label: 'API' },
                              { value: '教程', label: '教程' },
                              { value: '指南', label: '指南' },
                              { value: '参考', label: '参考' },
                              { value: '示例', label: '示例' },
                              { value: '最佳实践', label: '最佳实践' }
                            ]}
                          />
                        </Form.Item>
                      ) : null;
                    }}
                  </Form.Item>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                  <Form.Item
                    name="filterByImportance"
                    label="按重要性筛选"
                    valuePropName="checked"
                    tooltip="启用重要性筛选"
                  >
                    <Switch
                      checkedChildren="开启"
                      unCheckedChildren="关闭"
                    />
                  </Form.Item>

                  <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                    prevValues.filterByImportance !== currentValues.filterByImportance
                  }>
                    {({ getFieldValue }) => {
                      const filterByImportance = getFieldValue('filterByImportance');
                      return filterByImportance ? (
                        <Form.Item
                          name="importance"
                          label="选择重要性"
                        >
                          <Select
                            placeholder="选择重要性级别"
                            allowClear
                          >
                            <Select.Option value="high">
                              <Space>
                                <span style={{ color: '#f5222d' }}>●</span>
                                高
                              </Space>
                            </Select.Option>
                            <Select.Option value="medium">
                              <Space>
                                <span style={{ color: '#fa8c16' }}>●</span>
                                中
                              </Space>
                            </Select.Option>
                            <Select.Option value="low">
                              <Space>
                                <span style={{ color: '#52c41a' }}>●</span>
                                低
                              </Space>
                            </Select.Option>
                          </Select>
                        </Form.Item>
                      ) : null;
                    }}
                  </Form.Item>
                </div>
              </Tabs.TabPane>
            </Tabs>
          </Card>
        </Form>

        {searchResults.length > 0 && (
          <div style={{
            maxHeight: '500px',
            overflow: 'auto',
            marginTop: '16px',
            border: '1px solid #434343',
            borderRadius: '6px',
            padding: '16px',
            background: 'transparent'
          }}>
            <Divider orientation="left">搜索结果 ({searchResults.length})</Divider>
            <List
              dataSource={searchResults}
              style={{ background: 'transparent' }}
              renderItem={(item) => (
                <List.Item
                  style={{
                    background: 'transparent',
                    borderBottom: '1px solid #434343'
                  }}
                >
                  <List.Item.Meta
                    avatar={<FileTextOutlined style={{ fontSize: '24px', color: '#1890ff' }} />}
                    title={
                      <Space wrap>
                        <Text strong>{item.title}</Text>
                        <Tag color="green">相关度: {(item.relevance * 100).toFixed(1)}%</Tag>
                        {item.avgSimilarity && (
                          <Tag color="blue">平均: {(item.avgSimilarity * 100).toFixed(1)}%</Tag>
                        )}
                        {item.hasHierarchy && (
                          <Tag color="purple" icon={<BulbOutlined />}>
                            分层结构
                          </Tag>
                        )}
                        {item.parentChunkCount > 0 && (
                          <Tag color="geekblue">
                            {item.parentChunkCount} 个父分块
                          </Tag>
                        )}
                        {item.childChunkCount > 0 && (
                          <Tag color="cyan">
                            {item.childChunkCount} 个子分块
                          </Tag>
                        )}
                      </Space>
                    }
                    description={
                      <div>
                        <Text type="secondary">
                          {item.content.substring(0, 200)}...
                        </Text>
                        <br />
                        <Space size="large" wrap style={{ marginTop: '8px' }}>
                          {item.relevantChunks && (
                            <span>
                              <Text type="secondary" style={{ fontSize: '12px' }}>
                                匹配块数: {item.relevantChunks}
                              </Text>
                            </span>
                          )}
                          <span>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              创建时间: {new Date(item.createdAt || Date.now()).toLocaleString()}
                            </Text>
                          </span>
                        </Space>

                        {/* 显示匹配的文本块 */}
                        {item.chunks && item.chunks.length > 0 && (
                          <div style={{ marginTop: '12px' }}>
                            <Text type="secondary" style={{ fontSize: '12px' }}>匹配片段:</Text>
                            {item.chunks.slice(0, 2).map((chunk, index) => (
                              <div
                                key={index}
                                style={{
                                  marginTop: '4px',
                                  padding: '8px',
                                  background: 'rgba(24, 144, 255, 0.1)',
                                  borderLeft: '3px solid #1890ff',
                                  borderRadius: '4px',
                                  fontSize: '12px'
                                }}
                              >
                                <Text>
                                  {chunk.text.substring(0, 150)}
                                  {chunk.text.length > 150 ? '...' : ''}
                                </Text>
                                <Tag size="small" color="green" style={{ marginLeft: '8px' }}>
                                  {(chunk.similarity * 100).toFixed(1)}%
                                </Tag>
                              </div>
                            ))}
                            {item.chunks.length > 2 && (
                              <Text type="secondary" style={{ fontSize: '11px' }}>
                                还有 {item.chunks.length - 2} 个匹配片段...
                              </Text>
                            )}
                          </div>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </div>
        )}
      </Modal>

      {/* 🚀 新增：统一的进度弹框 */}
      <Modal
        title={
          <Space>
            <DatabaseOutlined style={{ color: '#1890ff' }} />
            <span>{progressInfo.title}</span>
          </Space>
        }
        open={progressModalVisible}
        footer={null}
        closable={false}
        maskClosable={false}
        centered
        width={500}
      >
        <div style={{ padding: '20px 0' }}>
          <div style={{ marginBottom: '16px' }}>
            <Text strong style={{ fontSize: '14px' }}>
              {progressInfo.message}
            </Text>
          </div>

          <Progress
            percent={progressInfo.percent}
            status={progressInfo.percent === 100 ? 'success' : 'active'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            style={{ marginBottom: '12px' }}
          />

          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            color: '#666',
            fontSize: '12px'
          }}>
            <span>进度: {progressInfo.current}/{progressInfo.total}</span>
            <span>{progressInfo.percent}%</span>
          </div>
        </div>
      </Modal>

      {/* 🚀 新增：文档版本管理 */}
      <DocumentVersionManager
        documentId={currentDocumentForVersion?.id}
        currentContent={currentDocumentForVersion?.content}
        visible={versionManagerVisible}
        onClose={() => {
          setVersionManagerVisible(false);
          setCurrentDocumentForVersion(null);
        }}
        onVersionRestore={(restoredVersion) => {
          // 处理版本恢复
          console.log('📝 [VersionManager] 版本已恢复:', restoredVersion);
          // TODO: 更新文档内容
        }}
      />

      {/* 🚀 新增：文档处理器界面 */}
      <DocumentProcessorInterface
        visible={documentProcessorVisible}
        onClose={() => setDocumentProcessorVisible(false)}
        onDocumentProcessed={(results) => {
          console.log('📄 [DocumentProcessor] 文档处理完成:', results);
          // TODO: 将处理结果添加到知识库
          message.success(`成功处理 ${results.results?.length || 0} 个文档`);
        }}
      />

      {/* 🚀 新增：知识图谱可视化 */}
      <KnowledgeGraphVisualization
        visible={knowledgeGraphVisible}
        onClose={() => setKnowledgeGraphVisible(false)}
        knowledgeBases={knowledgeBases}
      />

      {/* 🚀 新增：开发者调试面板 */}
      <DeveloperDebugPanel
        visible={debugPanelVisible}
        onClose={() => setDebugPanelVisible(false)}
        style={{
          position: 'fixed',
          top: '10%',
          left: '10%',
          right: '10%',
          bottom: '10%',
          zIndex: 1000,
          maxHeight: '80vh',
          overflow: 'auto'
        }}
      />
    </div>
  );
};

export default KnowledgeBase;
