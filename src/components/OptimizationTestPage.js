import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Alert,
  Row,
  Col,
  Statistic,
  Tag,
  List,
  message,
  Progress
} from 'antd';
import {
  ThunderboltOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  PlayCircleOutlined,
  BugOutlined
} from '@ant-design/icons';
import {
  isOptimizedFeaturesAvailable,
  getOptimizationStatus,
  optimizedVectorSearch,
  smartCache,
  documentProcessor,
  versionManager,
  performanceMonitor
} from '../utils/optimizedFeatures';

const { Text, Title } = Typography;

/**
 * 🧪 优化功能测试页面
 * 用于验证所有优化功能是否正常工作
 */
const OptimizationTestPage = () => {
  const [testResults, setTestResults] = useState([]);
  const [testing, setTesting] = useState(false);
  const [optimizationStatus, setOptimizationStatus] = useState(null);
  const [testProgress, setTestProgress] = useState(0);

  // 加载优化状态
  useEffect(() => {
    const status = getOptimizationStatus();
    setOptimizationStatus(status);
  }, []);

  // 添加测试结果
  const addTestResult = (name, success, message, data = null) => {
    setTestResults(prev => [...prev, {
      id: Date.now(),
      name,
      success,
      message,
      data,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  // 运行所有测试
  const runAllTests = async () => {
    setTesting(true);
    setTestResults([]);
    setTestProgress(0);

    const tests = [
      { name: '优化功能可用性检查', test: testOptimizationAvailability },
      { name: '向量搜索性能测试', test: testVectorSearch },
      { name: '智能缓存功能测试', test: testSmartCache },
      { name: '文档处理功能测试', test: testDocumentProcessor },
      { name: '版本管理功能测试', test: testVersionManager },
      { name: '性能监控功能测试', test: testPerformanceMonitor }
    ];

    for (let i = 0; i < tests.length; i++) {
      const { name, test } = tests[i];
      
      try {
        addTestResult(name, null, '测试中...', null);
        await test();
        setTestProgress(((i + 1) / tests.length) * 100);
        await new Promise(resolve => setTimeout(resolve, 500)); // 短暂延迟
      } catch (error) {
        addTestResult(name, false, `测试失败: ${error.message}`, error);
      }
    }

    setTesting(false);
    message.success('所有测试完成');
  };

  // 测试优化功能可用性
  const testOptimizationAvailability = async () => {
    const available = isOptimizedFeaturesAvailable();
    const status = getOptimizationStatus();
    
    if (available) {
      addTestResult('优化功能可用性检查', true, '优化功能可用', status);
    } else {
      addTestResult('优化功能可用性检查', false, '优化功能不可用，使用基础模式', status);
    }
  };

  // 测试向量搜索
  const testVectorSearch = async () => {
    try {
      // 创建测试向量
      const testVector = new Array(1536).fill(0).map(() => Math.random());
      const testVectors = [
        { id: 'test1', vector: testVector, metadata: { test: true } },
        { id: 'test2', vector: new Array(1536).fill(0).map(() => Math.random()), metadata: { test: true } }
      ];

      const startTime = Date.now();
      const results = await optimizedVectorSearch.search(testVector, testVectors, { topK: 1 });
      const searchTime = Date.now() - startTime;

      if (results && results.length > 0) {
        addTestResult('向量搜索性能测试', true, `搜索成功，耗时 ${searchTime}ms`, { searchTime, resultCount: results.length });
        performanceMonitor.recordSearchTime(searchTime, results.length, true);
      } else {
        addTestResult('向量搜索性能测试', false, '搜索返回空结果', null);
      }
    } catch (error) {
      addTestResult('向量搜索性能测试', false, `搜索失败: ${error.message}`, error);
    }
  };

  // 测试智能缓存
  const testSmartCache = async () => {
    try {
      const testKey = 'test_cache_key';
      const testData = { test: 'data', timestamp: Date.now() };

      // 设置缓存
      await smartCache.set(testKey, testData, 'test');
      
      // 获取缓存
      const cached = await smartCache.get(testKey, 'test');
      
      if (cached && cached.test === testData.test) {
        addTestResult('智能缓存功能测试', true, '缓存读写成功', { testData, cached });
      } else {
        addTestResult('智能缓存功能测试', false, '缓存数据不匹配', { testData, cached });
      }
    } catch (error) {
      addTestResult('智能缓存功能测试', false, `缓存测试失败: ${error.message}`, error);
    }
  };

  // 测试文档处理
  const testDocumentProcessor = async () => {
    try {
      const formats = await documentProcessor.getSupportedFormats();
      
      if (formats && formats.all && formats.all.length > 0) {
        addTestResult('文档处理功能测试', true, `支持 ${formats.all.length} 种文件格式`, formats);
      } else {
        addTestResult('文档处理功能测试', false, '无法获取支持的文件格式', formats);
      }
    } catch (error) {
      addTestResult('文档处理功能测试', false, `文档处理器测试失败: ${error.message}`, error);
    }
  };

  // 测试版本管理
  const testVersionManager = async () => {
    try {
      const testDocId = 'test_document';
      const testContent = 'This is test content for version management.';
      
      // 尝试保存版本
      const version = await versionManager.saveVersion(testDocId, testContent, {
        comment: '测试版本',
        author: 'test'
      });

      if (version) {
        addTestResult('版本管理功能测试', true, '版本保存成功', version);
      } else {
        addTestResult('版本管理功能测试', true, '版本管理可用（内容无变化）', null);
      }
    } catch (error) {
      addTestResult('版本管理功能测试', false, `版本管理测试失败: ${error.message}`, error);
    }
  };

  // 测试性能监控
  const testPerformanceMonitor = async () => {
    try {
      const stats = await performanceMonitor.getStats();
      
      if (stats) {
        addTestResult('性能监控功能测试', true, '性能统计获取成功', stats);
      } else {
        addTestResult('性能监控功能测试', false, '无法获取性能统计', null);
      }
    } catch (error) {
      addTestResult('性能监控功能测试', false, `性能监控测试失败: ${error.message}`, error);
    }
  };

  // 获取测试结果统计
  const getTestStats = () => {
    const total = testResults.length;
    const successful = testResults.filter(r => r.success === true).length;
    const failed = testResults.filter(r => r.success === false).length;
    const running = testResults.filter(r => r.success === null).length;
    
    return { total, successful, failed, running };
  };

  const stats = getTestStats();

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <BugOutlined style={{ color: '#fa8c16' }} />
            <span>优化功能测试页面</span>
            {optimizationStatus && (
              <Tag color={optimizationStatus.available ? 'green' : 'default'}>
                {optimizationStatus.available ? '优化模式' : '基础模式'}
              </Tag>
            )}
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            loading={testing}
            onClick={runAllTests}
          >
            {testing ? '测试中...' : '运行所有测试'}
          </Button>
        }
      >
        {/* 优化状态概览 */}
        {optimizationStatus && (
          <Alert
            message={optimizationStatus.available ? '🚀 优化功能已启用' : '📝 基础模式运行'}
            description={
              <div>
                <Text>功能状态：</Text>
                <Space wrap style={{ marginLeft: 8 }}>
                  {Object.entries(optimizationStatus.features).map(([feature, available]) => (
                    <Tag
                      key={feature}
                      color={available ? 'green' : 'default'}
                      icon={available ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
                    >
                      {feature}
                    </Tag>
                  ))}
                </Space>
              </div>
            }
            type={optimizationStatus.available ? 'success' : 'info'}
            style={{ marginBottom: 16 }}
          />
        )}

        {/* 测试进度 */}
        {testing && (
          <div style={{ marginBottom: 16 }}>
            <Progress percent={Math.round(testProgress)} status="active" />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              正在运行测试...
            </Text>
          </div>
        )}

        <Row gutter={16}>
          {/* 测试统计 */}
          <Col span={8}>
            <Card title="测试统计" size="small">
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="总测试数"
                    value={stats.total}
                    prefix={<ThunderboltOutlined />}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="成功"
                    value={stats.successful}
                    valueStyle={{ color: '#3f8600' }}
                    prefix={<CheckCircleOutlined />}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="失败"
                    value={stats.failed}
                    valueStyle={{ color: '#cf1322' }}
                    prefix={<CloseCircleOutlined />}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="运行中"
                    value={stats.running}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 测试结果 */}
          <Col span={16}>
            <Card title="测试结果" size="small">
              <List
                size="small"
                dataSource={testResults}
                style={{ height: '300px', overflow: 'auto' }}
                renderItem={(result) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        result.success === true ? (
                          <CheckCircleOutlined style={{ color: '#52c41a' }} />
                        ) : result.success === false ? (
                          <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                        ) : (
                          <ThunderboltOutlined style={{ color: '#1890ff' }} />
                        )
                      }
                      title={
                        <Space>
                          <Text strong>{result.name}</Text>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {result.timestamp}
                          </Text>
                        </Space>
                      }
                      description={
                        <div>
                          <Text style={{ fontSize: '12px' }}>{result.message}</Text>
                          {result.data && (
                            <details style={{ marginTop: 4 }}>
                              <summary style={{ fontSize: '11px', color: '#666', cursor: 'pointer' }}>
                                查看详细数据
                              </summary>
                              <pre style={{ 
                                fontSize: '10px', 
                                background: '#f5f5f5', 
                                padding: '4px',
                                borderRadius: '2px',
                                marginTop: '4px',
                                overflow: 'auto',
                                maxHeight: '100px'
                              }}>
                                {JSON.stringify(result.data, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default OptimizationTestPage;
