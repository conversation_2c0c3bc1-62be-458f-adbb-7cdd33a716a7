import React, { memo, useState, useMemo, useCallback } from 'react';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import MarkdownRenderer from './MarkdownRenderer';

// 🚀 可折叠内容组件 - 优化长消息性能
const CollapsibleContent = memo(({ 
  content, 
  maxHeight = 300, 
  previewLines = 5,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // 🎯 检测内容是否需要折叠
  const shouldCollapse = useMemo(() => {
    if (!content) return false;
    
    // 基于内容长度判断
    if (content.length > 1000) return true;
    
    // 基于行数判断
    const lines = content.split('\n').length;
    if (lines > previewLines) return true;
    
    // 基于代码块数量判断
    const codeBlocks = (content.match(/```/g) || []).length / 2;
    if (codeBlocks > 2) return true;
    
    return false;
  }, [content, previewLines]);

  // 🎯 生成预览内容
  const previewContent = useMemo(() => {
    if (!shouldCollapse || isExpanded) return content;
    
    const lines = content.split('\n');
    if (lines.length <= previewLines) return content;
    
    // 取前几行作为预览
    const preview = lines.slice(0, previewLines).join('\n');
    return preview + '\n\n...';
  }, [content, previewLines, shouldCollapse, isExpanded]);

  // 🎯 切换展开状态
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  // 🎯 如果不需要折叠，直接渲染完整内容
  if (!shouldCollapse) {
    return (
      <div className={className}>
        <MarkdownRenderer content={content} />
      </div>
    );
  }

  return (
    <div className={className}>
      {/* 内容区域 */}
      <div 
        style={{
          maxHeight: isExpanded ? 'none' : `${maxHeight}px`,
          overflow: 'hidden',
          transition: 'max-height 0.3s ease-in-out',
          position: 'relative'
        }}
      >
        <MarkdownRenderer content={previewContent} />
        
        {/* 渐变遮罩 */}
        {!isExpanded && (
          <div style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: '40px',
            background: 'linear-gradient(transparent, rgba(26, 31, 46, 0.9))',
            pointerEvents: 'none'
          }} />
        )}
      </div>

      {/* 展开/折叠按钮 */}
      <div
        onClick={toggleExpanded}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '6px',
          marginTop: '8px',
          padding: '6px 12px',
          background: 'rgba(0, 212, 255, 0.1)',
          border: '1px solid rgba(0, 212, 255, 0.3)',
          borderRadius: '8px',
          color: '#00d4ff',
          fontSize: '12px',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          fontWeight: '500',
          userSelect: 'none'
        }}
        onMouseEnter={(e) => {
          e.target.style.background = 'rgba(0, 212, 255, 0.2)';
          e.target.style.borderColor = '#00d4ff';
          e.target.style.boxShadow = '0 0 8px rgba(0, 212, 255, 0.3)';
        }}
        onMouseLeave={(e) => {
          e.target.style.background = 'rgba(0, 212, 255, 0.1)';
          e.target.style.borderColor = 'rgba(0, 212, 255, 0.3)';
          e.target.style.boxShadow = 'none';
        }}
      >
        {isExpanded ? (
          <>
            <UpOutlined style={{ fontSize: '10px' }} />
            <span>收起内容</span>
          </>
        ) : (
          <>
            <DownOutlined style={{ fontSize: '10px' }} />
            <span>展开完整内容</span>
          </>
        )}
      </div>
    </div>
  );
});

CollapsibleContent.displayName = 'CollapsibleContent';

export default CollapsibleContent;
