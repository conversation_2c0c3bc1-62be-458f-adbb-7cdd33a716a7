import React, { memo, useState, useMemo, useCallback } from 'react';
import { DownOutlined, UpOutlined, ThunderboltOutlined } from '@ant-design/icons';
import MarkdownRenderer from './MarkdownRenderer';
import { ThinkingCopyButton } from './CopyButton';

// 🚀 思考内容组件 - 支持可折叠显示和科技感样式
const ThinkingSection = memo(({ 
  content, 
  sectionId,
  defaultExpanded = false,
  showCopyButton = true,
  className = '',
  style = {}
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  // 🎯 切换展开状态
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  // 🎯 生成预览内容（折叠时显示前几行）
  const previewContent = useMemo(() => {
    if (isExpanded || !content) return content;
    
    const lines = content.split('\n');
    if (lines.length <= 3) return content;
    
    // 取前3行作为预览
    const preview = lines.slice(0, 3).join('\n');
    return preview + '\n\n...';
  }, [content, isExpanded]);

  // 🎯 如果没有内容，不渲染
  if (!content || !content.trim()) {
    return null;
  }

  return (
    <div 
      className={`thinking-section ${className}`}
      style={{
        marginTop: '16px',
        position: 'relative',
        ...style
      }}
    >
      {/* 🎨 思考内容容器 */}
      <div style={{
        background: 'rgba(26, 31, 46, 0.8)',
        border: '1px solid rgba(138, 43, 226, 0.3)',
        borderRadius: '12px',
        overflow: 'hidden',
        boxShadow: '0 4px 20px rgba(138, 43, 226, 0.1)',
        backdropFilter: 'blur(20px)',
        position: 'relative'
      }}>
        {/* 🎨 背景动画效果 */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'linear-gradient(45deg, transparent 30%, rgba(138, 43, 226, 0.05) 50%, transparent 70%)',
          animation: 'shimmer 4s ease-in-out infinite',
          transform: 'translateX(-100%)',
          pointerEvents: 'none'
        }} />

        {/* 🎯 思考标题栏 */}
        <div 
          onClick={toggleExpanded}
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '12px 16px',
            cursor: 'pointer',
            borderBottom: isExpanded ? '1px solid rgba(138, 43, 226, 0.2)' : 'none',
            position: 'relative',
            zIndex: 1,
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = 'rgba(138, 43, 226, 0.1)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
          }}
        >
          {/* 左侧标题 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            color: '#8a2be2',
            fontWeight: '600',
            fontSize: '12px',
            textTransform: 'uppercase',
            letterSpacing: '0.5px',
            textShadow: '0 0 8px rgba(138, 43, 226, 0.3)'
          }}>
            <ThunderboltOutlined style={{
              fontSize: '14px',
              filter: 'drop-shadow(0 0 5px rgba(138, 43, 226, 0.5))'
            }} />
            <span>思考过程</span>
          </div>

          {/* 右侧控制区域 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            {/* 复制按钮 */}
            {showCopyButton && (
              <div onClick={(e) => e.stopPropagation()}>
                <ThinkingCopyButton
                  content={content}
                  className="thinking-copy-btn"
                />
              </div>
            )}
            
            {/* 展开/折叠图标 */}
            <div style={{
              color: '#8a2be2',
              fontSize: '12px',
              transition: 'transform 0.2s ease',
              transform: isExpanded ? 'rotate(0deg)' : 'rotate(-90deg)'
            }}>
              {isExpanded ? <UpOutlined /> : <DownOutlined />}
            </div>
          </div>
        </div>

        {/* 🎯 思考内容区域 */}
        <div style={{
          maxHeight: isExpanded ? 'none' : '0px',
          overflow: 'hidden',
          transition: 'max-height 0.3s ease-in-out',
          position: 'relative',
          zIndex: 1
        }}>
          <div style={{
            padding: isExpanded ? '16px' : '0 16px',
            transition: 'padding 0.3s ease-in-out'
          }}>
            <MarkdownRenderer 
              content={isExpanded ? content : previewContent} 
              className="thinking-content" 
            />
          </div>
        </div>

        {/* 🎯 折叠状态下的预览提示 */}
        {!isExpanded && (
          <div style={{
            padding: '8px 16px',
            fontSize: '11px',
            color: 'rgba(138, 43, 226, 0.7)',
            fontStyle: 'italic',
            textAlign: 'center',
            borderTop: '1px solid rgba(138, 43, 226, 0.1)',
            position: 'relative',
            zIndex: 1
          }}>
            点击展开查看完整思考过程
          </div>
        )}
      </div>
    </div>
  );
});

ThinkingSection.displayName = 'ThinkingSection';

export default ThinkingSection;
