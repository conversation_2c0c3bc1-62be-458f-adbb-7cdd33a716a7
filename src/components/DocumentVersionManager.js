import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Button,
  Space,
  Tag,
  Typography,
  Modal,
  Tooltip,
  message,
  Popconfirm,
  Divider,
  Alert,
  Timeline,
  Descriptions
} from 'antd';
import {
  HistoryOutlined,
  EyeOutlined,
  RollbackOutlined,
  DeleteOutlined,
  DiffOutlined,
  SaveOutlined,
  ClockCircleOutlined,
  UserOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { versionManager } from '../utils/optimizedFeatures';

const { Text, Title, Paragraph } = Typography;

/**
 * 📝 文档版本管理组件
 * 显示文档版本历史、对比差异、恢复版本等功能
 */
const DocumentVersionManager = ({ 
  documentId, 
  currentContent,
  onVersionRestore,
  visible,
  onClose,
  style = {} 
}) => {
  const [versions, setVersions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [compareModalVisible, setCompareModalVisible] = useState(false);
  const [compareResult, setCompareResult] = useState(null);
  const [selectedVersions, setSelectedVersions] = useState([]);

  // 加载版本列表
  const loadVersions = async () => {
    if (!documentId) return;
    
    setLoading(true);
    try {
      const versionList = await versionManager.getVersions(documentId);
      setVersions(versionList);
      console.log(`📝 [VersionManager] 加载了 ${versionList.length} 个版本`);
    } catch (error) {
      console.error('❌ [VersionManager] 加载版本失败:', error);
      message.error('加载版本历史失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存当前版本
  const saveCurrentVersion = async () => {
    if (!documentId || !currentContent) return;
    
    try {
      const version = await versionManager.saveVersion(documentId, currentContent, {
        comment: '手动保存',
        author: 'user'
      });
      
      if (version) {
        message.success('版本保存成功');
        await loadVersions();
      } else {
        message.info('内容无变化，未创建新版本');
      }
    } catch (error) {
      console.error('❌ [VersionManager] 保存版本失败:', error);
      message.error('保存版本失败');
    }
  };

  // 恢复版本
  const handleRestore = async (versionId) => {
    try {
      const restoredVersion = await versionManager.restoreVersion(documentId, versionId);
      message.success('版本恢复成功');
      onVersionRestore?.(restoredVersion);
      await loadVersions();
    } catch (error) {
      console.error('❌ [VersionManager] 恢复版本失败:', error);
      message.error('恢复版本失败');
    }
  };

  // 比较版本
  const handleCompare = async (version1Id, version2Id) => {
    try {
      const result = await versionManager.compareVersions(documentId, version1Id, version2Id);
      setCompareResult(result);
      setCompareModalVisible(true);
    } catch (error) {
      console.error('❌ [VersionManager] 版本比较失败:', error);
      message.error('版本比较失败');
    }
  };

  // 删除版本
  const handleDelete = async (versionId) => {
    try {
      await versionManager.deleteVersion(documentId, versionId);
      message.success('版本删除成功');
      await loadVersions();
    } catch (error) {
      console.error('❌ [VersionManager] 删除版本失败:', error);
      message.error('删除版本失败');
    }
  };

  // 选择版本进行比较
  const toggleVersionSelection = (versionId) => {
    setSelectedVersions(prev => {
      if (prev.includes(versionId)) {
        return prev.filter(id => id !== versionId);
      } else if (prev.length < 2) {
        return [...prev, versionId];
      } else {
        return [prev[1], versionId]; // 保持最多2个选择
      }
    });
  };

  // 格式化文件大小
  const formatSize = (bytes) => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  };

  // 渲染差异对比
  const renderDiffView = (diff) => {
    if (!diff || !diff.lines) return null;

    return (
      <div style={{ fontFamily: 'monospace', fontSize: '12px' }}>
        {diff.lines.map((change, index) => (
          <div
            key={index}
            style={{
              padding: '2px 8px',
              backgroundColor: change.added 
                ? '#e6ffed' 
                : change.removed 
                ? '#ffebee' 
                : 'transparent',
              borderLeft: change.added 
                ? '3px solid #52c41a' 
                : change.removed 
                ? '3px solid #ff4d4f' 
                : 'none'
            }}
          >
            <Text 
              style={{ 
                color: change.added 
                  ? '#389e0d' 
                  : change.removed 
                  ? '#cf1322' 
                  : 'inherit'
              }}
            >
              {change.added && '+ '}
              {change.removed && '- '}
              {change.value}
            </Text>
          </div>
        ))}
      </div>
    );
  };

  useEffect(() => {
    if (visible && documentId) {
      loadVersions();
    }
  }, [visible, documentId]);

  return (
    <>
      <Modal
        title={
          <Space>
            <HistoryOutlined style={{ color: '#722ed1' }} />
            <span>文档版本管理</span>
            <Tag color="purple">{versions.length} 个版本</Tag>
          </Space>
        }
        open={visible}
        onCancel={onClose}
        width={800}
        footer={
          <Space>
            <Button onClick={onClose}>关闭</Button>
            <Button 
              type="primary" 
              icon={<SaveOutlined />}
              onClick={saveCurrentVersion}
            >
              保存当前版本
            </Button>
            {selectedVersions.length === 2 && (
              <Button 
                icon={<DiffOutlined />}
                onClick={() => handleCompare(selectedVersions[0], selectedVersions[1])}
              >
                比较选中版本
              </Button>
            )}
          </Space>
        }
        style={style}
      >
        {versions.length === 0 ? (
          <Alert
            message="暂无版本历史"
            description="保存第一个版本来开始版本管理。"
            type="info"
            showIcon
          />
        ) : (
          <>
            <Alert
              message="版本管理说明"
              description={
                <div>
                  <Text>• 点击版本可选择进行比较（最多选择2个）</Text><br />
                  <Text>• 选中版本后可以恢复到该版本</Text><br />
                  <Text>• 版本按时间倒序排列，最新的在上面</Text>
                </div>
              }
              type="info"
              style={{ marginBottom: 16 }}
            />

            <List
              loading={loading}
              dataSource={versions}
              renderItem={(version, index) => {
                const isSelected = selectedVersions.includes(version.id);
                const isLatest = index === 0;
                
                return (
                  <List.Item
                    style={{
                      backgroundColor: isSelected ? '#f0f8ff' : 'transparent',
                      border: isSelected ? '1px solid #1890ff' : '1px solid transparent',
                      borderRadius: '4px',
                      margin: '4px 0',
                      padding: '12px',
                      cursor: 'pointer'
                    }}
                    onClick={() => toggleVersionSelection(version.id)}
                    actions={[
                      <Tooltip title="查看版本">
                        <Button 
                          size="small" 
                          icon={<EyeOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            // TODO: 实现版本查看功能
                          }}
                        />
                      </Tooltip>,
                      <Tooltip title="恢复到此版本">
                        <Popconfirm
                          title="确定要恢复到此版本吗？"
                          onConfirm={(e) => {
                            e?.stopPropagation();
                            handleRestore(version.id);
                          }}
                          onCancel={(e) => e?.stopPropagation()}
                        >
                          <Button
                            size="small"
                            icon={<RollbackOutlined />}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </Popconfirm>
                      </Tooltip>,
                      !isLatest && (
                        <Tooltip title="删除版本">
                          <Popconfirm
                            title="确定要删除此版本吗？"
                            onConfirm={(e) => {
                              e?.stopPropagation();
                              handleDelete(version.id);
                            }}
                            onCancel={(e) => e?.stopPropagation()}
                          >
                            <Button 
                              size="small" 
                              icon={<DeleteOutlined />}
                              danger
                              onClick={(e) => e.stopPropagation()}
                            />
                          </Popconfirm>
                        </Tooltip>
                      )
                    ].filter(Boolean)}
                  >
                    <List.Item.Meta
                      avatar={
                        <div style={{ textAlign: 'center' }}>
                          <FileTextOutlined style={{ fontSize: '20px', color: '#1890ff' }} />
                          {isLatest && <Tag color="green" size="small">最新</Tag>}
                        </div>
                      }
                      title={
                        <Space>
                          <Text strong>版本 {versions.length - index}</Text>
                          <Tag color="blue">{formatSize(version.size)}</Tag>
                          {version.metadata?.comment && (
                            <Text type="secondary">- {version.metadata.comment}</Text>
                          )}
                        </Space>
                      }
                      description={
                        <Space direction="vertical" size="small">
                          <Space>
                            <ClockCircleOutlined />
                            <Text type="secondary">
                              {new Date(version.timestamp).toLocaleString()}
                            </Text>
                          </Space>
                          {version.metadata?.author && (
                            <Space>
                              <UserOutlined />
                              <Text type="secondary">{version.metadata.author}</Text>
                            </Space>
                          )}
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            哈希: {version.hash.substring(0, 8)}...
                          </Text>
                        </Space>
                      }
                    />
                  </List.Item>
                );
              }}
            />
          </>
        )}
      </Modal>

      {/* 版本比较对话框 */}
      <Modal
        title={
          <Space>
            <DiffOutlined style={{ color: '#fa8c16' }} />
            <span>版本差异对比</span>
          </Space>
        }
        open={compareModalVisible}
        onCancel={() => setCompareModalVisible(false)}
        width={1000}
        footer={
          <Button onClick={() => setCompareModalVisible(false)}>
            关闭
          </Button>
        }
      >
        {compareResult && (
          <div>
            <Descriptions bordered size="small" style={{ marginBottom: 16 }}>
              <Descriptions.Item label="版本1" span={1}>
                {new Date(compareResult.version1.timestamp).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="版本2" span={1}>
                {new Date(compareResult.version2.timestamp).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="相似度" span={1}>
                {compareResult.statistics.similarity}%
              </Descriptions.Item>
              <Descriptions.Item label="新增行" span={1}>
                <Text style={{ color: '#52c41a' }}>+{compareResult.statistics.addedLines}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="删除行" span={1}>
                <Text style={{ color: '#ff4d4f' }}>-{compareResult.statistics.removedLines}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="总变更" span={1}>
                {compareResult.statistics.totalChanges}
              </Descriptions.Item>
            </Descriptions>

            <Divider>差异详情</Divider>
            <div style={{ 
              maxHeight: '400px', 
              overflow: 'auto',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              padding: '8px'
            }}>
              {renderDiffView(compareResult.diff)}
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};

export default DocumentVersionManager;
