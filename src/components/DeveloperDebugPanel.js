import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Tabs,
  Typography,
  Space,
  Button,
  Tag,
  Table,
  Progress,
  Alert,
  Statistic,
  Row,
  Col,
  List,
  Badge,
  Tooltip,
  Switch
} from 'antd';
import {
  BugOutlined,
  CodeOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  ClearOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { 
  isOptimizedFeaturesAvailable, 
  getOptimizationStatus,
  performanceMonitor 
} from '../utils/optimizedFeatures';

const { Text, Title, Paragraph } = Typography;
const { TabPane } = Tabs;

/**
 * 🛠️ 开发者调试面板
 * 显示优化功能的详细状态、性能指标和调试信息
 */
const DeveloperDebugPanel = ({ visible, onClose, style = {} }) => {
  const [logs, setLogs] = useState([]);
  const [realTimeStats, setRealTimeStats] = useState(null);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [serviceStatus, setServiceStatus] = useState(null);
  const intervalRef = useRef(null);

  // 模拟日志收集
  const addLog = (level, message, data = null) => {
    const log = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      level,
      message,
      data
    };
    
    setLogs(prev => [log, ...prev.slice(0, 99)]); // 保留最近100条
  };

  // 开始实时监控
  const startMonitoring = () => {
    setIsMonitoring(true);
    addLog('info', '🚀 开始实时性能监控');
    
    intervalRef.current = setInterval(async () => {
      try {
        const stats = await performanceMonitor.getStats();
        setRealTimeStats(stats);
        
        // 检查性能阈值
        if (stats.local?.avgSearchTime > 1000) {
          addLog('warn', '⚠️ 搜索性能警告：平均搜索时间超过1秒', { avgTime: stats.local.avgSearchTime });
        }
        
        if (stats.service) {
          setServiceStatus(stats.service);
        }
      } catch (error) {
        addLog('error', '❌ 获取性能统计失败', error.message);
      }
    }, 2000);
  };

  // 停止监控
  const stopMonitoring = () => {
    setIsMonitoring(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    addLog('info', '⏹️ 停止实时监控');
  };

  // 清空日志
  const clearLogs = () => {
    setLogs([]);
    addLog('info', '🧹 日志已清空');
  };

  // 测试优化功能
  const testOptimizedFeatures = async () => {
    addLog('info', '🧪 开始测试优化功能');
    
    const status = getOptimizationStatus();
    addLog('info', '📊 优化状态检查完成', status);
    
    if (status.available) {
      try {
        // 测试向量搜索
        if (window.electronAPI?.vectorSearch) {
          const testVector = new Array(1536).fill(0).map(() => Math.random());
          const testVectors = [
            { id: 'test1', vector: testVector, metadata: { test: true } }
          ];
          
          const startTime = Date.now();
          await window.electronAPI.vectorSearch(testVector, testVectors, { topK: 1 });
          const searchTime = Date.now() - startTime;
          
          addLog('success', `✅ 向量搜索测试成功，耗时: ${searchTime}ms`);
          performanceMonitor.recordSearchTime(searchTime, 1, true);
        }
        
        // 测试缓存
        if (window.electronAPI?.cache) {
          await window.electronAPI.cache.set('test_key', { test: 'data' }, 'test');
          const cached = await window.electronAPI.cache.get('test_key', 'test');
          
          if (cached && cached.test === 'data') {
            addLog('success', '✅ 缓存测试成功');
          } else {
            addLog('error', '❌ 缓存测试失败');
          }
        }
        
      } catch (error) {
        addLog('error', '❌ 优化功能测试失败', error.message);
      }
    } else {
      addLog('warn', '⚠️ 优化功能不可用，跳过测试');
    }
  };

  // 获取日志级别颜色
  const getLogLevelColor = (level) => {
    switch (level) {
      case 'error': return 'red';
      case 'warn': return 'orange';
      case 'success': return 'green';
      case 'info': return 'blue';
      default: return 'default';
    }
  };

  // 获取日志级别图标
  const getLogLevelIcon = (level) => {
    switch (level) {
      case 'error': return '❌';
      case 'warn': return '⚠️';
      case 'success': return '✅';
      case 'info': return 'ℹ️';
      default: return '📝';
    }
  };

  useEffect(() => {
    if (visible) {
      addLog('info', '🛠️ 开发者调试面板已打开');
      
      // 初始化时获取一次状态
      const status = getOptimizationStatus();
      addLog('info', '📊 初始化优化状态', status);
    }
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [visible]);

  if (!visible) return null;

  return (
    <Card
      title={
        <Space>
          <BugOutlined style={{ color: '#fa8c16' }} />
          <span>开发者调试面板</span>
          <Badge status={isOptimizedFeaturesAvailable() ? 'success' : 'default'} />
        </Space>
      }
      extra={
        <Button size="small" onClick={onClose}>
          关闭
        </Button>
      }
      style={style}
    >
      <Tabs defaultActiveKey="logs">
        {/* 实时日志 */}
        <TabPane
          tab={
            <Space>
              <CodeOutlined />
              <span>实时日志</span>
              <Badge count={logs.length} size="small" />
            </Space>
          }
          key="logs"
        >
          <Space style={{ marginBottom: 16 }}>
            <Button 
              icon={<PlayCircleOutlined />} 
              onClick={testOptimizedFeatures}
              type="primary"
              size="small"
            >
              测试优化功能
            </Button>
            <Button 
              icon={<ClearOutlined />} 
              onClick={clearLogs}
              size="small"
            >
              清空日志
            </Button>
          </Space>
          
          <div
            style={{
              height: '400px',
              overflow: 'auto',
              background: '#1f1f1f',
              border: '1px solid #434343',
              borderRadius: '6px',
              padding: '8px'
            }}
          >
            <List
              size="small"
              dataSource={logs}
              style={{ background: 'transparent' }}
              renderItem={(log) => (
                <List.Item
                  style={{
                    padding: '6px 8px',
                    borderBottom: '1px solid #333',
                    background: 'transparent'
                  }}
                >
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <Space>
                      <Text style={{ fontSize: '11px', color: '#888', fontFamily: 'monospace' }}>
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </Text>
                      <Tag color={getLogLevelColor(log.level)} size="small">
                        {log.level.toUpperCase()}
                      </Tag>
                      <Text style={{
                        fontFamily: 'monospace',
                        fontSize: '12px',
                        color: '#e6e6e6'
                      }}>
                        {getLogLevelIcon(log.level)} {log.message}
                      </Text>
                    </Space>
                    {log.data && (
                      <div
                        style={{
                          fontSize: '11px',
                          background: '#2d2d2d',
                          color: '#a6e22e',
                          padding: '8px',
                          borderRadius: '4px',
                          border: '1px solid #444',
                          fontFamily: 'monospace',
                          whiteSpace: 'pre-wrap',
                          overflow: 'auto',
                          maxHeight: '200px'
                        }}
                      >
                        {typeof log.data === 'string' ? log.data : JSON.stringify(log.data, null, 2)}
                      </div>
                    )}
                  </Space>
                </List.Item>
              )}
            />
          </div>
        </TabPane>

        {/* 性能监控 */}
        <TabPane 
          tab={
            <Space>
              <BarChartOutlined />
              <span>性能监控</span>
              {isMonitoring && <Badge status="processing" />}
            </Space>
          } 
          key="performance"
        >
          <Space style={{ marginBottom: 16 }}>
            <Switch
              checked={isMonitoring}
              onChange={isMonitoring ? stopMonitoring : startMonitoring}
              checkedChildren="监控中"
              unCheckedChildren="已停止"
            />
            <Button 
              icon={<ReloadOutlined />} 
              onClick={async () => {
                const stats = await performanceMonitor.getStats();
                setRealTimeStats(stats);
                addLog('info', '🔄 手动刷新性能数据');
              }}
              size="small"
            >
              刷新数据
            </Button>
          </Space>

          {realTimeStats ? (
            <Row gutter={16}>
              <Col span={12}>
                <Card size="small" title="本地统计">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="平均搜索时间"
                        value={realTimeStats.local?.avgSearchTime || 0}
                        suffix="ms"
                        precision={1}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="总搜索次数"
                        value={realTimeStats.local?.totalSearches || 0}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="缓存命中率"
                        value={realTimeStats.local?.cacheHitRate || '0%'}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="主进程使用"
                        value={realTimeStats.local?.mainProcessUsage || 0}
                        suffix="次"
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
              
              <Col span={12}>
                <Card size="small" title="服务状态">
                  {serviceStatus ? (
                    Object.entries(serviceStatus).map(([serviceName, serviceInfo]) => (
                      <div key={serviceName} style={{ marginBottom: 8 }}>
                        <Space>
                          <Text strong>{serviceName}</Text>
                          <Badge 
                            status={serviceInfo.initialized ? 'success' : 'error'} 
                            text={serviceInfo.initialized ? '运行中' : '未初始化'}
                          />
                        </Space>
                      </div>
                    ))
                  ) : (
                    <Text type="secondary">暂无服务状态数据</Text>
                  )}
                </Card>
              </Col>
            </Row>
          ) : (
            <Alert
              message="暂无性能数据"
              description="开启实时监控或点击刷新按钮获取性能数据"
              type="info"
            />
          )}
        </TabPane>

        {/* 系统信息 */}
        <TabPane 
          tab={
            <Space>
              <SettingOutlined />
              <span>系统信息</span>
            </Space>
          } 
          key="system"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Card size="small" title="优化功能状态">
                <Space direction="vertical" style={{ width: '100%' }}>
                  {Object.entries(getOptimizationStatus().features || {}).map(([feature, available]) => (
                    <div key={feature}>
                      <Space>
                        <Badge status={available ? 'success' : 'default'} />
                        <Text>{feature}</Text>
                        <Tag color={available ? 'green' : 'default'}>
                          {available ? '可用' : '不可用'}
                        </Tag>
                      </Space>
                    </div>
                  ))}
                </Space>
              </Card>
            </Col>
            
            <Col span={12}>
              <Card size="small" title="环境信息">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>Electron API: </Text>
                    <Tag color={window.electronAPI ? 'green' : 'red'}>
                      {window.electronAPI ? '可用' : '不可用'}
                    </Tag>
                  </div>
                  <div>
                    <Text strong>平台: </Text>
                    <Text code>{window.electronAPI?.platform || 'unknown'}</Text>
                  </div>
                  <div>
                    <Text strong>版本信息: </Text>
                    {window.electronAPI?.versions && (
                      <div style={{ marginTop: 4 }}>
                        <Text style={{ fontSize: '12px' }}>
                          Node: {window.electronAPI.versions.node}<br />
                          Chrome: {window.electronAPI.versions.chrome}<br />
                          Electron: {window.electronAPI.versions.electron}
                        </Text>
                      </div>
                    )}
                  </div>
                </Space>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default DeveloperDebugPanel;
