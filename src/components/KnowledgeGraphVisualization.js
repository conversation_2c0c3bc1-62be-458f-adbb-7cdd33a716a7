import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  Typography,
  Alert,
  List,
  Tag,
  Tooltip,
  Select,
  Slider,
  Switch,
  message,
  Tabs
} from 'antd';
import {
  ShareAltOutlined,
  NodeIndexOutlined,
  BranchesOutlined,
  SearchOutlined,
  SettingOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Text, Title, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 🕸️ 知识图谱可视化组件
 * 显示知识库间的关联关系和文档网络
 */
const KnowledgeGraphVisualization = ({ visible, onClose, knowledgeBases = [] }) => {
  const [graphData, setGraphData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [selectedKB, setSelectedKB] = useState(null);
  const [relatedDocuments, setRelatedDocuments] = useState([]);
  const [graphStats, setGraphStats] = useState(null);
  const [viewMode, setViewMode] = useState('overview'); // 'overview', 'detailed'
  const [filterOptions, setFilterOptions] = useState({
    minSimilarity: 0.5,
    maxDepth: 2,
    showLabels: true,
    relationTypes: ['all']
  });

  const graphContainerRef = useRef(null);

  // 加载图谱数据
  const loadGraphData = async () => {
    setLoading(true);
    try {
      // 模拟图谱数据（实际应该从API获取）
      const mockGraphData = {
        nodes: knowledgeBases.map((kb, index) => ({
          id: kb.id,
          name: kb.name,
          type: 'knowledgeBase',
          size: kb.documents?.length || 0,
          x: Math.cos(index * 2 * Math.PI / knowledgeBases.length) * 200,
          y: Math.sin(index * 2 * Math.PI / knowledgeBases.length) * 200
        })),
        edges: []
      };

      // 生成一些模拟的关联关系
      for (let i = 0; i < knowledgeBases.length; i++) {
        for (let j = i + 1; j < knowledgeBases.length; j++) {
          if (Math.random() > 0.6) { // 40%的概率有关联
            mockGraphData.edges.push({
              source: knowledgeBases[i].id,
              target: knowledgeBases[j].id,
              type: 'similar_to',
              weight: Math.random() * 0.5 + 0.5
            });
          }
        }
      }

      setGraphData(mockGraphData);

      // 生成统计数据
      setGraphStats({
        totalNodes: mockGraphData.nodes.length,
        totalEdges: mockGraphData.edges.length,
        avgConnections: mockGraphData.edges.length / mockGraphData.nodes.length,
        clusters: Math.ceil(mockGraphData.nodes.length / 3)
      });

      console.log('📊 [KnowledgeGraph] 图谱数据已加载');
    } catch (error) {
      console.error('❌ [KnowledgeGraph] 加载图谱数据失败:', error);
      message.error('加载图谱数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 查找相关文档
  const findRelatedDocuments = async (kbId) => {
    try {
      // 模拟相关文档数据
      const mockRelated = [
        {
          documentId: 'doc1',
          title: '相关文档1',
          similarity: 0.85,
          relationType: 'similar_to',
          snippet: '这是一个相关文档的摘要内容...'
        },
        {
          documentId: 'doc2',
          title: '相关文档2',
          similarity: 0.72,
          relationType: 'references',
          snippet: '这是另一个相关文档的摘要...'
        }
      ];

      setRelatedDocuments(mockRelated);
    } catch (error) {
      console.error('❌ [KnowledgeGraph] 查找相关文档失败:', error);
    }
  };

  // 渲染简单的图谱可视化
  const renderSimpleGraph = () => {
    if (!graphData) return null;

    const svgWidth = 400;
    const svgHeight = 300;
    const centerX = svgWidth / 2;
    const centerY = svgHeight / 2;

    return (
      <svg width={svgWidth} height={svgHeight} style={{ border: '1px solid #d9d9d9', borderRadius: '4px' }}>
        {/* 渲染边 */}
        {graphData.edges.map((edge, index) => {
          const sourceNode = graphData.nodes.find(n => n.id === edge.source);
          const targetNode = graphData.nodes.find(n => n.id === edge.target);
          if (!sourceNode || !targetNode) return null;

          return (
            <line
              key={index}
              x1={centerX + sourceNode.x * 0.8}
              y1={centerY + sourceNode.y * 0.6}
              x2={centerX + targetNode.x * 0.8}
              y2={centerY + targetNode.y * 0.6}
              stroke="#1890ff"
              strokeWidth={edge.weight * 3}
              opacity={0.6}
            />
          );
        })}

        {/* 渲染节点 */}
        {graphData.nodes.map((node, index) => (
          <g key={node.id}>
            <circle
              cx={centerX + node.x * 0.8}
              cy={centerY + node.y * 0.6}
              r={Math.max(8, Math.min(20, node.size * 2))}
              fill="#52c41a"
              stroke="#fff"
              strokeWidth="2"
              style={{ cursor: 'pointer' }}
              onClick={() => {
                setSelectedKB(node.id);
                findRelatedDocuments(node.id);
              }}
            />
            {filterOptions.showLabels && (
              <text
                x={centerX + node.x * 0.8}
                y={centerY + node.y * 0.6 + 25}
                textAnchor="middle"
                fontSize="10"
                fill="#666"
              >
                {node.name.length > 8 ? node.name.substring(0, 8) + '...' : node.name}
              </text>
            )}
          </g>
        ))}
      </svg>
    );
  };

  // 查看文档
  const handleViewDocument = (doc) => {
    Modal.info({
      title: `查看文档 - ${doc.title}`,
      content: (
        <div>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>文档ID：</Text>
              <Text code>{doc.documentId}</Text>
            </div>
            <div>
              <Text strong>关系类型：</Text>
              <Tag color={getRelationTypeColor(doc.relationType)}>
                {doc.relationType}
              </Tag>
            </div>
            <div>
              <Text strong>相似度：</Text>
              <Text>{(doc.similarity * 100).toFixed(1)}%</Text>
            </div>
            <div>
              <Text strong>内容摘要：</Text>
              <Paragraph style={{ marginTop: 8 }}>
                {doc.snippet}
              </Paragraph>
            </div>
          </Space>
        </div>
      ),
      width: 600
    });
  };

  // 编辑文档
  const handleEditDocument = (doc) => {
    Modal.confirm({
      title: `编辑文档 - ${doc.title}`,
      content: (
        <div>
          <Alert
            message="功能开发中"
            description="文档编辑功能正在开发中。您可以在文档管理页面中编辑文档内容。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Text>是否跳转到文档管理页面？</Text>
        </div>
      ),
      okText: '跳转',
      cancelText: '取消',
      onOk() {
        message.info('跳转到文档管理页面功能待实现');
        // TODO: 实现跳转到文档管理页面的逻辑
      }
    });
  };

  // 获取关系类型颜色
  const getRelationTypeColor = (type) => {
    const colors = {
      'similar_to': 'blue',
      'references': 'green',
      'extends': 'orange',
      'depends_on': 'purple'
    };
    return colors[type] || 'default';
  };

  useEffect(() => {
    if (visible && knowledgeBases.length > 0) {
      loadGraphData();
    }
  }, [visible, knowledgeBases]);

  return (
    <Modal
      title={
        <Space>
          <ShareAltOutlined style={{ color: '#722ed1' }} />
          <span>知识图谱可视化</span>
          {graphStats && (
            <Tag color="purple">{graphStats.totalNodes} 个节点</Tag>
          )}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={
        <Space>
          <Button onClick={onClose}>关闭</Button>
          <Button 
            icon={<ReloadOutlined />}
            onClick={loadGraphData}
            loading={loading}
          >
            刷新图谱
          </Button>
          <Button 
            icon={<FullscreenOutlined />}
            onClick={() => message.info('全屏功能开发中')}
          >
            全屏查看
          </Button>
        </Space>
      }
    >
      <Tabs defaultActiveKey="graph">
        {/* 图谱可视化 */}
        <TabPane 
          tab={
            <Space>
              <NodeIndexOutlined />
              <span>关系图谱</span>
            </Space>
          } 
          key="graph"
        >
          <Row gutter={16}>
            <Col span={16}>
              <Card title="知识库关系图" size="small" loading={loading}>
                {graphData ? (
                  <div style={{ textAlign: 'center' }}>
                    {renderSimpleGraph()}
                    <div style={{ marginTop: 16 }}>
                      <Alert
                        message="图谱说明"
                        description="圆圈代表知识库，大小表示文档数量，线条表示关联关系，点击节点查看详情"
                        type="info"
                        showIcon
                        style={{ textAlign: 'left' }}
                      />
                    </div>
                  </div>
                ) : (
                  <div style={{ textAlign: 'center', padding: '60px 0' }}>
                    <ShareAltOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                    <div style={{ marginTop: 16 }}>
                      <Text type="secondary">暂无图谱数据</Text>
                    </div>
                  </div>
                )}
              </Card>
            </Col>

            <Col span={8}>
              {/* 图谱统计 */}
              <Card title="图谱统计" size="small" style={{ marginBottom: 16 }}>
                {graphStats ? (
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="节点数"
                        value={graphStats.totalNodes}
                        prefix={<NodeIndexOutlined />}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="关系数"
                        value={graphStats.totalEdges}
                        prefix={<BranchesOutlined />}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="平均连接"
                        value={graphStats.avgConnections}
                        precision={1}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="集群数"
                        value={graphStats.clusters}
                      />
                    </Col>
                  </Row>
                ) : (
                  <Text type="secondary">暂无统计数据</Text>
                )}
              </Card>

              {/* 显示设置 */}
              <Card title="显示设置" size="small">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text>显示标签</Text>
                    <Switch
                      checked={filterOptions.showLabels}
                      onChange={(checked) => 
                        setFilterOptions(prev => ({ ...prev, showLabels: checked }))
                      }
                      style={{ float: 'right' }}
                    />
                  </div>
                  
                  <div>
                    <Text>最小相似度</Text>
                    <Slider
                      min={0}
                      max={1}
                      step={0.1}
                      value={filterOptions.minSimilarity}
                      onChange={(value) => 
                        setFilterOptions(prev => ({ ...prev, minSimilarity: value }))
                      }
                    />
                  </div>

                  <div>
                    <Text>关系类型</Text>
                    <Select
                      mode="multiple"
                      style={{ width: '100%', marginTop: 8 }}
                      placeholder="选择关系类型"
                      value={filterOptions.relationTypes}
                      onChange={(value) => 
                        setFilterOptions(prev => ({ ...prev, relationTypes: value }))
                      }
                    >
                      <Option value="all">全部</Option>
                      <Option value="similar_to">相似</Option>
                      <Option value="references">引用</Option>
                      <Option value="extends">扩展</Option>
                      <Option value="depends_on">依赖</Option>
                    </Select>
                  </div>
                </Space>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 相关文档 */}
        <TabPane 
          tab={
            <Space>
              <SearchOutlined />
              <span>相关文档</span>
              {relatedDocuments.length > 0 && (
                <Tag color="blue" size="small">{relatedDocuments.length}</Tag>
              )}
            </Space>
          } 
          key="related"
        >
          {selectedKB ? (
            <Card title={`知识库关联文档`} size="small">
              <List
                dataSource={relatedDocuments}
                renderItem={(doc) => (
                  <List.Item
                    actions={[
                      <Button
                        size="small"
                        type="link"
                        onClick={() => handleViewDocument(doc)}
                      >
                        查看
                      </Button>,
                      <Button
                        size="small"
                        type="link"
                        onClick={() => handleEditDocument(doc)}
                      >
                        编辑
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          <Text>{doc.title}</Text>
                          <Tag color={getRelationTypeColor(doc.relationType)} size="small">
                            {doc.relationType}
                          </Tag>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            相似度: {(doc.similarity * 100).toFixed(1)}%
                          </Text>
                        </Space>
                      }
                      description={
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {doc.snippet}
                        </Text>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          ) : (
            <Alert
              message="请选择知识库节点"
              description="在图谱中点击知识库节点来查看相关文档"
              type="info"
              showIcon
            />
          )}
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default KnowledgeGraphVisualization;
