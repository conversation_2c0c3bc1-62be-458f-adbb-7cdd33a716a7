import React, { useState, useEffect, useRef, useMemo, useCallback, useImperativeHandle } from 'react';
import {
  Layout,
  Card,
  Button,
  Input,
  Typography,
  Space,
  Tag,
  Select,
  Switch,
  Slider,
  message,
  Modal,
  Checkbox,
  Upload,
  Image,
  Tooltip,
  Progress
} from 'antd';
// 🚀 导入优化组件
import MessageItem from './MessageItem';
import SmartClipboardManager from './SmartClipboardManager'; // 🚀 智能剪贴板管理器
import QuickActionToolbar from './QuickActionToolbar'; // 🚀 一键操作工具栏

import messageCache from '../utils/messageCache';
import { setMessageInstance } from '../utils/copyUtils'; // 🎯 导入复制工具
import WebSearchManager from '../utils/webSearchManager'; // 🌐 导入网络搜索管理器
// 不再需要debounce，使用立即响应的滚动处理
import '../styles/messageItem.css';
import {
  SendOutlined,
  PlusOutlined,
  DeleteOutlined,
  SettingOutlined,
  StopOutlined,
  RobotOutlined,
  ThunderboltOutlined,
  EyeOutlined,
  GlobalOutlined,
  PaperClipOutlined,
  FileImageOutlined,
  FileTextOutlined,
  CloseOutlined,
  UserOutlined,
  CopyOutlined,
  HistoryOutlined,
  BarChartOutlined,
  DashboardOutlined,
  ArrowDownOutlined
} from '@ant-design/icons';

import { APIManager } from '../utils/apiManager';
import { LoopProtectionManager } from '../utils/loopProtection';
import { CoordinatorManager } from '../utils/coordinatorManager';
import { KnowledgeBaseIntegration } from '../utils/knowledgeBaseIntegration'; // 🚀 新增：知识库集成
import MarkdownRenderer from './MarkdownRenderer';
import EnhancedMessageRenderer from './EnhancedMessageRenderer'; // 🚀 新增：增强的消息渲染器

const { Sider, Content } = Layout;
const { Option } = Select;
const { Title, Text } = Typography;

const ChatRoom = React.forwardRef(({ dataManager }, ref) => {
  // 基础状态
  const [allModels, setAllModels] = useState([]);
  const [allPartners, setAllPartners] = useState([]);
  const [sessions, setSessions] = useState([]);
  const [currentSession, setCurrentSession] = useState(null);
  const [allKnowledgeBases, setAllKnowledgeBases] = useState([]); // 🚀 新增：所有知识库
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [invitedModels, setInvitedModels] = useState([]);
  const [selectedPartner, setSelectedPartner] = useState(null);
  const [coordinatorModel, setCoordinatorModel] = useState(null);

  // 🔥 新增：文件上传相关状态
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isConversationActive, setIsConversationActive] = useState(false);
  const [activeRequests, setActiveRequests] = useState(new Set());

  // 设置状态
  const [maxRounds, setMaxRounds] = useState(20); // 🔥 增加默认轮次，支持更深入的对话
  const [showThinking, setShowThinking] = useState(true);
  const [enableCoordinator, setEnableCoordinator] = useState(true);
  const [allowCoordinatorToSpeak, setAllowCoordinatorToSpeak] = useState(true);

  // 🔥 新增：智能结束检测控制选项
  const [enableIntelligentEnd, setEnableIntelligentEnd] = useState(true); // 启用智能结束检测
  const [discussionQualityThreshold, setDiscussionQualityThreshold] = useState(0.75); // 讨论质量阈值

  // 🌐 新增：网络搜索功能状态（默认关闭）
  const [enableWebSearch, setEnableWebSearch] = useState(() => {
    const saved = localStorage.getItem('jdcaichat_web_search_enabled');
    return saved ? JSON.parse(saved) : false; // 默认关闭
  });

  // 🌐 网络搜索管理器
  const webSearchManagerRef = useRef(null);

  // 🔧 辅助函数：获取平台特定的快捷键文本
  const getWebSearchShortcut = () => {
    const isMac = navigator.userAgent.toUpperCase().indexOf('MAC') >= 0;
    return isMac ? 'Cmd+E' : 'Ctrl+E';
  };



  // 🔥 新增：统一的标题检查函数
  const shouldGenerateTitle = (session) => {
    if (!session || !session.title) {
      return true; // 没有标题，需要生成
    }

    // 检查是否是临时标题
    const isTemporaryTitle = session.title.startsWith('会话') ||
                            session.title.startsWith('新会话');

    if (!isTemporaryTitle) {
      console.log('📝 [ChatRoom] 会话已有自定义标题，无需生成:', session.title);
      return false;
    }

    console.log('📝 [ChatRoom] 检测到临时标题，需要生成智能标题:', session.title);
    return true;
  };





  // 🚀 性能优化：预定义Hooks
  const handleScrollCallback = useCallback((scrollInfo) => {
    // 处理滚动事件
    if (scrollInfo.scrollDirection === 'forward') {
      // 向下滚动时可以预加载更多消息
    }
  }, []);

  // 🔥 新增：智能调度状态指示器组件
  const renderSchedulingIndicator = () => {
    if (!schedulingProgress.isActive) return null;

    const getStageIcon = (stage) => {
      switch (stage) {
        case 'analyzing': return '🧠';
        case 'selecting': return '🎯';
        case 'generating': return '✨';
        case 'complete': return '✅';
        default: return '⚡';
      }
    };

    const getStageText = (stage) => {
      switch (stage) {
        case 'analyzing': return '分析讨论内容';
        case 'selecting': return '选择最佳模型';
        case 'generating': return '生成互动提示';
        case 'complete': return '调度完成';
        case 'ending': return '分析讨论完成度';
        case 'no_scheduling': return '无需继续调度';
        default: return '智能调度中';
      }
    };

    const elapsedTime = schedulingProgress.startTime ?
      Math.floor((Date.now() - schedulingProgress.startTime) / 1000) : 0;

    return (
      <Card
        size="small"
        style={{
          margin: '8px 0',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          border: 'none'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div style={{ fontSize: '20px' }}>
            {getStageIcon(schedulingProgress.stage)}
          </div>

          <div style={{ flex: 1 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span style={{ fontWeight: 'bold' }}>
                {getStageText(schedulingProgress.stage)}
              </span>
              <span style={{ fontSize: '12px', opacity: 0.8 }}>
                {elapsedTime}s
              </span>
            </div>

            <Progress
              percent={schedulingProgress.progress}
              size="small"
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
              style={{ marginTop: '4px' }}
              showInfo={false}
            />

            {schedulingProgress.message && (
              <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.9 }}>
                {schedulingProgress.message}
              </div>
            )}

            {schedulingProgress.selectedModel && (
              <div style={{ fontSize: '12px', marginTop: '4px' }}>
                🎯 选中模型: <strong>{schedulingProgress.selectedModel}</strong>
                {schedulingProgress.confidence > 0 && (
                  <span style={{ marginLeft: '8px', opacity: 0.8 }}>
                    置信度: {(schedulingProgress.confidence * 100).toFixed(0)}%
                  </span>
                )}
              </div>
            )}
          </div>

          <Button
            size="small"
            type="text"
            style={{ color: 'white' }}
            onClick={() => {
              if (discussionAbortController) {
                discussionAbortController.abort();
                setSchedulingProgress(prev => ({ ...prev, isActive: false }));
              }
            }}
          >
            中断
          </Button>
        </div>
      </Card>
    );
  };

  // 调度模式状态
  const [schedulingMode, setSchedulingMode] = useState('intelligent'); // 'intelligent', 'roundRobin', 'random'
  const [selectedModelForInput, setSelectedModelForInput] = useState(null); // 输入框指定的模型

  // 智能协作状态
  const [isAIDiscussionActive, setIsAIDiscussionActive] = useState(false); // AI讨论是否进行中
  const [discussionRounds, setDiscussionRounds] = useState(0); // 当前讨论轮数
  const [pendingUserMessage, setPendingUserMessage] = useState(null); // 用户打断时的待处理消息
  const [discussionAbortController, setDiscussionAbortController] = useState(null); // 讨论中断控制器
  const [currentSchedulingStatus, setCurrentSchedulingStatus] = useState(''); // 当前调度状态

  // 🔥 新增：详细的调度状态管理
  const [schedulingProgress, setSchedulingProgress] = useState({
    isActive: false,
    stage: '', // 'analyzing', 'selecting', 'generating', 'complete'
    progress: 0,
    message: '',
    selectedModel: null,
    confidence: 0,
    startTime: null,
    estimatedTime: 0
  });

  // 弹窗状态
  const [createSessionModalVisible, setCreateSessionModalVisible] = useState(false);
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);
  const [currentSessionKnowledgeBases, setCurrentSessionKnowledgeBases] = useState([]); // 🚀 新增：当前会话的知识库配置
  const [newSessionName, setNewSessionName] = useState('');
  const [selectedModelsForNewSession, setSelectedModelsForNewSession] = useState([]);
  const [newSessionCoordinator, setNewSessionCoordinator] = useState(null);
  const [newSessionPartner, setNewSessionPartner] = useState(null);
  const [newSessionMaxRounds, setNewSessionMaxRounds] = useState(20); // 🔥 增加默认轮次，支持更深入的对话
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState([]); // 🚀 新增：选中的知识库

  // 🎯 输入法状态管理 - 修复macOS中文输入法回车键冲突
  const [isComposing, setIsComposing] = useState(false);

  // 🚀 性能优化状态
  const [performanceMode, setPerformanceMode] = useState('auto'); // 'auto', 'high', 'low'

  // 🚀 智能剪贴板管理器状态 - 默认关闭
  const [showClipboardManager, setShowClipboardManager] = useState(false);

  // 🎯 智能滚动状态管理
  // 智能滚动相关状态 - 使用ref避免被消息更新重置
  const isUserScrolledUpRef = useRef(false); // 用户是否手动滚动离开底部
  const [showScrollToBottom, setShowScrollToBottom] = useState(false); // 是否显示回到底部按钮

  // 🎯 平台检测
  const isMacOS = useMemo(() => {
    return navigator.userAgent.toUpperCase().indexOf('MAC') >= 0;
  }, []);

  // 🎯 输入法事件处理 - 修复macOS中文输入法回车键冲突
  const handleCompositionStart = useCallback(() => {
    console.log('🎯 [输入法] 开始输入法组合输入');
    setIsComposing(true);
  }, []);

  const handleCompositionEnd = useCallback(() => {
    console.log('🎯 [输入法] 结束输入法组合输入');
    setIsComposing(false);
  }, []);

  // 🎯 暴露方法给父组件使用（快捷键功能）
  useImperativeHandle(ref, () => ({
    toggleClipboard: () => {
      setShowClipboardManager(prev => !prev);
    },
    createNewSession: () => {
      // 🔥 修复：使用描述性的临时标题，等待AI回复后生成智能标题
      const now = new Date();
      const timeStr = now.toLocaleString('zh-CN', {
        month: 'numeric',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
      setNewSessionName(`新会话 - ${timeStr}`);
      setSelectedModelsForNewSession([]);
      setNewSessionCoordinator(coordinatorModel?.id || null);
      setNewSessionPartner(selectedPartner?.id || null);
      setNewSessionMaxRounds(maxRounds);
      setSelectedKnowledgeBases([]); // 🚀 新增：重置知识库选择
      setCreateSessionModalVisible(true);
    }
  }), [coordinatorModel, selectedPartner, maxRounds]);



  // 🚀 剪贴板内容插入回调
  const handleClipboardInsert = useCallback((content) => {
    if (content && content.trim()) {
      // 如果当前输入框有内容，在末尾添加
      const currentValue = inputValue.trim();
      const newValue = currentValue ? `${currentValue}\n\n${content}` : content;
      setInputValue(newValue);

      // 🎯 修改：不再自动关闭剪贴板管理器，保持一直打开
      // setShowClipboardManager(false);

      // 聚焦到输入框
      setTimeout(() => {
        const textarea = document.querySelector('textarea[placeholder*="输入消息"]');
        if (textarea) {
          textarea.focus();
          // 将光标移到末尾
          textarea.setSelectionRange(newValue.length, newValue.length);
        }
      }, 100);
    }
  }, [inputValue]);

  // 🚀 一键操作工具栏功能函数

  // 清空当前对话
  const handleClearConversation = useCallback(async () => {
    if (!currentSession) return;

    try {
      // 清空消息
      setMessages([]);

      // 更新数据管理器
      dataManager.clearSessionMessages(currentSession.id);

      console.log('🧹 [快捷操作] 对话已清空');
    } catch (error) {
      console.error('❌ [快捷操作] 清空对话失败:', error);
      throw error;
    }
  }, [currentSession, dataManager]);



  // 删除指定消息
  const handleDeleteMessage = useCallback(async (messageToDelete) => {
    if (!currentSession || !messageToDelete) return;

    try {
      // 从消息列表中删除该消息
      const newMessages = messages.filter(msg =>
        msg.id !== messageToDelete.id &&
        !(msg.timestamp === messageToDelete.timestamp && msg.sender === messageToDelete.sender)
      );

      setMessages(newMessages);

      // 更新数据管理器
      dataManager.setSessionMessages(currentSession.id, newMessages);

      console.log('🗑️ [快捷操作] 消息已删除:', messageToDelete.sender);

    } catch (error) {
      console.error('❌ [快捷操作] 删除消息失败:', error);
      throw error;
    }
  }, [currentSession, messages, dataManager]);

  // 导出对话
  const handleExportConversation = useCallback(async () => {
    if (!currentSession || messages.length === 0) return;

    try {
      // 生成导出内容
      const exportContent = messages.map(msg => {
        const timestamp = new Date(msg.timestamp).toLocaleString();
        const sender = msg.sender === 'user' ? '用户' : msg.sender;
        return `[${timestamp}] ${sender}:\n${msg.content}\n`;
      }).join('\n');

      // 创建下载链接
      const blob = new Blob([exportContent], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${currentSession.name || '对话记录'}_${new Date().toISOString().split('T')[0]}.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('📄 [快捷操作] 对话已导出');

    } catch (error) {
      console.error('❌ [快捷操作] 导出对话失败:', error);
      throw error;
    }
  }, [currentSession, messages]);

  // 🚀 用户消息编辑功能
  const handleEditMessage = useCallback(async (originalMessage, newContent) => {
    if (!currentSession || !originalMessage) return;

    try {
      console.log('✏️ [ChatRoom] 开始编辑用户消息:', originalMessage.id);

      // 找到该消息在数组中的位置
      const messageIndex = messages.findIndex(msg =>
        msg.id === originalMessage.id ||
        (msg.timestamp === originalMessage.timestamp && msg.sender === originalMessage.sender)
      );

      if (messageIndex === -1) {
        throw new Error('未找到要编辑的消息');
      }

      // 更新消息内容
      const updatedMessage = {
        ...originalMessage,
        content: newContent,
        editedAt: new Date().toISOString(),
        originalContent: originalMessage.originalContent || originalMessage.content
      };

      // 删除该消息及其之后的所有AI回复
      const newMessages = [...messages];
      newMessages[messageIndex] = updatedMessage;

      // 删除该消息之后的所有消息（因为编辑后需要重新生成AI回复）
      const messagesToKeep = newMessages.slice(0, messageIndex + 1);

      setMessages(messagesToKeep);
      dataManager.setSessionMessages(currentSession.id, messagesToKeep);

      console.log('✅ [ChatRoom] 消息编辑完成，准备重新生成AI回复');

      // 自动触发AI重新生成回复
      setIsConversationActive(true);

      // 使用现有的AI协作逻辑重新生成回复
      try {
        if (schedulingMode === 'intelligent' && enableCoordinator && coordinatorModel) {
          await startIntelligentCollaboration(messagesToKeep);
        } else if (schedulingMode === 'roundRobin') {
          await processRoundRobinScheduling(messagesToKeep);
        } else {
          await processRandomScheduling(messagesToKeep);
        }
      } catch (error) {
        console.error('❌ [ChatRoom] 重新生成AI回复失败:', error);
        setIsConversationActive(false);
        throw new Error('重新生成AI回复失败: ' + error.message);
      }

    } catch (error) {
      console.error('❌ [ChatRoom] 编辑消息失败:', error);
      throw error;
    }
  }, [currentSession, messages, dataManager, schedulingMode, enableCoordinator, coordinatorModel]);





  // Refs
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null); // 🚀 消息容器引用，用于滚动导航
  const apiManagerRef = useRef(new APIManager());
  const loopProtectionRef = useRef(new LoopProtectionManager(maxRounds));
  const coordinatorManagerRef = useRef(new CoordinatorManager());
  const knowledgeBaseIntegrationRef = useRef(new KnowledgeBaseIntegration(dataManager)); // 🚀 新增：知识库集成工具

  // 初始化数据
  useEffect(() => {
    loadData();



    // 🚀 新增：为知识库管理器设置API管理器（用于智能分块）
    if (dataManager && dataManager.knowledgeBaseManager && apiManagerRef.current) {
      dataManager.knowledgeBaseManager.setAPIManager(apiManagerRef.current);
    }

    // 🌐 初始化网络搜索管理器
    if (!webSearchManagerRef.current) {
      webSearchManagerRef.current = new WebSearchManager();
      webSearchManagerRef.current.setEnabled(enableWebSearch);
      console.log('✅ [ChatRoom] 网络搜索管理器初始化完成');
    }
  }, []);

  // 🌐 网络搜索状态持久化
  useEffect(() => {
    localStorage.setItem('jdcaichat_web_search_enabled', JSON.stringify(enableWebSearch));
    if (webSearchManagerRef.current) {
      webSearchManagerRef.current.setEnabled(enableWebSearch);
    }
  }, [enableWebSearch]);

  // 🌐 网络搜索快捷键支持 (Ctrl+E / Cmd+E)
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl+E (Windows/Linux) 或 Command+E (macOS)
      if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
        event.preventDefault();
        const newState = !enableWebSearch;
        setEnableWebSearch(newState);
        message.info(`网络搜索已${newState ? '启用' : '关闭'} (${getWebSearchShortcut()})`);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [enableWebSearch]);

  // 清理资源
  useEffect(() => {
    const apiManager = apiManagerRef.current;
    const coordinatorManager = coordinatorManagerRef.current;

    // 🎯 设置message实例用于复制功能
    setMessageInstance(message);

    // 🎯 修复：监听主进程的剪贴板启动通知
    if (window.electronAPI?.clipboard?.onMonitoringStarted) {
      window.electronAPI.clipboard.onMonitoringStarted(() => {
        console.log('📋 [ChatRoom] 收到剪贴板启动通知，剪贴板监听已在后台启动');
        // 不需要打开抽屉，只是确保后台监听已启动
      });
    }

    return () => {
      apiManager.cleanup();
      coordinatorManager.cleanup();
      
      // 清理剪贴板监听器
      if (window.electronAPI?.clipboard?.removeMonitoringListener) {
        window.electronAPI.clipboard.removeMonitoringListener();
      }
    };
  }, []);

  // 🚀 性能优化：消息缓存管理
  useEffect(() => {
    // 缓存当前会话的消息
    if (messages && messages.length > 0 && currentSession) {
      try {
        // 简化的消息缓存处理
        messages.forEach(msg => {
          if (msg && msg.id) {
            messageCache.cacheMessage(msg);
          }
        });

        console.log('🚀 [性能优化] 已缓存消息:', messageCache.getCacheStats());
      } catch (error) {
        console.error('🚨 [性能优化] 缓存消息时出错:', error);
      }
    }

    // 定期清理过期缓存
    const cleanupInterval = setInterval(() => {
      try {
        const cleanedCount = messageCache.cleanupExpiredCache();
        if (cleanedCount > 0) {
          console.log(`🧹 [性能优化] 已清理 ${cleanedCount} 条过期消息缓存`);
        }
      } catch (error) {
        console.error('🚨 [性能优化] 清理缓存时出错:', error);
      }
    }, 5 * 60 * 1000); // 每5分钟清理一次

    return () => {
      clearInterval(cleanupInterval);
    };
  }, [messages, currentSession]);

  // 🎯 智能自动滚动到底部 - 完整的检测逻辑
  useEffect(() => {
    if (messages.length > 0) {
      // 等待DOM更新完成
      setTimeout(() => {
        const container = document.querySelector('[data-scroll-container="true"]');
        if (container) {
          const threshold = 100;
          const scrollTop = container.scrollTop;
          const clientHeight = container.clientHeight;
          const scrollHeight = container.scrollHeight;
          const currentlyAtBottom = scrollTop + clientHeight >= scrollHeight - threshold;
          
          console.log('🎯 [滚动检测] 详细信息:', {
            scrollTop,
            clientHeight,
            scrollHeight,
            threshold,
            currentlyAtBottom,
            userScrolledUp: isUserScrolledUpRef.current,
            messagesLength: messages.length
          });
          
          // 只有用户在底部且没有手动滚动时才自动滚动
          if (currentlyAtBottom && !isUserScrolledUpRef.current) {
            console.log('🎯 [滚动] ✅ 用户在底部且允许自动滚动，执行滚动');
            messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
          } else {
            console.log('🎯 [滚动] ❌ 跳过自动滚动，原因:', {
              在底部: currentlyAtBottom,
              用户已手动滚动: isUserScrolledUpRef.current
            });
          }
        } else {
          console.log('🎯 [滚动] ⚠️ 未找到滚动容器');
        }
      }, 100);
    }
  }, [messages]);



  // 加载数据
  const loadData = () => {
    try {
      const models = dataManager.getAllModels();
      const partners = dataManager.getPartners ? dataManager.getPartners() : [];
      const sessionList = dataManager.getAllSessions();
      const knowledgeBases = dataManager.getKnowledgeBases ? dataManager.getKnowledgeBases() : []; // 🚀 新增：加载知识库

      setAllModels(models);
      setAllPartners(partners);
      setSessions(sessionList);
      setAllKnowledgeBases(knowledgeBases); // 🚀 新增：设置知识库数据

      console.log('📊 [ChatRoom] 数据加载完成:', {
        models: models.length,
        partners: partners.length,
        sessions: sessionList.length
      });
    } catch (error) {
      console.error('❌ [ChatRoom] 数据加载失败:', error);
      message.error('数据加载失败');
      // 设置默认值避免应用崩溃
      setAllModels([]);
      setAllPartners([]);
      setSessions([]);
    }
  };

  // 不再需要isAtBottom函数，直接在handleScrollImmediate中计算

  // 🎯 滚动事件处理（立即响应用户滚动）
  const handleScrollImmediate = useCallback((event) => {
    const container = event.target;
    const threshold = 100;
    const scrollTop = container.scrollTop;
    const clientHeight = container.clientHeight;
    const scrollHeight = container.scrollHeight;
    const atBottom = scrollTop + clientHeight >= scrollHeight - threshold;
    
    console.log('🎯 [用户滚动事件] 详细信息:', {
      scrollTop,
      clientHeight,
      scrollHeight,
      threshold,
      atBottom,
      previousUserScrolledUp: isUserScrolledUpRef.current
    });
    
    // 使用ref立即更新状态，避免被消息更新影响
    if (atBottom !== !isUserScrolledUpRef.current) {
      if (atBottom) {
        console.log('🎯 [滚动] 用户回到底部，恢复自动滚动');
        isUserScrolledUpRef.current = false;
        setShowScrollToBottom(false);
      } else {
        console.log('🎯 [滚动] 用户离开底部，立即暂停自动滚动');
        isUserScrolledUpRef.current = true;
        setShowScrollToBottom(true);
      }
    } else {
      console.log('🎯 [滚动] 滚动状态无变化，保持当前状态');
    }
  }, []);

  // 🎯 立即响应的滚动处理，不使用防抖
  const handleScroll = handleScrollImmediate;

  // 不再需要防抖清理，因为直接使用handleScrollImmediate

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // 🎯 强制滚动到底部（用户点击按钮）
  const forceScrollToBottom = useCallback(() => {
    isUserScrolledUpRef.current = false;
    setShowScrollToBottom(false);
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    console.log('🎯 [滚动] 用户手动回到底部');
  }, []);

  // 显示新建会话弹窗
  const showCreateSessionModal = () => {
    // 🔥 修复：使用描述性的临时标题，等待AI回复后生成智能标题
    const now = new Date();
    const timeStr = now.toLocaleString('zh-CN', {
      month: 'numeric',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
    setNewSessionName(`新会话 - ${timeStr}`);
    setSelectedModelsForNewSession([]);
    setNewSessionCoordinator(coordinatorModel?.id || null);
    setNewSessionPartner(selectedPartner?.id || null);
    setNewSessionMaxRounds(maxRounds);
    setSelectedKnowledgeBases([]); // 🚀 新增：重置知识库选择
    setCreateSessionModalVisible(true);
  };

  // 显示设置弹窗
  const showSettingsModal = () => {
    // 🚀 新增：初始化当前会话的知识库配置
    if (currentSession && currentSession.knowledgeBases) {
      setCurrentSessionKnowledgeBases(currentSession.knowledgeBases);
    } else {
      setCurrentSessionKnowledgeBases([]);
    }
    setSettingsModalVisible(true);
  };

  // 🚀 新增：保存设置
  const saveSettings = () => {
    if (!currentSession) {
      message.warning('当前没有活跃的会话');
      return;
    }

    try {
      // 更新会话的知识库配置
      const updatedSession = {
        ...currentSession,
        knowledgeBases: currentSessionKnowledgeBases,
        updatedAt: new Date().toISOString()
      };

      // 保存到数据管理器
      dataManager.updateSession(currentSession.id, updatedSession);
      setCurrentSession(updatedSession);

      // 生成系统消息通知用户
      const selectedKBs = allKnowledgeBases.filter(kb => currentSessionKnowledgeBases.includes(kb.id));
      const kbNames = selectedKBs.map(kb => kb.name);

      const systemMessage = {
        sender: 'system',
        content: `会话设置已更新${
          kbNames.length > 0 ? `，当前关联知识库：${kbNames.join('、')}` : '，已清除所有关联知识库'
        }`,
        timestamp: new Date().toISOString()
      };

      const newMessages = [...messages, systemMessage];
      setMessages(newMessages);
      dataManager.addMessage(currentSession.id, systemMessage);

      setSettingsModalVisible(false);
      message.success('设置已保存');
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error('保存设置失败');
    }
  };

  // 创建新会话
  const createNewSession = () => {
    if (selectedModelsForNewSession.length === 0) {
      message.warning('请至少选择一个模型');
      return;
    }

    if (!newSessionName.trim()) {
      message.warning('请输入会话名称');
      return;
    }

    const selectedModels = allModels.filter(model => 
      selectedModelsForNewSession.includes(model.id)
    );

    const session = dataManager.createSession('chatroom', {
      title: newSessionName.trim(),
      invitedModels: selectedModelsForNewSession,
      coordinator: newSessionCoordinator,
      partnerId: newSessionPartner,
      knowledgeBases: selectedKnowledgeBases // 🚀 新增：关联知识库
    });

    setCurrentSession(session);
    setInvitedModels(selectedModels);
    setMessages([]);
    loopProtectionRef.current.reset();
    
    // 🎯 重置滚动状态
    isUserScrolledUpRef.current = false;
    setShowScrollToBottom(false);

    // 重置会话级别的发言统计
    coordinatorManagerRef.current.resetSessionStats(session.id);

    // 设置中控模型和搭档
    if (newSessionCoordinator) {
      const coordinator = allModels.find(m => m.id === newSessionCoordinator);
      setCoordinatorModel(coordinator);
    }
    if (newSessionPartner) {
      const partner = allPartners.find(p => p.id === newSessionPartner);
      setSelectedPartner(partner);
    }

    // 设置最大轮数
    setMaxRounds(newSessionMaxRounds);
    
    // 添加系统消息
    // 获取选中的知识库名称
    const selectedKBs = allKnowledgeBases.filter(kb => selectedKnowledgeBases.includes(kb.id));
    const kbNames = selectedKBs.map(kb => kb.name);

    const systemMessage = {
      sender: 'system',
      content: `聊天室"${newSessionName.trim()}"已创建，邀请了 ${selectedModels.length} 个模型：${selectedModels.map(m => m.name).join('、')}${
        kbNames.length > 0 ? `，关联了 ${kbNames.length} 个知识库：${kbNames.join('、')}` : ''
      }`,
      timestamp: new Date().toISOString()
    };
    
    const newMessages = [systemMessage];
    setMessages(newMessages);
    dataManager.addMessage(session.id, systemMessage);
    
    // 关闭弹窗
    setCreateSessionModalVisible(false);
    setNewSessionName('');
    setSelectedModelsForNewSession([]);
    setNewSessionCoordinator(null);
    setNewSessionPartner(null);
    setNewSessionMaxRounds(12);
    setSelectedKnowledgeBases([]); // 🚀 新增：重置知识库选择
    
    // 刷新会话列表
    loadData();
    
    message.success('聊天室已创建');
  };

  // 加载会话
  const loadSession = (sessionId) => {
    const session = dataManager.getSession(sessionId);
    if (!session) return;

    setCurrentSession(session);
    setMessages(session.messages || []);
    
    // 🎯 重置滚动状态（加载会话时默认滚动到底部）
    isUserScrolledUpRef.current = false;
    setShowScrollToBottom(false);
    
    // 恢复会话状态
    const sessionModels = allModels.filter(m => 
      session.invitedModels?.includes(m.id)
    );
    setInvitedModels(sessionModels);
    
    if (session.coordinator) {
      const coordinator = allModels.find(m => m.id === session.coordinator);
      setCoordinatorModel(coordinator);
    } else {
      setCoordinatorModel(null); // 🔥 修复：明确清空中控模型
    }
    
    if (session.partnerId) {
      const partner = allPartners.find(p => p.id === session.partnerId);
      setSelectedPartner(partner);
    } else {
      setSelectedPartner(null); // 🔥 修复：明确清空搭档角色
    }
    
    // 🔥 修复：恢复最大讨论轮数
    if (session.maxRounds) {
      setMaxRounds(session.maxRounds);
    } else {
      setMaxRounds(20); // 默认值
    }
    
    loopProtectionRef.current.reset();
    setIsConversationActive(false);

    // 重置会话级别的发言统计
    coordinatorManagerRef.current.resetSessionStats(sessionId);
  };

  // 删除会话
  const deleteSession = (sessionId) => {
    Modal.confirm({
      title: (
        <span style={{
          color: '#ffffff',
          textShadow: '0 0 8px rgba(255, 107, 107, 0.3)'
        }}>
          🗑️ 确认删除会话
        </span>
      ),
      content: (
        <div style={{
          color: '#ffffff',
          lineHeight: '1.6'
        }}>
          删除后无法恢复，确定要删除这个会话吗？
        </div>
      ),
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      centered: true,
      maskStyle: {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        backdropFilter: 'blur(10px)'
      },
      styles: {
        mask: {
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(10px)'
        },
        content: {
          background: 'rgba(26, 31, 46, 0.95)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 107, 107, 0.3)',
          boxShadow: '0 20px 60px rgba(255, 107, 107, 0.2)'
        },
        header: {
          background: 'transparent',
          borderBottom: '1px solid rgba(255, 107, 107, 0.3)'
        },
        body: {
          background: 'transparent'
        },
        footer: {
          background: 'transparent',
          borderTop: '1px solid rgba(255, 107, 107, 0.3)'
        }
      },
      onOk: () => {
        dataManager.deleteSession(sessionId);
        setSessions(prev => prev.filter(s => s.id !== sessionId));

        if (currentSession?.id === sessionId) {
          setCurrentSession(null);
          setMessages([]);
          setInvitedModels([]);
        }

        message.success('会话已删除');
      }
    });
  };

  // 发送用户消息
  const sendMessage = async () => {
    if (!inputValue.trim()) {
      return;
    }
    if (!currentSession) {
      message.warning('请先创建会话');
      return;
    }
    if (invitedModels.length === 0) {
      message.warning('请先邀请模型');
      return;
    }

    let messageContent = inputValue.trim();
    let targetModel = selectedModelForInput;

    // 检查是否使用了/命令指定模型
    if (messageContent.startsWith('/')) {
      const spaceIndex = messageContent.indexOf(' ');
      if (spaceIndex > 0) {
        const modelCommand = messageContent.substring(1, spaceIndex);
        const foundModel = invitedModels.find(m =>
          m.name.toLowerCase().includes(modelCommand.toLowerCase()) ||
          m.id.toLowerCase().includes(modelCommand.toLowerCase())
        );

        if (foundModel) {
          targetModel = foundModel;
          messageContent = messageContent.substring(spaceIndex + 1).trim();
          console.log('💬 [ChatRoom] 检测到/命令指定模型:', foundModel.name);
        }
      }
    }

    // 🔥 修复：如果AI讨论正在进行中，先中断讨论（stopAIDiscussion会处理反馈消息）
    if (isAIDiscussionActive) {
      console.log('🛑 [ChatRoom] 用户通过发送消息打断AI讨论');
      await stopAIDiscussion();

      // 等待一小段时间确保状态更新完成
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    const userMessage = {
      sender: 'user',
      content: messageContent,
      timestamp: new Date().toISOString(),
      targetModel: targetModel?.name, // 记录用户指定的目标模型
      files: uploadedFiles.length > 0 ? [...uploadedFiles] : undefined // 🔥 新增：包含上传的文件
    };

    const newMessages = [...messages, userMessage];
    setMessages(newMessages);
    dataManager.addMessage(currentSession.id, userMessage);

    setInputValue('');
    setSelectedModelForInput(null); // 清除输入框的模型选择
    setUploadedFiles([]); // 🔥 新增：清除已上传的文件
    setIsConversationActive(true);

    // 🔥 修复：用户发送第一条消息后立即检查是否需要生成标题
    if (messages.filter(msg => msg.sender === 'user').length === 0) {
      // 这是第一条用户消息，异步生成标题
      setTimeout(() => {
        checkAndGenerateSessionTitle(newMessages);
      }, 500);
    }

    // 重置循环保护和讨论状态
    loopProtectionRef.current.reset();
    setDiscussionRounds(0);

    // 🌐 网络搜索处理
    let searchResults = null;
    if (enableWebSearch && webSearchManagerRef.current) {
      try {
        const searchQuery = webSearchManagerRef.current.extractSearchQuery(messageContent);
        if (searchQuery) {
          console.log('🔍 [ChatRoom] 检测到搜索意图，执行网络搜索:', searchQuery);

          // 显示搜索状态消息
          const searchStatusMessage = {
            id: `search_status_${Date.now()}`,
            sender: 'system',
            content: `🔍 正在搜索网络信息: "${searchQuery}"...`,
            timestamp: new Date().toISOString(),
            type: 'search_status'
          };

          setMessages(prev => [...prev, searchStatusMessage]);
          dataManager.addMessage(currentSession.id, searchStatusMessage);

          // 执行搜索
          searchResults = await webSearchManagerRef.current.search(searchQuery, {
            maxResults: 5,
            includeContent: true,
            language: 'zh-CN'
          });

          // 显示搜索结果
          const searchResultMessage = {
            id: `search_result_${Date.now()}`,
            sender: 'system',
            content: webSearchManagerRef.current.formatResultsForAI(searchResults),
            timestamp: new Date().toISOString(),
            type: 'search_result',
            searchData: searchResults
          };

          setMessages(prev => prev.map(msg =>
            msg.id === searchStatusMessage.id ? searchResultMessage : msg
          ));
          dataManager.addMessage(currentSession.id, searchResultMessage);

          console.log(`✅ [ChatRoom] 网络搜索完成，找到 ${searchResults.totalResults} 个结果`);
        }
      } catch (error) {
        console.error('❌ [ChatRoom] 网络搜索失败:', error);

        // 显示搜索错误
        const errorMessage = {
          id: `search_error_${Date.now()}`,
          sender: 'system',
          content: `❌ 网络搜索失败: ${error.message}`,
          timestamp: new Date().toISOString(),
          type: 'search_error'
        };

        setMessages(prev => [...prev, errorMessage]);
        dataManager.addMessage(currentSession.id, errorMessage);
      }
    }

    // 开始AI协作（包含搜索结果的消息）
    let finalMessages = [...newMessages];

    if (searchResults && searchResults.results && searchResults.results.length > 0) {
      // 🌐 将搜索结果添加到消息上下文中
      const formattedResults = webSearchManagerRef.current.formatResultsForAI(searchResults);
      const searchContextMessage = {
        id: `search_context_${Date.now()}`,
        sender: 'user', // 🔧 改为user角色，确保AI能看到
        content: `[网络搜索结果 - 请基于以下实时信息回答用户问题]\n\n${formattedResults}\n\n[以上是网络搜索结果，请结合这些信息回答用户的问题]`,
        timestamp: new Date().toISOString(),
        type: 'search_context',
        searchData: searchResults,
        isContextOnly: true // 标记为仅供AI参考的上下文消息
      };

      // 将搜索结果添加到消息列表中，但不显示给用户
      finalMessages.push(searchContextMessage);

      console.log('🌐 [ChatRoom] 已将搜索结果添加到AI上下文中');
    }

    await startAICollaboration(finalMessages);
  };

  // 停止AI讨论
  const stopAIDiscussion = async () => {
    console.log('🛑 [ChatRoom] 用户主动停止AI讨论');

    // 🔥 修复：中断所有正在进行的操作
    if (discussionAbortController) {
      discussionAbortController.abort();
      setDiscussionAbortController(null);
    }

    // 🔥 修复：完整的状态清理
    setIsAIDiscussionActive(false);
    setIsConversationActive(false);
    setDiscussionRounds(0);
    setCurrentSchedulingStatus(''); // 清理调度状态

    // 🔥 新增：清理调度状态指示器
    setSchedulingProgress({
      isActive: false,
      stage: '',
      progress: 0,
      message: '',
      selectedModel: null,
      confidence: 0,
      startTime: null,
      estimatedTime: 0
    });

    // 🎨 科技感用户打断反馈消息
    const interruptMessage = {
      sender: 'system',
      content: '⚡ **用户已打断AI协作讨论** - 系统已停止当前讨论流程',
      timestamp: new Date().toISOString(),
      type: 'user_interrupt'
    };

    // 添加打断消息到界面和数据管理器
    setMessages(prev => [...prev, interruptMessage]);
    if (currentSession) {
      dataManager.addMessage(currentSession.id, interruptMessage);
    }

    console.log('✅ [ChatRoom] AI讨论已停止，用户反馈已添加');
  };

  // 🔥 新增：文件处理相关函数

  // 检测消息是否包含Vision内容
  const hasVisionContent = (messages) => {
    const lastUserMessage = messages.find(msg => msg.sender === 'user');
    return lastUserMessage && Array.isArray(lastUserMessage.content) &&
      lastUserMessage.content.some(item =>
        item &&
        typeof item === 'object' &&
        item.type === 'image_url' &&
        item.image_url &&
        typeof item.image_url.url === 'string' &&
        item.image_url.url.startsWith('data:image/')
      );
  };

  // 过滤支持Vision的模型
  const filterVisionCapableModels = (messages, models, context = '') => {
    const hasVision = hasVisionContent(messages);

    if (!hasVision) {
      console.log(`🔍 [ChatRoom] ${context}：消息不包含图片，使用所有邀请的模型`);
      return { availableModels: models, hasVision: false };
    }

    console.log(`🖼️ [ChatRoom] ${context}：检测到Vision内容，过滤支持Vision的模型`);
    console.log(`🖼️ [ChatRoom] 原邀请模型:`, models.map(m => `${m.name}(Vision:${m.supportVision ? '✓' : '✗'})`));

    const visionCapableModels = models.filter(model => model.supportVision === true);

    if (visionCapableModels.length === 0) {
      console.error(`❌ [ChatRoom] ${context}：消息包含图片，但没有支持Vision的模型`);
      return { availableModels: null, hasVision: true, error: '当前邀请的模型都不支持图片识别，请邀请支持Vision功能的模型（如GPT-4 Vision、Claude 3等）' };
    }

    console.log(`🎯 [ChatRoom] ${context}：已过滤为Vision支持模型:`, visionCapableModels.map(m => m.name));
    return { availableModels: visionCapableModels, hasVision: true };
  };

  // 将文件转换为base64
  const fileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  };

  // 检查文件类型和大小
  const validateFile = (file) => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
      // 图片类型
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp',
      // 文本类型
      'text/plain', 'text/markdown', 'application/json', 'text/csv',
      // 其他常见类型
      'application/pdf', 'text/html', 'text/css', 'text/javascript'
    ];

    if (file.size > maxSize) {
      message.error('文件大小不能超过10MB');
      return false;
    }

    if (!allowedTypes.includes(file.type)) {
      message.error('不支持的文件类型');
      return false;
    }

    return true;
  };

  // 处理文件上传
  const handleFileUpload = async (file) => {
    if (!validateFile(file)) {
      return false;
    }

    try {
      const base64 = await fileToBase64(file);
      const fileInfo = {
        id: Date.now() + Math.random(),
        name: file.name,
        type: file.type,
        size: file.size,
        base64: base64,
        isImage: file.type.startsWith('image/')
      };

      setUploadedFiles(prev => [...prev, fileInfo]);

      // 🔥 新增：检查是否有支持Vision的模型
      if (fileInfo.isImage) {
        const visionSupportedModels = invitedModels.filter(model => model.supportVision);
        if (visionSupportedModels.length > 0) {
          message.success(`图片 "${file.name}" 上传成功！支持Vision的模型：${visionSupportedModels.map(m => m.name).join('、')}`);
        } else {
          message.warning(`图片 "${file.name}" 上传成功，但当前邀请的模型都不支持图片识别`);
        }
      } else {
        message.success(`文件 "${file.name}" 上传成功`);
      }

      return false; // 阻止默认上传行为
    } catch (error) {
      console.error('文件上传失败:', error);
      message.error('文件上传失败');
      return false;
    }
  };

  // 删除已上传的文件
  const removeUploadedFile = (fileId) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  // 处理拖拽上传
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    files.forEach(file => handleFileUpload(file));
  };

  // 处理粘贴上传
  const handlePaste = async (e) => {
    const items = Array.from(e.clipboardData.items);

    for (const item of items) {
      if (item.kind === 'file') {
        const file = item.getAsFile();
        if (file) {
          await handleFileUpload(file);
        }
      }
    }
  };

  // 开始AI协作
  const startAICollaboration = async (currentMessages) => {
    console.log('🤖 [ChatRoom] 开始AI协作');

    try {
      const lastUserMessage = getLastUserMessage(currentMessages);

      // 检查用户是否指定了特定模型
      const userSpecifiedModel = detectUserSpecifiedModel(lastUserMessage, invitedModels);

      if (userSpecifiedModel) {
        // 用户指定模式：只让指定的模型回答
        console.log('👤 [ChatRoom] 用户指定模式，目标模型:', userSpecifiedModel.name);
        await processSingleModelResponse(userSpecifiedModel, currentMessages, '用户指定回答');
      } else {
        // 🔥 单模型场景优化：检测只有一个模型时直接回答
      if (invitedModels.length === 1) {
        console.log('⚡ [ChatRoom] 单模型场景，直接回答');
        await processSingleModelResponse(invitedModels[0], currentMessages, '单模型直接回答');
        return;
      }

      // 根据调度模式选择策略
        if (schedulingMode === 'intelligent' && enableCoordinator && coordinatorModel) {
          // 智能调度模式：启动多轮协作讨论
          console.log('🧠 [ChatRoom] 智能协作模式，使用中控模型:', coordinatorModel.name);
          await startIntelligentCollaboration(currentMessages);
        } else if (schedulingMode === 'roundRobin') {
          // 轮流调度模式：所有模型依次回答
          console.log('🔄 [ChatRoom] 轮流调度模式，邀请的模型数量:', invitedModels.length);
          await processRoundRobinScheduling(currentMessages);
        } else {
          // 随机调度模式：随机选择一个模型
          console.log('🎲 [ChatRoom] 随机调度模式');
          await processRandomScheduling(currentMessages);
        }
      }

    } catch (error) {
      console.error('❌ [ChatRoom] AI协作过程中出错:', error);
      message.error('AI协作过程中出现错误: ' + error.message);
      setIsConversationActive(false);
      setIsAIDiscussionActive(false);
    }
  };

  // 获取最后一条用户消息
  const getLastUserMessage = (messages) => {
    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i].sender === 'user') {
        return messages[i];
      }
    }
    return null;
  };

  // 检测用户是否指定了特定模型
  const detectUserSpecifiedModel = (userMessage, models) => {
    if (!userMessage) return null;

    // 优先检查targetModel（通过/命令或下拉选择指定的模型）
    if (userMessage.targetModel) {
      const targetModel = models.find(m => m.name === userMessage.targetModel);
      if (targetModel) {
        console.log('🎯 [ChatRoom] 检测到目标模型:', targetModel.name);
        return targetModel;
      }
    }

    // 如果没有targetModel，检查消息内容中的指定模式
    if (!userMessage.content) return null;

    const content = userMessage.content.toLowerCase();

    // 检测各种指定模式
    for (const model of models) {
      const modelName = model.name.toLowerCase();

      // 模式1: "模型A，你怎么看？"
      if (content.includes(`${modelName}，`) || content.includes(`${modelName},`)) {
        return model;
      }

      // 模式2: "@模型B 请回答"
      if (content.includes(`@${modelName}`) || content.includes(`@ ${modelName}`)) {
        return model;
      }

      // 模式3: "让模型C来分析"
      if (content.includes(`让${modelName}`) || content.includes(`请${modelName}`)) {
        return model;
      }

      // 模式4: "模型A你觉得"
      if (content.includes(`${modelName}你`) || content.includes(`${modelName} 你`)) {
        return model;
      }
    }

    return null;
  };

  // 生成会话标题
  const generateSessionTitle = async (userMessage) => {
    if (!coordinatorModel || !currentSession) {
      console.log('📝 [ChatRoom] 无中控模型或当前会话，跳过标题生成');
      return;
    }

    try {
      console.log('📝 [ChatRoom] 开始生成会话标题');
      console.log('📝 [ChatRoom] 用户问题:', userMessage.substring(0, 100));

      // 🔥 修复：针对o3模型优化的提示词，明确要求输出
      const titlePrompt = `请直接输出一个2-10字的标题来概括这个问题：

${userMessage}

要求：只输出标题，不要解释或推理过程。

示例输出：
Python学习
React vs Vue
报数游戏`;

      const titleMessages = [
        {
          sender: 'user',
          content: titlePrompt,
          timestamp: new Date().toISOString()
        }
      ];

      const response = await apiManagerRef.current.sendChatRequest(
        coordinatorModel,
        titleMessages,
        {
          maxTokens: 300, // 🔥 修复：对于o3模型，使用较少的token，避免过度推理
          temperature: 0, // 设置为0，要求确定性输出
          enableThinking: false // 明确禁用思考模式
        }
      );

      console.log('🔧 [ChatRoom] 标题生成响应:', response);

      if (response && response.success && response.data) {
        // 🔥 修复：检查内容是否为空，包括finishReason
        const content = response.data.content;
        const finishReason = response.data.finishReason;

        console.log('🔧 [ChatRoom] API响应详情:', {
          content: content,
          finishReason: finishReason,
          contentLength: content?.length || 0
        });

        if (!content || content.trim() === '') {
          if (finishReason === 'length') {
            console.error('❌ [ChatRoom] 标题生成被长度限制截断，内容为空');
            // 使用用户问题的前几个字符作为标题
            const fallbackTitle = userMessage.substring(0, 8).replace(/[？?！!。.，,]/g, '');
            if (fallbackTitle.length >= 2) {
              console.log('🔧 [ChatRoom] 使用降级标题:', fallbackTitle);
              const updatedSession = { ...currentSession, title: fallbackTitle };
              setCurrentSession(updatedSession);
              dataManager.updateSession(currentSession.id, { title: fallbackTitle });
              loadData();
              return;
            }
          }
          console.error('❌ [ChatRoom] 标题内容为空，保持原标题');
          return;
        }

        let newTitle = content.trim();
        console.log('🔧 [ChatRoom] 原始标题内容:', newTitle);

        // 清理标题格式
        newTitle = newTitle.replace(/^["'「『]|["'」』]$/g, ''); // 移除各种引号
        newTitle = newTitle.replace(/^标题[:：]?\s*/, ''); // 移除"标题："前缀
        newTitle = newTitle.replace(/\n.*$/s, ''); // 只取第一行

        console.log('🔧 [ChatRoom] 清理后标题:', newTitle);

        // 🔥 修复：限制长度为2-10个字符
        if (newTitle.length > 10) {
          newTitle = newTitle.substring(0, 10);
        }

        // 确保最少2个字符
        if (newTitle.length < 2) {
          console.warn('⚠️ [ChatRoom] 生成的标题过短，使用默认标题');
          newTitle = '新对话';
        }

        if (newTitle && newTitle.length > 0) {
          // 更新会话标题
          const updatedSession = {
            ...currentSession,
            title: newTitle
          };

          setCurrentSession(updatedSession);
          dataManager.updateSession(currentSession.id, { title: newTitle });

          // 刷新会话列表
          loadData();

          console.log('✅ [ChatRoom] 会话标题已生成:', newTitle);
        } else {
          console.warn('⚠️ [ChatRoom] 生成的标题为空，保持原标题');
        }
      } else {
        console.error('❌ [ChatRoom] 标题生成请求失败:', {
          response: response,
          success: response?.success,
          data: response?.data,
          error: response?.error
        });
      }
    } catch (error) {
      console.error('❌ [ChatRoom] 生成会话标题失败:', error);
    }
  };

  // 检查并生成会话标题
  const checkAndGenerateSessionTitle = async (messages) => {
    if (!currentSession || !coordinatorModel) {
      return;
    }

    // 🔥 修复：使用统一的标题检查函数
    if (!shouldGenerateTitle(currentSession)) {
      return;
    }

    // 获取用户的原始问题
    const userMessage = messages.find(msg => msg.sender === 'user');
    if (!userMessage) {
      console.log('📝 [ChatRoom] 未找到用户消息，跳过标题生成');
      return;
    }

    console.log('📝 [ChatRoom] 检测到临时标题，开始生成智能会话标题');
    console.log('📝 [ChatRoom] 当前标题:', currentSession.title);
    console.log('📝 [ChatRoom] 中控模型:', coordinatorModel?.name || '未配置');

    // 🔥 修复：只使用用户问题生成标题，不需要等待AI回复
    await generateSessionTitle(userMessage.content);
  };

  // 验证消息完整性
  const validateMessageIntegrity = (messages, context) => {
    console.log(`🔍 [ChatRoom] 验证消息完整性 - ${context}`);
    console.log(`🔍 [ChatRoom] 消息数量: ${messages.length}`);

    const userMessages = messages.filter(msg => msg.sender === 'user');
    const aiMessages = messages.filter(msg => msg.sender !== 'user' && msg.sender !== 'system');
    const systemMessages = messages.filter(msg => msg.sender === 'system');

    console.log(`🔍 [ChatRoom] 用户消息: ${userMessages.length}条`);
    console.log(`🔍 [ChatRoom] AI消息: ${aiMessages.length}条`);
    console.log(`🔍 [ChatRoom] 系统消息: ${systemMessages.length}条`);

    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      const safeContent = typeof lastMessage.content === 'string'
        ? lastMessage.content.substring(0, 100) + '...'
        : Array.isArray(lastMessage.content)
          ? (lastMessage.content.find(item => item.type === 'text')?.text || '[Vision消息]').substring(0, 100) + '...'
          : '[非文本内容]';

      console.log(`🔍 [ChatRoom] 最后一条消息:`, {
        sender: lastMessage.sender,
        content: safeContent
      });
    }

    return {
      total: messages.length,
      userCount: userMessages.length,
      aiCount: aiMessages.length,
      systemCount: systemMessages.length
    };
  };

  // 启动智能协作讨论
  const startIntelligentCollaboration = async (initialMessages) => {
    console.log('🧠 [ChatRoom] 启动智能协作讨论');
    console.log('🧠 [ChatRoom] 初始消息数量:', initialMessages.length);
    console.log('🧠 [ChatRoom] 初始消息概览:', initialMessages.map((msg, index) => {
      const safeContent = typeof msg.content === 'string'
        ? msg.content.substring(0, 50) + (msg.content.length > 50 ? '...' : '')
        : Array.isArray(msg.content)
          ? (msg.content.find(item => item.type === 'text')?.text || '[Vision消息]').substring(0, 50) + '...'
          : '[非文本内容]';

      return {
        index: index + 1,
        sender: msg.sender,
        contentPreview: safeContent
      };
    }));

    // 创建中断控制器
    const abortController = new AbortController();
    setDiscussionAbortController(abortController);
    setIsAIDiscussionActive(true);
    setDiscussionRounds(0);

    // 🔥 修复：重置调度状态，确保状态显示正常
    setCurrentSchedulingStatus('准备开始智能协作讨论...');
    console.log('🔄 [ChatRoom] 状态已重置，准备开始新的协作讨论');

    // 确保workingMessages包含完整的初始消息
    let workingMessages = [...initialMessages];
    let currentRound = 0;

    // 验证初始消息完整性
    validateMessageIntegrity(workingMessages, '协作讨论开始前');

    try {
      while (currentRound < maxRounds && !abortController.signal.aborted) {
        currentRound++;
        setDiscussionRounds(currentRound);
        setCurrentSchedulingStatus(`正在调度第 ${currentRound} 轮...`);

        console.log(`🧠 [ChatRoom] 协作讨论第 ${currentRound}/${maxRounds} 轮`);

        // 使用中控模型进行智能调度
        setCurrentSchedulingStatus(`正在调度第 ${currentRound} 轮，中控模型分析中...`);

        // 🔥 新增：启动调度状态指示器
        setSchedulingProgress({
          isActive: true,
          stage: 'analyzing',
          progress: 10,
          message: `第 ${currentRound} 轮调度开始`,
          selectedModel: null,
          confidence: 0,
          startTime: Date.now(),
          estimatedTime: 15
        });

        // 🔥 新增：检测是否包含Vision内容，过滤可用模型
        const visionFilterResult = filterVisionCapableModels(workingMessages, invitedModels, '智能协作讨论');

        if (visionFilterResult.error) {
          // 添加错误提示消息
          const errorMessage = {
            sender: 'system',
            content: `❌ ${visionFilterResult.error}`,
            timestamp: new Date().toISOString(),
            type: 'error'
          };

          workingMessages = [...workingMessages, errorMessage];
          setMessages(workingMessages);
          dataManager.addMessage(currentSession.id, errorMessage);
          break;
        }

        const availableModels = visionFilterResult.availableModels;

        // 🔥 新增：更新调度状态 - 选择模型阶段
        setSchedulingProgress(prev => ({
          ...prev,
          stage: 'selecting',
          progress: 30,
          message: `分析 ${availableModels.length} 个可用模型`
        }));

        const schedulingResult = await coordinatorManagerRef.current.scheduleNextModel(
          coordinatorModel,
          availableModels, // 🔥 修复：使用过滤后的模型列表
          { messages: workingMessages },
          {
            showThinking,
            allowCoordinatorToSpeak,
            discussionRound: currentRound,
            maxRounds,
            isCollaborativeDiscussion: true,
            sessionId: currentSession.id,
            abortController: abortController, // 🔥 修复：传递AbortController
            // 🔥 新增：智能结束检测参数
            enableIntelligentEnd,
            discussionQualityThreshold
          }
        );

        // 🔥 新增：详细的调度结果日志
        console.log('🎯 [ChatRoom] 调度结果详情:', {
          shouldContinue: schedulingResult.shouldContinue,
          shouldEndDiscussion: schedulingResult.shouldEndDiscussion,
          selectedModel: schedulingResult.selectedModel?.name,
          reasoning: schedulingResult.reasoning,
          endReason: schedulingResult.endReason,
          confidence: schedulingResult.confidence
        });

        // 🔥 新增：更新调度状态 - 显示选择结果
        setSchedulingProgress(prev => ({
          ...prev,
          stage: 'generating',
          progress: 60,
          message: schedulingResult.reasoning || '准备生成回复',
          selectedModel: schedulingResult.selectedModel?.name || null,
          confidence: schedulingResult.confidence || 0
        }));

        if (abortController.signal.aborted) {
          console.log('🛑 [ChatRoom] 协作讨论被中断');
          break;
        }

        if (!schedulingResult.success || !schedulingResult.selectedModel) {
          console.log('❌ [ChatRoom] 调度失败，结束讨论');
          break;
        }

        // 🔥 修复：检查调度模型的明确决策
        const shouldEndDiscussion = schedulingResult.shouldEndDiscussion ||
                                   schedulingResult.isIntelligentEnd ||
                                   (schedulingResult.shouldContinue === false);

        if (shouldEndDiscussion) {
          console.log('✅ [ChatRoom] 调度模型决定结束讨论');
          console.log('✅ [ChatRoom] shouldContinue:', schedulingResult.shouldContinue);
          console.log('✅ [ChatRoom] shouldEndDiscussion:', schedulingResult.shouldEndDiscussion);
          console.log('✅ [ChatRoom] 结束原因:', schedulingResult.endReason || schedulingResult.reasoning);

          // 🔥 新增：更新调度状态为"无需调度"
          setSchedulingProgress(prev => ({
            ...prev,
            stage: 'no_scheduling',
            progress: 100,
            message: schedulingResult.endReason || schedulingResult.reasoning || '讨论已充分完成'
          }));

          // 🔥 新增：延迟显示，让用户看到"无需调度"的状态
          await new Promise(resolve => setTimeout(resolve, 2000));

          // 🔥 修复：提供更详细的无需调度理由
          let endContent = '';
          if (schedulingResult.isIntelligentEnd || schedulingResult.endReason) {
            // 智能分析结束或调度模型明确结束
            endContent = `🧠 **智能调度分析完成**\n\n`;
            endContent += `📋 **调度决策**：${schedulingResult.endReason || schedulingResult.reasoning}\n\n`;

            if (schedulingResult.analysisScores) {
              endContent += `📊 **详细分析结果**：\n`;
              endContent += `• 内容重复度：${(schedulingResult.analysisScores.repetition * 100).toFixed(1)}%\n`;
              endContent += `• 问题覆盖度：${(schedulingResult.analysisScores.coverage * 100).toFixed(1)}%\n`;
              endContent += `• 共识达成度：${(schedulingResult.analysisScores.consensus * 100).toFixed(1)}%\n`;
              endContent += `• 整体完成度：${(schedulingResult.analysisScores.overall * 100).toFixed(1)}%\n\n`;
            }

            endContent += `💡 **调度结论**：经过 ${currentRound} 轮分析，调度模型认为当前话题已经得到充分讨论，无需继续安排其他模型回答。\n\n`;
            endContent += `✨ **建议**：如果您需要更多观点或有新的问题，请直接提出新的问题。`;
          } else {
            // 传统结束 - 也提供详细说明
            endContent = `🤖 **调度分析**\n\n`;
            endContent += `📋 **决策理由**：${schedulingResult.reasoning || '当前问题已经得到充分回答'}\n\n`;
            endContent += `💡 **说明**：智能调度系统分析后认为无需安排更多模型参与讨论。如有新问题，请继续提问。`;
          }

          // 添加讨论结束消息
          const endMessage = {
            sender: 'system',
            content: endContent,
            timestamp: new Date().toISOString(),
            type: 'discussion_end',
            isIntelligentEnd: schedulingResult.isIntelligentEnd || false
          };

          workingMessages = [...workingMessages, endMessage];
          setMessages(workingMessages);
          dataManager.addMessage(currentSession.id, endMessage);

          // 🔥 新增：清理调度状态
          setSchedulingProgress(prev => ({
            ...prev,
            isActive: false,
            stage: 'complete',
            progress: 100,
            message: '讨论已结束'
          }));

          break;
        }

        console.log(`🎯 [ChatRoom] 第${currentRound}轮选择模型:`, schedulingResult.selectedModel.name);
        console.log(`🎯 [ChatRoom] 调度理由:`, schedulingResult.reasoning);

        // 更新状态显示选中的模型
        setCurrentSchedulingStatus(`第 ${currentRound} 轮：${schedulingResult.selectedModel.name} 正在回复...`);

        // 让选中的模型回复
        const modelInfo = {
          model: schedulingResult.selectedModel,
          schedulingInfo: {
            ...schedulingResult,
            discussionRound: currentRound,
            maxRounds
          },
          interactionPrompt: schedulingResult.interactionPrompt // 🔥 传递互动提示词
        };

        console.log(`🔍 [ChatRoom] 第${currentRound}轮调度前，传递给模型的消息数量:`, workingMessages.length);
        console.log(`🔍 [ChatRoom] 传递给模型的最后3条消息:`, workingMessages.slice(-3).map(msg => {
          const safeContent = typeof msg.content === 'string'
            ? msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : '')
            : Array.isArray(msg.content)
              ? (msg.content.find(item => item.type === 'text')?.text || '[Vision消息]').substring(0, 100) + '...'
              : '[非文本内容]';

          return {
            sender: msg.sender,
            content: safeContent
          };
        }));

        const response = await sendModelRequest(modelInfo, workingMessages, abortController); // 🔥 修复：传递AbortController

        if (abortController.signal.aborted) {
          console.log('🛑 [ChatRoom] 协作讨论被中断');
          break;
        }

        if (!response) {
          console.log(`❌ [ChatRoom] 模型 ${schedulingResult.selectedModel.name} 回复失败，继续下一轮`);

          // 添加失败提示消息，让其他模型可以"嘲笑"或评论
          const failureMessage = {
            sender: 'system',
            content: `模型 ${schedulingResult.selectedModel.name} 请求失败，暂时无法参与讨论。`,
            timestamp: new Date().toISOString(),
            type: 'model_failure',
            failedModel: schedulingResult.selectedModel.name
          };

          workingMessages = [...workingMessages, failureMessage];
          setMessages(workingMessages);
          dataManager.addMessage(currentSession.id, failureMessage);

          // 继续下一轮，不中断讨论
          continue;
        }

        // 关键修复：从response中获取最终消息，确保workingMessages包含最新内容
        const finalMessage = {
          sender: schedulingResult.selectedModel.name,
          content: response.content,
          thinking: response.thinking,
          timestamp: new Date().toISOString(),
          schedulingInfo: response.schedulingInfo
        };

        // 更新工作消息，确保包含刚刚添加的消息
        workingMessages = [...workingMessages, finalMessage];

        console.log(`✅ [ChatRoom] 第${currentRound}轮完成，workingMessages更新后数量:`, workingMessages.length);

        const safeContent = typeof finalMessage.content === 'string'
          ? finalMessage.content.substring(0, 100) + (finalMessage.content.length > 100 ? '...' : '')
          : Array.isArray(finalMessage.content)
            ? (finalMessage.content.find(item => item.type === 'text')?.text || '[Vision消息]').substring(0, 100) + '...'
            : '[非文本内容]';

        console.log(`✅ [ChatRoom] 最新消息:`, {
          sender: finalMessage.sender,
          content: safeContent
        });

        // 验证消息完整性
        validateMessageIntegrity(workingMessages, `第${currentRound}轮完成后`);

        // 🔥 新增：更新调度状态 - 轮次完成
        setSchedulingProgress(prev => ({
          ...prev,
          stage: 'complete',
          progress: 90,
          message: `第 ${currentRound} 轮完成，准备下一轮`
        }));

        // 轮次间延迟，让用户有时间阅读
        if (currentRound < maxRounds && !abortController.signal.aborted) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      // 讨论完成
      if (currentRound >= maxRounds) {
        const completionMessage = {
          sender: 'system',
          content: `智能协作讨论完成，共进行了 ${currentRound} 轮对话。`,
          timestamp: new Date().toISOString(),
          type: 'discussion_complete'
        };

        workingMessages = [...workingMessages, completionMessage];
        setMessages(workingMessages);
        dataManager.addMessage(currentSession.id, completionMessage);
      }

    } catch (error) {
      console.error('❌ [ChatRoom] 智能协作讨论出错:', error);

      const errorMessage = {
        sender: 'system',
        content: `协作讨论出现错误：${error.message}`,
        timestamp: new Date().toISOString(),
        type: 'error'
      };

      workingMessages = [...workingMessages, errorMessage];
      setMessages(workingMessages);
      dataManager.addMessage(currentSession.id, errorMessage);
    } finally {
      setIsAIDiscussionActive(false);
      setIsConversationActive(false);
      setDiscussionRounds(0);
      setDiscussionAbortController(null);
      setCurrentSchedulingStatus('');

      // 🔥 新增：清理调度状态指示器
      setSchedulingProgress({
        isActive: false,
        stage: '',
        progress: 0,
        message: '',
        selectedModel: null,
        confidence: 0,
        startTime: null,
        estimatedTime: 0
      });

      // 🔥 修复：智能协作讨论完成后检查是否需要生成会话标题
      setTimeout(() => {
        checkAndGenerateSessionTitle(workingMessages);
      }, 1000);

      console.log('✅ [ChatRoom] 智能协作讨论结束');
    }
  };

  // 处理单个模型回复（增强版：支持中断）
  const processSingleModelResponse = async (model, workingMessages, reason, abortController = null) => {
    console.log('📡 [ChatRoom] 处理单个模型回复:', model.name);

    // 🚀 如果没有传入AbortController，创建一个新的
    let localAbortController = abortController;
    if (!localAbortController) {
      localAbortController = new AbortController();
      setDiscussionAbortController(localAbortController);
    }

    const modelInfo = { model, schedulingInfo: { reasoning: reason } };

    try {
      const response = await sendModelRequest(modelInfo, workingMessages, localAbortController);

      if (localAbortController.signal.aborted) {
        console.log('🛑 [ChatRoom] 单个模型回复被中断');
        return null;
      }

      if (response) {
        console.log('✅ [ChatRoom] 单个模型回复完成');
      } else {
        console.log('❌ [ChatRoom] 单个模型回复失败');
        // 添加失败提示消息
        const failureMessage = {
          sender: 'system',
          content: `模型 ${model.name} 暂时无法回复，请稍后重试。`,
          timestamp: new Date().toISOString(),
          type: 'failure'
        };

        const newMessages = [...workingMessages, failureMessage];
        setMessages(newMessages);
        dataManager.addMessage(currentSession.id, failureMessage);
      }
    } catch (error) {
      if (localAbortController.signal.aborted) {
        console.log('🛑 [ChatRoom] 单个模型回复被用户中断');
      } else {
        console.error('❌ [ChatRoom] 单个模型回复出错:', error);
      }
    } finally {
      setIsConversationActive(false);
      if (!abortController) {
        // 只有在我们创建的AbortController时才清理
        setDiscussionAbortController(null);
      }
    }
  };

  // 智能调度模式：使用中控模型决策
  const processIntelligentScheduling = async (initialMessages) => {
    console.log('🧠 [ChatRoom] 开始智能调度');

    try {
      // 🔥 新增：检测是否包含Vision内容，过滤可用模型
      const visionFilterResult = filterVisionCapableModels(initialMessages, invitedModels, '智能调度');

      if (visionFilterResult.error) {
        // 添加错误提示消息
        const errorMessage = {
          sender: 'system',
          content: `❌ ${visionFilterResult.error}`,
          timestamp: new Date().toISOString(),
          type: 'error'
        };

        const newMessages = [...initialMessages, errorMessage];
        setMessages(newMessages);
        dataManager.addMessage(currentSession.id, errorMessage);
        return;
      }

      const availableModels = visionFilterResult.availableModels;

      // 使用中控模型进行调度决策
      const schedulingResult = await coordinatorManagerRef.current.scheduleNextModel(
        coordinatorModel,
        availableModels, // 🔥 修复：使用过滤后的模型列表
        { messages: initialMessages },
        {
          showThinking,
          allowCoordinatorToSpeak
        }
      );

      if (schedulingResult.success && schedulingResult.selectedModel) {
        console.log('✅ [ChatRoom] 中控调度成功，选择模型:', schedulingResult.selectedModel.name);

        const modelInfo = {
          model: schedulingResult.selectedModel,
          schedulingInfo: schedulingResult
        };

        const response = await sendModelRequest(modelInfo, initialMessages);

        if (response) {
          console.log('✅ [ChatRoom] 智能调度回复成功');
        } else {
          console.log('❌ [ChatRoom] 智能调度回复失败');
        }
      } else {
        console.log('❌ [ChatRoom] 中控调度失败，使用随机选择');
        await processRandomScheduling(initialMessages);
      }
    } catch (error) {
      console.error('❌ [ChatRoom] 智能调度出错:', error);
      await processRandomScheduling(initialMessages);
    }

    // 🎯 修复：智能调度完成后重置所有相关状态
    setIsConversationActive(false);
    setIsAIDiscussionActive(false);
  };

  // 轮流调度模式：所有模型依次回答
  const processRoundRobinScheduling = async (initialMessages) => {
    console.log('🔄 [ChatRoom] 开始轮流调度');

    let workingMessages = [...initialMessages];
    let successCount = 0;
    let failureCount = 0;

    // 确定参与回答的模型（排除中控模型，除非明确允许）
    const participatingModels = allowCoordinatorToSpeak
      ? invitedModels
      : invitedModels.filter(model =>
          !coordinatorModel || (model.id !== coordinatorModel.id && model.name !== coordinatorModel.name)
        );

    console.log('🔄 [ChatRoom] 参与回答的模型:', participatingModels.map(m => m.name));

    // 随机打乱模型顺序，确保公平性
    const shuffledModels = [...participatingModels].sort(() => Math.random() - 0.5);

    for (let i = 0; i < shuffledModels.length && isConversationActive; i++) {
      const model = shuffledModels[i];
      console.log(`🔄 [ChatRoom] 第${i + 1}/${shuffledModels.length}个模型回复:`, model.name);

      const modelInfo = {
        model,
        schedulingInfo: { reasoning: `轮流回答 (${i + 1}/${shuffledModels.length})` }
      };

      const response = await sendModelRequest(modelInfo, workingMessages);

      if (response) {
        successCount++;
        console.log(`✅ [ChatRoom] 模型 ${model.name} 回复成功`);
        workingMessages = [...messages];
      } else {
        failureCount++;
        console.log(`❌ [ChatRoom] 模型 ${model.name} 回复失败，跳过`);

        const failureMessage = {
          sender: 'system',
          content: `模型 ${model.name} 暂时无法回复，已跳过。`,
          timestamp: new Date().toISOString(),
          type: 'failure'
        };

        workingMessages = [...workingMessages, failureMessage];
        setMessages(workingMessages);
        dataManager.addMessage(currentSession.id, failureMessage);
      }

      // 模型间延迟
      if (i < shuffledModels.length - 1 && isConversationActive) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // 添加完成消息
    if (successCount > 0 || failureCount > 0) {
      const completionMessage = {
        sender: 'system',
        content: `轮流回复完成：${successCount} 个模型成功回复，${failureCount} 个模型失败。`,
        timestamp: new Date().toISOString(),
        type: 'completion'
      };

      workingMessages = [...workingMessages, completionMessage];
      setMessages(workingMessages);
      dataManager.addMessage(currentSession.id, completionMessage);
    }

    // 🎯 修复：轮流调度完成后重置所有相关状态
    setIsConversationActive(false);
    setIsAIDiscussionActive(false);

    // 🔥 修复：轮流调度完成后检查是否需要生成会话标题
    setTimeout(() => {
      checkAndGenerateSessionTitle(workingMessages);
    }, 1000);

    console.log('✅ [ChatRoom] 轮流调度完成');
  };

  // 随机调度模式：随机选择一个模型
  const processRandomScheduling = async (initialMessages) => {
    console.log('🎲 [ChatRoom] 开始随机调度');

    const participatingModels = allowCoordinatorToSpeak
      ? invitedModels
      : invitedModels.filter(model =>
          !coordinatorModel || (model.id !== coordinatorModel.id && model.name !== coordinatorModel.name)
        );

    if (participatingModels.length === 0) {
      console.log('❌ [ChatRoom] 没有可用的模型');
      setIsConversationActive(false);
      return;
    }

    const randomModel = participatingModels[Math.floor(Math.random() * participatingModels.length)];
    console.log('🎲 [ChatRoom] 随机选择模型:', randomModel.name);

    const modelInfo = {
      model: randomModel,
      schedulingInfo: { reasoning: '随机选择' }
    };

    const response = await sendModelRequest(modelInfo, initialMessages);

    if (response) {
      console.log('✅ [ChatRoom] 随机调度回复成功');
    } else {
      console.log('❌ [ChatRoom] 随机调度回复失败');
    }

    // 🎯 修复：随机调度完成后重置所有相关状态
    setIsConversationActive(false);
    setIsAIDiscussionActive(false);
  };

  // 🚀 修复：重新生成单个AI消息
  const handleRegenerateMessage = useCallback(async (messageToRegenerate) => {
    if (!currentSession || !messageToRegenerate || messageToRegenerate.sender === 'user') return;

    try {
      console.log('🔄 [ChatRoom] 重新生成单个AI消息:', messageToRegenerate.sender);

      // 找到该消息在数组中的位置
      const messageIndex = messages.findIndex(msg =>
        msg.id === messageToRegenerate.id ||
        (msg.timestamp === messageToRegenerate.timestamp && msg.sender === messageToRegenerate.sender)
      );

      if (messageIndex === -1) {
        throw new Error('未找到要重新生成的消息');
      }

      // 🔥 修复：保存原始消息结构用于恢复
      const messagesBeforeTarget = messages.slice(0, messageIndex);
      const messagesAfterTarget = messages.slice(messageIndex + 1);
      const originalMessage = messages[messageIndex];

      console.log('🔄 [ChatRoom] 消息位置信息:', {
        messageIndex,
        totalMessages: messages.length,
        beforeCount: messagesBeforeTarget.length,
        afterCount: messagesAfterTarget.length,
        targetSender: originalMessage.sender
      });

      // 🔥 修复：找到要重新生成的模型
      const targetModel = allModels.find(model => model.name === messageToRegenerate.sender);
      if (!targetModel) {
        throw new Error(`未找到模型: ${messageToRegenerate.sender}`);
      }

      // 🔥 修复：构建用于重新生成的消息上下文（只包含目标消息之前的消息）
      const contextMessages = messagesBeforeTarget;

      console.log('🔄 [ChatRoom] 重新生成上下文消息数量:', contextMessages.length);

      // 🔥 修复：创建自定义的重新生成逻辑，不使用sendModelRequest
      await regenerateSingleMessage(targetModel, contextMessages, messageIndex, messagesAfterTarget);

      console.log('✅ [ChatRoom] 单个消息重新生成完成');

    } catch (error) {
      console.error('❌ [ChatRoom] 重新生成单个消息失败:', error);

      // 🔥 修复：发生错误时恢复原始消息列表
      setMessages(messages);

      // 显示错误提示
      message.error(`重新生成失败: ${error.message}`);
    }
  }, [currentSession, messages, allModels, message]);

  // 🚀 新增：专门用于重新生成单条消息的函数
  const regenerateSingleMessage = async (targetModel, contextMessages, originalIndex, messagesAfter) => {
    console.log('🔄 [ChatRoom] 开始重新生成单条消息');

    // 创建临时消息ID
    const tempMessageId = `temp_regen_${targetModel.id}_${Date.now()}`;

    // 创建临时消息
    const tempMessage = {
      id: tempMessageId,
      sender: targetModel.name,
      content: '',
      thinking: '',
      timestamp: new Date().toISOString(),
      isStreaming: true,
      isRegeneration: true
    };

    // 🔥 关键：在正确的位置插入临时消息
    const messagesBeforeTarget = messages.slice(0, originalIndex);
    const newMessagesWithTemp = [
      ...messagesBeforeTarget,
      tempMessage,
      ...messagesAfter
    ];

    setMessages(newMessagesWithTemp);
    setActiveRequests(prev => new Set([...prev, targetModel.id]));

    try {
      // 🔥 修复：处理Vision内容
      const processedMessages = contextMessages.map(msg => {
        if (msg.sender === 'user' && msg.files && msg.files.length > 0) {
          const imageFiles = msg.files.filter(file => file.type?.startsWith('image/'));

          if (imageFiles.length > 0 && targetModel.supportVision) {
            const content = [
              {
                type: "text",
                text: msg.content
              }
            ];

            imageFiles.forEach(file => {
              content.push({
                type: "image_url",
                image_url: {
                  url: file.base64
                }
              });
            });

            return {
              ...msg,
              content: content,
              originalContent: msg.content,
              hasVisionContent: true
            };
          }
        }
        return msg;
      });

      // 🔥 修复：调用API
      const requestOptions = {
        enableThinking: showThinking && targetModel.supportThinking,
        maxTokens: targetModel.maxTokens || 8192,
        temperature: 0.7,
        stream: true,
        sessionId: currentSession?.id,
        onStreamChunk: (chunk) => {
          // 流式更新消息内容
          setMessages(prev => prev.map(msg =>
            msg.id === tempMessageId
              ? {
                  ...msg,
                  content: chunk.fullContent,
                  thinking: chunk.thinking,
                  isStreaming: !chunk.isComplete
                }
              : msg
          ));
        }
      };

      const response = await apiManagerRef.current.sendChatRequest(
        targetModel,
        processedMessages,
        requestOptions
      );

      if (!response.success) {
        throw new Error(response.error);
      }

      // 🔥 关键：创建最终消息并替换临时消息
      const finalMessage = {
        sender: targetModel.name,
        content: response.data.content,
        thinking: response.data.thinking,
        timestamp: new Date().toISOString(),
        schedulingInfo: {
          reason: `重新生成 ${targetModel.name} 的回复`,
          confidence: 100,
          isRegeneration: true
        }
      };

      // 🔥 关键：替换临时消息为最终消息，保持位置不变
      setMessages(prev => prev.map(msg =>
        msg.id === tempMessageId
          ? { ...finalMessage, id: undefined }
          : msg
      ));

      // 保存到数据管理器
      if (currentSession) {
        // 重新构建完整的消息列表并保存
        const finalMessages = [
          ...messagesBeforeTarget,
          finalMessage,
          ...messagesAfter
        ];
        dataManager.setSessionMessages(currentSession.id, finalMessages);
      }

      console.log('✅ [ChatRoom] 单条消息重新生成成功');

    } catch (error) {
      console.error('❌ [ChatRoom] 重新生成失败:', error);

      // 🔥 修复：恢复原始消息列表
      setMessages(messages);

      throw error;
    } finally {
      setActiveRequests(prev => {
        const newSet = new Set(prev);
        newSet.delete(targetModel.id);
        return newSet;
      });
    }
  };

  // 🚀 消息列表渲染优化
  const memoizedMessages = useMemo(() =>
    messages.map((msg, index) => (
      <MessageItem
        key={msg.id || `msg-${index}`}
        msg={msg}
        index={index}
        isUser={msg.sender === 'user'}
        isSystem={msg.sender === 'system'}
        isStreaming={msg.isStreaming}
        showThinking={showThinking}
        onEditMessage={msg.sender === 'user' ? handleEditMessage : null}
        onDeleteMessage={msg.sender !== 'user' ? handleDeleteMessage : null}
        onRegenerateMessage={msg.sender !== 'user' && msg.sender !== 'system' ? handleRegenerateMessage : null}
      />
    )), [messages, showThinking, handleEditMessage, handleDeleteMessage, handleRegenerateMessage]
  );

  // 发送模型请求（支持流式响应）
  const sendModelRequest = async (modelInfo, currentMessages, abortController = null) => {
    const { model, schedulingInfo, interactionPrompt } = modelInfo;

    console.log(`🔍 [ChatRoom] sendModelRequest 开始，模型: ${model.name}`);
    console.log(`🔍 [ChatRoom] 模型支持Vision: ${model.supportVision ? '是' : '否'}`);
    console.log(`🔍 [ChatRoom] 接收到的消息数量: ${currentMessages.length}`);

    // 🌐 检查是否包含搜索结果
    const searchMessages = currentMessages.filter(msg =>
      msg.type === 'search_result' || msg.type === 'search_context'
    );
    if (searchMessages.length > 0) {
      console.log(`🌐 [ChatRoom] 检测到 ${searchMessages.length} 条搜索相关消息`);
      searchMessages.forEach((msg, index) => {
        console.log(`🌐 [ChatRoom] 搜索消息 ${index + 1}:`, {
          type: msg.type,
          contentLength: msg.content?.length || 0,
          hasSearchData: !!msg.searchData
        });
      });
    } else {
      console.log(`⚠️ [ChatRoom] 未检测到搜索结果消息`);
    }

    // 🚀 新增：检查是否需要查询知识库
    let enhancedMessages = currentMessages;
    let knowledgeReferences = [];

    if (currentSession && knowledgeBaseIntegrationRef.current.isKnowledgeBaseEnabled(currentSession)) {
      console.log('🧠 [ChatRoom] 检测到知识库配置，开始查询相关内容');
      const knowledgeBases = knowledgeBaseIntegrationRef.current.getSessionKnowledgeBases(currentSession);

      try {
        if (knowledgeBases && knowledgeBases.length > 0) {
          const enhancementResult = await knowledgeBaseIntegrationRef.current.enhanceMessagesWithKnowledge(
            currentMessages,
            knowledgeBases
          );

          if (enhancementResult && enhancementResult.messages) {
            enhancedMessages = enhancementResult.messages;
            knowledgeReferences = enhancementResult.knowledgeReferences || [];
            console.log('✅ [ChatRoom] 知识库内容已集成到消息中');
            console.log('📚 [ChatRoom] 知识库引用数量:', knowledgeReferences ? knowledgeReferences.length : 0);
          } else {
            console.warn('⚠️ [ChatRoom] 知识库查询返回了无效结果');
          }
        } else {
          console.warn('⚠️ [ChatRoom] 会话配置了知识库，但知识库ID列表为空');
        }
      } catch (error) {
        console.error('❌ [ChatRoom] 知识库查询失败:', error);
        // 如果知识库查询失败，继续使用原始消息
        enhancedMessages = currentMessages;
        knowledgeReferences = [];
      }
    }
    console.log(`🔍 [ChatRoom] 消息历史概览:`, currentMessages.map((msg, index) => {
      const safeContent = typeof msg.content === 'string'
        ? msg.content.substring(0, 50) + (msg.content.length > 50 ? '...' : '')
        : Array.isArray(msg.content)
          ? (msg.content.find(item => item.type === 'text')?.text || '[Vision消息]').substring(0, 50) + '...'
          : '[非文本内容]';

      return {
        index: index + 1,
        sender: msg.sender,
        contentPreview: safeContent,
        timestamp: msg.timestamp,
        hasFiles: msg.files ? msg.files.length : 0
      };
    }));

    // 🔥 修复：检查是否已被中断
    if (abortController && abortController.signal.aborted) {
      console.log('🛑 [ChatRoom] sendModelRequest 检测到中断信号，取消请求');
      return null;
    }

    // 🔥 新增：处理包含文件的消息，转换为Vision API格式
    const processedMessages = enhancedMessages.map(msg => {
      if (msg.files && msg.files.length > 0) {
        const imageFiles = msg.files.filter(file => file.isImage);

        if (imageFiles.length > 0 && model.supportVision) {
          // 如果模型支持Vision且消息包含图片，转换为Vision格式
          console.log(`🖼️ [ChatRoom] 为支持Vision的模型 ${model.name} 处理 ${imageFiles.length} 张图片`);

          // 构建Vision消息格式
          const content = [
            {
              type: "text",
              text: msg.content || "请分析这些图片："
            }
          ];

          // 添加图片内容
          imageFiles.forEach(file => {
            content.push({
              type: "image_url",
              image_url: {
                url: file.base64
              }
            });
          });

          return {
            ...msg,
            content: content,
            originalContent: msg.content, // 保存原始文本内容
            hasVisionContent: true
          };
        } else if (imageFiles.length > 0 && !model.supportVision) {
          // 如果模型不支持Vision但有图片，添加提示信息
          const fileInfo = msg.files.map(file =>
            `[文件: ${file.name} (${(file.size / 1024).toFixed(1)}KB)]`
          ).join(' ');

          return {
            ...msg,
            content: `${msg.content}\n\n${fileInfo}\n\n注意：当前模型不支持图片识别，仅显示文件信息。`
          };
        }
      }

      return msg;
    });

    setActiveRequests(prev => new Set([...prev, model.id]));

    // 创建临时消息用于流式显示
    const tempMessageId = `temp_${model.id}_${Date.now()}`;
    const tempMessage = {
      id: tempMessageId,
      sender: model.name,
      content: '',
      thinking: '',
      timestamp: new Date().toISOString(),
      schedulingInfo,
      isStreaming: true
    };

    // 立即添加临时消息到界面
    setMessages(prev => [...prev, tempMessage]);

    try {
      const partnerPrompt = selectedPartner?.prompt || null;

      const requestOptions = {
        partnerPrompt,
        interactionPrompt, // 🔥 传递互动提示词
        enableThinking: showThinking && model.supportThinking,
        maxTokens: model.maxTokens, // 🔥 高容量token支持，专注对话质量
        temperature: 0.7,
        stream: true, // 启用流式响应
        abortController: abortController, // 🔥 修复：传递AbortController
        sessionId: currentSession?.id, // 🚀 新增：传递会话ID用于性能统计
        onStreamChunk: (chunk) => {
          // 🔥 修复：在流式更新中检查中断信号
          if (abortController && abortController.signal.aborted) {
            console.log('🛑 [ChatRoom] 流式响应检测到中断信号');
            return;
          }

          // 流式更新消息内容
          setMessages(prev => prev.map(msg =>
            msg.id === tempMessageId
              ? {
                  ...msg,
                  content: chunk.fullContent,
                  thinking: chunk.thinking,
                  isStreaming: !chunk.isComplete
                }
              : msg
          ));
        }
      };

      const response = await apiManagerRef.current.sendChatRequest(
        model,
        processedMessages, // 🔥 修复：使用处理过的消息（包含Vision格式）
        requestOptions
      );

      if (!response.success) {
        // 移除临时消息
        setMessages(prev => prev.filter(msg => msg.id !== tempMessageId));
        throw new Error(response.error);
      }

      // 更新最终消息并保存到数据管理器
      const finalMessage = {
        sender: model.name,
        content: response.data.content,
        thinking: response.data.thinking,
        timestamp: new Date().toISOString(),
        schedulingInfo,
        knowledgeReferences: knowledgeReferences.length > 0 ? knowledgeReferences : undefined // 🚀 新增：知识库引用信息
      };

      // 替换临时消息为最终消息
      setMessages(prev => prev.map(msg =>
        msg.id === tempMessageId
          ? { ...finalMessage, id: undefined } // 移除临时ID
          : msg
      ));

      // 保存到数据管理器
      dataManager.addMessage(currentSession.id, finalMessage);

      // 记录会话级别的模型发言统计
      coordinatorManagerRef.current.recordSessionModelSpeaking(currentSession.id, model.id);

      // 🔥 修复：如果这是第一次AI回复且需要生成标题，生成新标题
      const isFirstAIResponse = currentMessages.filter(msg => msg.sender !== 'user' && msg.sender !== 'system').length === 0;

      if (isFirstAIResponse && shouldGenerateTitle(currentSession)) {
        const lastUserMessage = getLastUserMessage(currentMessages);
        if (lastUserMessage) {
          console.log('📝 [ChatRoom] 检测到第一次AI回复且需要生成标题');
          console.log('📝 [ChatRoom] 当前标题:', currentSession.title);
          // 异步生成标题，不阻塞当前流程
          setTimeout(() => {
            generateSessionTitle(lastUserMessage.content, response.data.content);
          }, 1000);
        }
      }

      return {
        content: response.data.content,
        thinking: response.data.thinking,
        schedulingInfo
      };

    } catch (error) {
      console.error(`❌ [ChatRoom] 模型 ${model.name} 请求失败:`, error);

      // 🔥 修复：检查是否是用户中断导致的错误
      if (abortController && abortController.signal.aborted) {
        console.log(`🛑 [ChatRoom] 模型 ${model.name} 请求被用户中断`);

        // 移除临时消息（被中断的不完整回复）
        setMessages(prev => prev.filter(msg => msg.id !== tempMessageId));

        // 不显示错误消息，因为这是用户主动中断
        return null;
      }

      // 移除临时消息
      setMessages(prev => prev.filter(msg => msg.id !== tempMessageId));

      message.error(`模型 ${model.name} 回复失败: ${error.message}`);
      return null;
    } finally {
      setActiveRequests(prev => {
        const newSet = new Set(prev);
        newSet.delete(model.id);
        return newSet;
      });
    }
  };

  // 渲染消息
  const renderMessage = (msg, index) => {
    const isUser = msg.sender === 'user';
    const isSystem = msg.sender === 'system';
    const isStreaming = msg.isStreaming;

    if (isSystem) {
      // 🎨 科技感系统消息样式
      let systemStyle = {
        background: 'rgba(26, 31, 46, 0.8)',
        color: '#ffffff',
        padding: '12px 20px',
        borderRadius: '20px',
        fontSize: '13px',
        maxWidth: '85%',
        textAlign: 'center',
        border: '1px solid rgba(0, 212, 255, 0.3)',
        boxShadow: '0 4px 20px rgba(0, 212, 255, 0.1)',
        backdropFilter: 'blur(20px)',
        position: 'relative',
        overflow: 'hidden'
      };

      // 🎨 用户打断消息 - 橙色科技感样式
      if (msg.type === 'user_interrupt') {
        systemStyle = {
          ...systemStyle,
          background: 'rgba(255, 165, 0, 0.2)',
          color: '#ffa500',
          border: '1px solid rgba(255, 165, 0, 0.5)',
          boxShadow: '0 4px 20px rgba(255, 165, 0, 0.2)',
          textShadow: '0 0 8px rgba(255, 165, 0, 0.3)'
        };
      }
      // 🎨 讨论结束消息 - 绿色科技感样式
      else if (msg.type === 'discussion_end') {
        systemStyle = {
          ...systemStyle,
          background: 'rgba(82, 196, 26, 0.2)',
          color: '#52c41a',
          border: '1px solid rgba(82, 196, 26, 0.5)',
          boxShadow: '0 4px 20px rgba(82, 196, 26, 0.2)',
          textShadow: '0 0 8px rgba(82, 196, 26, 0.3)'
        };
      }
      // 🎨 错误消息 - 红色科技感样式
      else if (msg.type === 'error' || msg.type === 'failure') {
        systemStyle = {
          ...systemStyle,
          background: 'rgba(255, 107, 107, 0.2)',
          color: '#ff6b6b',
          border: '1px solid rgba(255, 107, 107, 0.5)',
          boxShadow: '0 4px 20px rgba(255, 107, 107, 0.2)',
          textShadow: '0 0 8px rgba(255, 107, 107, 0.3)'
        };
      }
      // 🎨 默认系统消息 - 青色科技感样式
      else {
        systemStyle = {
          ...systemStyle,
          background: 'rgba(0, 212, 255, 0.1)',
          color: '#00d4ff',
          border: '1px solid rgba(0, 212, 255, 0.4)',
          boxShadow: '0 4px 20px rgba(0, 212, 255, 0.2)',
          textShadow: '0 0 8px rgba(0, 212, 255, 0.3)'
        };
      }

      return (
        <div key={msg.id || index} style={{
          display: 'flex',
          justifyContent: 'center',
          margin: '20px 0'
        }}>
          <div style={systemStyle}>
            {/* 🎨 系统消息背景动画 */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%)',
              animation: 'shimmer 4s ease-in-out infinite',
              transform: 'translateX(-100%)',
              pointerEvents: 'none',
              borderRadius: '20px'
            }} />
            <div style={{ position: 'relative', zIndex: 1 }}>
              <MarkdownRenderer content={msg.content} />
            </div>
          </div>
        </div>
      );
    }

    return (
      <div key={msg.id || index} style={{
        display: 'flex',
        justifyContent: isUser ? 'flex-end' : 'flex-start',
        margin: '16px 0',
        alignItems: 'flex-start'
      }}>
        {/* 🎨 AI模型头像 - 科技感设计 */}
        {!isUser && (
          <div style={{
            width: 40,
            height: 40,
            borderRadius: '50%',
            background: 'linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 16,
            flexShrink: 0,
            boxShadow: '0 0 20px rgba(0, 212, 255, 0.4)',
            border: '2px solid rgba(255, 255, 255, 0.1)',
            position: 'relative',
            overflow: 'hidden'
          }}>
            {/* 头像背景动画 */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%)',
              animation: 'shimmer 3s ease-in-out infinite',
              transform: 'translateX(-100%)'
            }} />
            <RobotOutlined style={{
              color: 'white',
              fontSize: 18,
              zIndex: 1,
              filter: 'drop-shadow(0 0 5px rgba(255, 255, 255, 0.3))'
            }} />
          </div>
        )}

        {/* 🎨 用户头像 */}
        {isUser && (
          <div style={{
            width: 36,
            height: 36,
            borderRadius: '50%',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginLeft: 16,
            flexShrink: 0,
            boxShadow: '0 0 15px rgba(102, 126, 234, 0.4)',
            border: '2px solid rgba(255, 255, 255, 0.1)'
          }}>
            <UserOutlined style={{
              color: 'white',
              fontSize: 16,
              filter: 'drop-shadow(0 0 3px rgba(255, 255, 255, 0.3))'
            }} />
          </div>
        )}

        <div
          className="message-bubble tech-border"
          style={{
            maxWidth: '75%',
            background: isUser
              ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
              : 'rgba(26, 31, 46, 0.95)',
            color: '#ffffff',
            padding: '16px 20px',
            borderRadius: isUser ? '20px 20px 6px 20px' : '20px 20px 20px 6px',
            boxShadow: isUser
              ? '0 8px 32px rgba(102, 126, 234, 0.3)'
              : '0 8px 32px rgba(0, 212, 255, 0.2)',
            border: isUser
              ? '1px solid rgba(255, 255, 255, 0.2)'
              : '1px solid rgba(0, 212, 255, 0.3)',
            position: 'relative',
            backdropFilter: 'blur(20px)',
            overflow: 'hidden',
            transition: 'all 0.3s ease'
          }}
        >
          {/* 🎨 消息气泡背景动画效果 */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: isUser
              ? 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%)'
              : 'linear-gradient(45deg, transparent 30%, rgba(0, 212, 255, 0.1) 50%, transparent 70%)',
            animation: 'shimmer 4s ease-in-out infinite',
            transform: 'translateX(-100%)',
            pointerEvents: 'none'
          }} />
          {/* 🎨 AI模型名称和状态 - 科技感设计 */}
          {!isUser && (
            <div style={{
              marginBottom: 12,
              fontSize: 13,
              display: 'flex',
              alignItems: 'center',
              gap: 8,
              position: 'relative',
              zIndex: 1
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: 6,
                padding: '4px 8px',
                background: 'rgba(0, 212, 255, 0.1)',
                borderRadius: '12px',
                border: '1px solid rgba(0, 212, 255, 0.3)'
              }}>
                <RobotOutlined style={{
                  color: '#00d4ff',
                  fontSize: '12px',
                  filter: 'drop-shadow(0 0 3px rgba(0, 212, 255, 0.5))'
                }} />
                <span style={{
                  fontWeight: '600',
                  color: '#00d4ff',
                  textShadow: '0 0 5px rgba(0, 212, 255, 0.3)'
                }}>
                  {msg.sender}
                </span>
              </div>

              {msg.schedulingInfo && !msg.schedulingInfo.isFallback && (
                <div style={{
                  padding: '2px 6px',
                  background: 'linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(138, 43, 226, 0.2) 100%)',
                  borderRadius: '8px',
                  border: '1px solid rgba(0, 212, 255, 0.4)',
                  fontSize: '10px',
                  color: '#00d4ff',
                  fontWeight: '500',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  🧠 智能调度
                </div>
              )}

              {/* 🔥 Vision支持标识 - 科技感设计 */}
              {!isUser && msg.sender && invitedModels.find(m => m.name === msg.sender)?.supportVision && (
                <div style={{
                  padding: '2px 6px',
                  background: 'rgba(138, 43, 226, 0.2)',
                  borderRadius: '8px',
                  border: '1px solid rgba(138, 43, 226, 0.4)',
                  fontSize: '10px',
                  color: '#8a2be2',
                  fontWeight: '500',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '3px'
                }}>
                  <EyeOutlined style={{ fontSize: '10px' }} />
                  VISION
                </div>
              )}

              {isStreaming && (
                <div style={{
                  padding: '2px 6px',
                  background: 'rgba(255, 165, 0, 0.2)',
                  borderRadius: '8px',
                  border: '1px solid rgba(255, 165, 0, 0.4)',
                  fontSize: '10px',
                  color: '#ffa500',
                  fontWeight: '500',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '3px',
                  animation: 'pulse 2s ease-in-out infinite'
                }}>
                  <ThunderboltOutlined spin style={{ fontSize: '10px' }} />
                  STREAMING
                </div>
              )}
            </div>
          )}

          {/* 消息内容 */}
          <div style={{
            lineHeight: '1.6',
            wordBreak: 'break-word'
          }}>
            {isUser ? (
              // 🔥 新增：用户消息支持文件显示
              <div>
                <div style={{ whiteSpace: 'pre-wrap' }}>{msg.content}</div>
                {/* 显示上传的文件 */}
                {msg.files && msg.files.length > 0 && (
                  <div style={{ marginTop: 8 }}>
                    {msg.files.map(file => (
                      <div key={file.id} style={{
                        marginBottom: 8,
                        padding: 8,
                        background: 'rgba(255,255,255,0.1)',
                        borderRadius: 8,
                        border: '1px solid rgba(255,255,255,0.2)'
                      }}>
                        {file.isImage ? (
                          <div>
                            <div style={{
                              fontSize: 12,
                              marginBottom: 4,
                              opacity: 0.8,
                              display: 'flex',
                              alignItems: 'center',
                              gap: 4
                            }}>
                              <FileImageOutlined />
                              {file.name} ({(file.size / 1024).toFixed(1)}KB)
                            </div>
                            <Image
                              src={file.base64}
                              alt={file.name}
                              style={{
                                maxWidth: 200,
                                maxHeight: 200,
                                borderRadius: 4
                              }}
                              preview={{
                                mask: <EyeOutlined style={{ color: 'white' }} />
                              }}
                            />
                          </div>
                        ) : (
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8
                          }}>
                            <FileTextOutlined />
                            <div>
                              <div style={{ fontSize: 12, fontWeight: 'bold' }}>
                                {file.name}
                              </div>
                              <div style={{ fontSize: 10, opacity: 0.8 }}>
                                {(file.size / 1024).toFixed(1)}KB
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              // 🔥 优化：AI消息使用增强渲染器，支持思考标签和流式打字机效果
              <div style={{ position: 'relative' }}>
                <EnhancedMessageRenderer
                  content={msg.content || (isStreaming ? '正在思考...' : '')}
                  showThinking={showThinking}
                  defaultThinkingExpanded={false}
                  className="ai-message-content"
                />
                {/* 🔥 新增：流式响应时显示打字机光标 */}
                {isStreaming && msg.content && (
                  <span style={{
                    display: 'inline-block',
                    width: '2px',
                    height: '1.2em',
                    backgroundColor: '#1890ff',
                    marginLeft: '2px',
                    animation: 'blink 1s infinite',
                    verticalAlign: 'text-bottom'
                  }} />
                )}
              </div>
            )}
          </div>

          {/* 🎨 思考过程 - 科技感设计 */}
          {msg.thinking && showThinking && (
            <div style={{
              marginTop: 16,
              padding: '16px 20px',
              background: 'rgba(26, 31, 46, 0.8)',
              borderRadius: '12px',
              fontSize: '13px',
              border: '1px solid rgba(138, 43, 226, 0.3)',
              boxShadow: '0 4px 20px rgba(138, 43, 226, 0.1)',
              backdropFilter: 'blur(20px)',
              position: 'relative',
              overflow: 'hidden'
            }}>
              {/* 思考过程背景装饰 */}
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'linear-gradient(45deg, transparent 30%, rgba(138, 43, 226, 0.05) 50%, transparent 70%)',
                animation: 'shimmer 4s ease-in-out infinite',
                transform: 'translateX(-100%)',
                pointerEvents: 'none'
              }} />

              <div style={{
                marginBottom: 12,
                fontWeight: '600',
                color: '#8a2be2',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                position: 'relative',
                zIndex: 1
              }}>
                <ThunderboltOutlined style={{
                  fontSize: '14px',
                  filter: 'drop-shadow(0 0 5px rgba(138, 43, 226, 0.5))'
                }} />
                <span style={{
                  textShadow: '0 0 8px rgba(138, 43, 226, 0.3)',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px',
                  fontSize: '12px'
                }}>
                  思考过程
                </span>
              </div>
              <div style={{ position: 'relative', zIndex: 1 }}>
                <MarkdownRenderer content={msg.thinking} className="thinking-content" />
              </div>
            </div>
          )}

          {/* 🎨 调度信息 - 科技感设计 */}
          {msg.schedulingInfo && msg.schedulingInfo.reasoning && (
            <div style={{
              marginTop: 12,
              fontSize: 12,
              color: 'rgba(0, 212, 255, 0.8)',
              fontStyle: 'italic',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              padding: '6px 12px',
              background: 'rgba(0, 212, 255, 0.05)',
              borderRadius: '8px',
              border: '1px solid rgba(0, 212, 255, 0.2)',
              position: 'relative',
              zIndex: 1
            }}>
              <EyeOutlined style={{
                fontSize: '12px',
                filter: 'drop-shadow(0 0 3px rgba(0, 212, 255, 0.5))'
              }} />
              <span style={{
                textShadow: '0 0 5px rgba(0, 212, 255, 0.3)'
              }}>
                {msg.schedulingInfo.reasoning}
              </span>
            </div>
          )}

          {/* 🎨 时间戳 - 科技感设计 */}
          <div style={{
            marginTop: 12,
            fontSize: 11,
            color: 'rgba(136, 146, 176, 0.7)',
            textAlign: isUser ? 'right' : 'left',
            fontFamily: 'Monaco, Menlo, monospace',
            letterSpacing: '0.5px',
            position: 'relative',
            zIndex: 1
          }}>
            {new Date(msg.timestamp).toLocaleTimeString()}
          </div>
        </div>

        {/* 用户头像 */}
        {isUser && (
          <div style={{
            width: 32,
            height: 32,
            borderRadius: '50%',
            background: '#1890ff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginLeft: 12,
            flexShrink: 0
          }}>
            <span style={{ color: 'white', fontSize: 14, fontWeight: 'bold' }}>
              您
            </span>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <Layout style={{ height: '100%', background: 'transparent' }}>
        {/* 🎨 左侧边栏 - 科技感会话列表 */}
      <Sider
        width={300}
        style={{
          background: 'rgba(26, 31, 46, 0.95)',
          backdropFilter: 'blur(20px)',
          borderRight: '1px solid rgba(0, 212, 255, 0.2)',
          boxShadow: '4px 0 20px rgba(0, 212, 255, 0.1)'
        }}
      >
        <div style={{
          padding: 20,
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative'
        }}>
          {/* 侧边栏背景装饰 */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 10% 20%, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 90% 80%, rgba(138, 43, 226, 0.05) 0%, transparent 50%)
            `,
            pointerEvents: 'none'
          }} />
          {/* 🎨 会话管理标题 - 科技感设计 */}
          <div style={{
            marginBottom: 20,
            paddingBottom: 16,
            borderBottom: '1px solid rgba(0, 212, 255, 0.3)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            position: 'relative',
            zIndex: 1
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <div style={{
                width: '4px',
                height: '20px',
                background: 'linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%)',
                borderRadius: '2px',
                boxShadow: '0 0 10px rgba(0, 212, 255, 0.5)'
              }} />
              <Title
                level={4}
                style={{
                  margin: 0,
                  color: '#ffffff',
                  textShadow: '0 0 10px rgba(0, 212, 255, 0.3)',
                  fontWeight: '600'
                }}
              >
                会话列表
              </Title>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateSessionModal}
              size="small"
              style={{
                background: 'linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%)',
                border: 'none',
                borderRadius: '8px',
                boxShadow: '0 4px 15px rgba(0, 212, 255, 0.3)',
                fontWeight: '500'
              }}
            >
              新建
            </Button>
          </div>
          
          {/* 会话列表 */}
          <div style={{ flex: 1, overflowY: 'auto' }}>
            {sessions.length === 0 ? (
              <div style={{
                textAlign: 'center',
                color: 'rgba(136, 146, 176, 0.8)',
                marginTop: 60,
                fontSize: '14px',
                position: 'relative',
                zIndex: 1
              }}>
                <div style={{
                  fontSize: '48px',
                  marginBottom: '16px',
                  opacity: 0.3
                }}>
                  🤖
                </div>
                <div style={{
                  color: '#ffffff',
                  fontWeight: '500',
                  marginBottom: '8px'
                }}>
                  暂无会话
                </div>
                <div style={{
                  fontSize: '12px',
                  color: 'rgba(0, 212, 255, 0.8)',
                  lineHeight: '1.5'
                }}>
                  点击"新建"创建第一个会话<br/>
                  开始您的AI协作之旅
                </div>
              </div>
            ) : (
              sessions
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)) // 按创建时间倒序排列
                .map(session => (
                <div
                  key={session.id}
                  onClick={() => loadSession(session.id)}
                  className="tech-border"
                  style={{
                    padding: 16,
                    cursor: 'pointer',
                    borderRadius: 12,
                    marginBottom: 12,
                    background: currentSession?.id === session.id
                      ? 'linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(138, 43, 226, 0.2) 100%)'
                      : 'rgba(26, 31, 46, 0.6)',
                    border: currentSession?.id === session.id
                      ? '1px solid rgba(0, 212, 255, 0.5)'
                      : '1px solid rgba(0, 212, 255, 0.2)',
                    boxShadow: currentSession?.id === session.id
                      ? '0 0 20px rgba(0, 212, 255, 0.3)'
                      : '0 4px 15px rgba(0, 212, 255, 0.1)',
                    transition: 'all 0.3s ease',
                    position: 'relative',
                    backdropFilter: 'blur(10px)',
                    overflow: 'hidden'
                  }}
                  onMouseEnter={(e) => {
                    if (currentSession?.id !== session.id) {
                      e.target.style.background = 'rgba(0, 212, 255, 0.1)';
                      e.target.style.borderColor = 'rgba(0, 212, 255, 0.4)';
                      e.target.style.boxShadow = '0 6px 20px rgba(0, 212, 255, 0.2)';
                      e.target.style.transform = 'translateX(4px)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (currentSession?.id !== session.id) {
                      e.target.style.background = 'rgba(26, 31, 46, 0.6)';
                      e.target.style.borderColor = 'rgba(0, 212, 255, 0.2)';
                      e.target.style.boxShadow = '0 4px 15px rgba(0, 212, 255, 0.1)';
                      e.target.style.transform = 'translateX(0)';
                    }
                  }}
                >
                  {/* 🎨 会话项背景动画 */}
                  {currentSession?.id === session.id && (
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: 'linear-gradient(45deg, transparent 30%, rgba(0, 212, 255, 0.1) 50%, transparent 70%)',
                      animation: 'shimmer 4s ease-in-out infinite',
                      transform: 'translateX(-100%)',
                      pointerEvents: 'none'
                    }} />
                  )}

                  <div style={{
                    fontWeight: currentSession?.id === session.id ? '600' : '500',
                    fontSize: 15,
                    marginBottom: 8,
                    paddingRight: 32,
                    color: currentSession?.id === session.id ? '#00d4ff' : '#ffffff',
                    textShadow: currentSession?.id === session.id
                      ? '0 0 10px rgba(0, 212, 255, 0.5)'
                      : 'none',
                    position: 'relative',
                    zIndex: 1,
                    lineHeight: '1.4'
                  }}>
                    {session.title}
                  </div>
                  <div style={{
                    fontSize: 12,
                    color: 'rgba(136, 146, 176, 0.8)',
                    marginBottom: 6,
                    position: 'relative',
                    zIndex: 1
                  }}>
                    {new Date(session.createdAt).toLocaleString()}
                  </div>
                  {session.invitedModels && session.invitedModels.length > 0 && (
                    <div style={{
                      fontSize: 11,
                      color: 'rgba(0, 212, 255, 0.8)',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      position: 'relative',
                      zIndex: 1
                    }}>
                      <RobotOutlined style={{ fontSize: '10px' }} />
                      {/* 🔥 修复：显示实时的模型数量 */}
                      {currentSession && currentSession.id === session.id
                        ? `${invitedModels.length} 个模型`
                        : `${session.invitedModels.length} 个模型`
                      }
                    </div>
                  )}
                  <Button
                    type="text"
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteSession(session.id);
                    }}
                    style={{
                      position: 'absolute',
                      right: 8,
                      top: 8,
                      width: 24,
                      height: 24,
                      fontSize: 12,
                      color: 'rgba(255, 107, 107, 0.7)',
                      background: 'rgba(255, 107, 107, 0.1)',
                      border: '1px solid rgba(255, 107, 107, 0.3)',
                      borderRadius: '6px',
                      transition: 'all 0.3s ease',
                      zIndex: 2
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.color = '#ff6b6b';
                      e.target.style.background = 'rgba(255, 107, 107, 0.2)';
                      e.target.style.borderColor = 'rgba(255, 107, 107, 0.5)';
                      e.target.style.boxShadow = '0 0 10px rgba(255, 107, 107, 0.4)';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.color = 'rgba(255, 107, 107, 0.7)';
                      e.target.style.background = 'rgba(255, 107, 107, 0.1)';
                      e.target.style.borderColor = 'rgba(255, 107, 107, 0.3)';
                      e.target.style.boxShadow = 'none';
                    }}
                  />
                </div>
              ))
            )}
          </div>
        </div>
      </Sider>

      {/* 🎨 主聊天区域 - 科技感设计 */}
      <Content style={{
        display: 'flex',
        flexDirection: 'column',
        background: 'transparent'
      }}>
        {/* 🎨 科技感状态栏 */}
        {currentSession && (
          <div style={{
            padding: '12px 20px',
            background: 'rgba(26, 31, 46, 0.95)',
            backdropFilter: 'blur(20px)',
            borderBottom: '1px solid rgba(0, 212, 255, 0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 2px 15px rgba(0, 212, 255, 0.1)',
            position: 'relative',
            overflow: 'hidden'
          }}>
            {/* 状态栏背景动画 */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(90deg, transparent 0%, rgba(0, 212, 255, 0.03) 50%, transparent 100%)',
              animation: 'pulse 4s ease-in-out infinite',
              pointerEvents: 'none'
            }} />
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: 20,
              position: 'relative',
              zIndex: 1
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <div style={{
                  width: '3px',
                  height: '18px',
                  background: 'linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%)',
                  borderRadius: '2px',
                  boxShadow: '0 0 8px rgba(0, 212, 255, 0.5)'
                }} />
                <Title level={5} style={{
                  margin: 0,
                  color: '#ffffff',
                  textShadow: '0 0 8px rgba(0, 212, 255, 0.3)',
                  fontWeight: '600'
                }}>
                  {currentSession.title}
                </Title>
              </div>
              <div style={{
                fontSize: '13px',
                color: 'rgba(136, 146, 176, 0.9)',
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                flexWrap: 'wrap'
              }}>
                <span style={{
                  color: '#00d4ff',
                  fontWeight: '500'
                }}>
                  🤖 {invitedModels.length} 个模型
                </span>
                <span>|</span>
                <span>
                  调度模式：
                  {schedulingMode === 'intelligent' && <span style={{ color: '#8a2be2' }}>🧠 智能调度</span>}
                  {schedulingMode === 'roundRobin' && <span style={{ color: '#00d4ff' }}>🔄 轮流调度</span>}
                  {schedulingMode === 'random' && <span style={{ color: '#ffa500' }}>🎲 随机调度</span>}
                </span>

                {/* 🌐 网络搜索状态显示 */}
                <span>|</span>
                <span style={{
                  color: enableWebSearch ? '#52c41a' : '#8c8c8c',
                  fontWeight: '500'
                }}>
                  🌐 网络搜索{enableWebSearch ? '已启用' : '已关闭'}
                </span>

                {isAIDiscussionActive && (
                  <>
                    <span>|</span>
                    <span style={{
                      color: '#00d4ff',
                      fontWeight: '600',
                      animation: 'pulse 2s ease-in-out infinite'
                    }}>
                      🤖 {currentSchedulingStatus || `AI协作讨论中 (${discussionRounds}/${maxRounds})`}
                    </span>
                  </>
                )}
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
              {/* 🚀 一键操作工具栏 */}
              <QuickActionToolbar
                messages={messages}
                onClearConversation={handleClearConversation}
                onDeleteMessage={handleDeleteMessage}
                onExportConversation={handleExportConversation}
                style={{
                  padding: '4px 8px',
                  background: 'rgba(0, 0, 0, 0.2)',
                  border: '1px solid rgba(0, 212, 255, 0.15)'
                }}
              />

              <div style={{ display: 'flex', gap: 8 }}>
                {isAIDiscussionActive && (
                  <Button
                    danger
                    icon={<StopOutlined />}
                    onClick={stopAIDiscussion}
                    size="small"
                  >
                    停止讨论
                  </Button>
                )}

                <Button
                  icon={<SettingOutlined />}
                  onClick={showSettingsModal}
                  size="small"
                >
                  设置
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 🎨 聊天消息区域 - 科技感设计 */}
        <div style={{
          flex: 1,
          padding: 20,
          overflowY: 'auto',
          background: 'transparent',
          position: 'relative'
        }}>
          {/* 聊天区域背景装饰 */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(138, 43, 226, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 40% 60%, rgba(0, 212, 255, 0.02) 0%, transparent 50%)
            `,
            pointerEvents: 'none'
          }} />

          <div style={{ position: 'relative', zIndex: 1 }}>
            {!currentSession ? (
              <div style={{
                textAlign: 'center',
                marginTop: 120,
                fontSize: '16px'
              }}>
                <div style={{
                  fontSize: '64px',
                  marginBottom: '24px',
                  opacity: 0.3
                }}>
                  🚀
                </div>
                <div style={{
                  color: '#ffffff',
                  fontWeight: '600',
                  marginBottom: '12px',
                  textShadow: '0 0 10px rgba(0, 212, 255, 0.3)'
                }}>
                  请选择或创建一个会话开始聊天
                </div>
                <div style={{
                  color: 'rgba(136, 146, 176, 0.8)',
                  fontSize: '14px'
                }}>
                  开启您的AI协作之旅
                </div>
              </div>
            ) : messages.length === 0 ? (
              <div style={{
                textAlign: 'center',
                marginTop: 120,
                fontSize: '14px'
              }}>
                <div style={{
                  fontSize: '48px',
                  marginBottom: '20px',
                  opacity: 0.4
                }}>
                  💬
                </div>
                <div style={{
                  color: '#ffffff',
                  fontWeight: '500',
                  marginBottom: '8px'
                }}>
                  开始新的对话
                </div>
                <div style={{
                  color: 'rgba(0, 212, 255, 0.8)',
                  fontSize: '13px'
                }}>
                  开始您的AI协作对话吧！
                </div>
                <div style={{
                  fontSize: '12px',
                  marginTop: 8,
                  color: 'rgba(136, 146, 176, 0.8)'
                }}>
                  当前已邀请 {invitedModels.length} 个模型参与对话
                </div>
              </div>
            ) : (
              <div style={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                position: 'relative' // 🚀 为导航按钮提供定位上下文
              }}>
                {/* 🚀 消息渲染：使用直接渲染（已禁用虚拟化） */}
                <div
                  ref={messagesContainerRef}
                  style={{ flex: 1, display: 'flex', flexDirection: 'column' }}
                >
                  {/* 🚀 始终使用直接渲染，已禁用虚拟化和高性能模式 */}
                  <div
                    style={{ flex: 1, overflowY: 'auto', position: 'relative' }}
                    data-scroll-container="true"
                    onScroll={handleScroll}
                  >
                    {memoizedMessages}

                    {/* 🔥 智能调度状态指示器 */}
                    {renderSchedulingIndicator()}

                    <div ref={messagesEndRef} />
                    
                    {/* 🎯 回到底部按钮 */}
                    {showScrollToBottom && (
                      <div
                        style={{
                          position: 'absolute',
                          bottom: '20px',
                          right: '20px',
                          zIndex: 1000
                        }}
                      >
                        <Tooltip title="回到底部">
                          <Button
                            type="primary"
                            shape="circle"
                            icon={<ArrowDownOutlined />}
                            onClick={forceScrollToBottom}
                            style={{
                              background: 'rgba(0, 212, 255, 0.9)',
                              border: 'none',
                              boxShadow: '0 4px 12px rgba(0, 212, 255, 0.3)',
                              backdropFilter: 'blur(8px)'
                            }}
                          />
                        </Tooltip>
                      </div>
                    )}
                  </div>
                </div>


              </div>
            )}
          </div>
        </div>

        {/* 🎨 科技感输入区域容器 */}
        {currentSession && (
          <div
            style={{
              padding: 20,
              borderTop: '1px solid rgba(0, 212, 255, 0.3)',
              background: 'rgba(26, 31, 46, 0.95)',
              backdropFilter: 'blur(20px)',
              boxShadow: '0 -4px 20px rgba(0, 212, 255, 0.1)',
              position: 'relative'
            }}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onPaste={handlePaste}
          >
            {/* 输入区域背景装饰 */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(90deg, transparent 0%, rgba(0, 212, 255, 0.03) 50%, transparent 100%)',
              animation: 'pulse 4s ease-in-out infinite',
              pointerEvents: 'none'
            }} />
            {/* 🎨 科技感模型选择器 */}
            {selectedModelForInput && (
              <div style={{
                marginBottom: 12,
                position: 'relative',
                zIndex: 1
              }}>
                <div style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '6px 12px',
                  background: 'rgba(0, 212, 255, 0.1)',
                  border: '1px solid rgba(0, 212, 255, 0.3)',
                  borderRadius: '12px',
                  color: '#00d4ff',
                  fontSize: '12px',
                  fontWeight: '500'
                }}>
                  <RobotOutlined style={{ fontSize: '12px' }} />
                  <span>指定模型: {selectedModelForInput.name}</span>
                  <CloseOutlined
                    style={{
                      fontSize: '10px',
                      cursor: 'pointer',
                      opacity: 0.7,
                      transition: 'opacity 0.3s ease'
                    }}
                    onClick={() => setSelectedModelForInput(null)}
                    onMouseEnter={(e) => e.target.style.opacity = '1'}
                    onMouseLeave={(e) => e.target.style.opacity = '0.7'}
                  />
                </div>
              </div>
            )}

            {/* 🎨 科技感已上传文件预览 */}
            {uploadedFiles.length > 0 && (
              <div style={{
                marginBottom: 16,
                padding: 16,
                background: 'rgba(26, 31, 46, 0.8)',
                borderRadius: 12,
                border: '1px solid rgba(0, 212, 255, 0.3)',
                boxShadow: '0 4px 15px rgba(0, 212, 255, 0.1)',
                backdropFilter: 'blur(20px)',
                position: 'relative',
                zIndex: 1
              }}>
                <div style={{
                  fontSize: 13,
                  color: '#00d4ff',
                  marginBottom: 12,
                  fontWeight: '600',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  textShadow: '0 0 5px rgba(0, 212, 255, 0.3)'
                }}>
                  <PaperClipOutlined style={{ fontSize: '12px' }} />
                  已上传文件 ({uploadedFiles.length})
                </div>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: 12 }}>
                  {uploadedFiles.map(file => (
                    <div key={file.id} style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                      padding: '8px 12px',
                      background: 'rgba(0, 212, 255, 0.1)',
                      borderRadius: 8,
                      border: '1px solid rgba(0, 212, 255, 0.3)',
                      fontSize: 12,
                      color: '#ffffff',
                      transition: 'all 0.3s ease'
                    }}>
                      {file.isImage ?
                        <FileImageOutlined style={{ color: '#8a2be2', fontSize: '14px' }} /> :
                        <FileTextOutlined style={{ color: '#00d4ff', fontSize: '14px' }} />
                      }
                      <span style={{ fontWeight: '500' }}>{file.name}</span>
                      <span style={{
                        color: 'rgba(136, 146, 176, 0.8)',
                        fontSize: '11px'
                      }}>
                        ({(file.size / 1024).toFixed(1)}KB)
                      </span>
                      <CloseOutlined
                        onClick={() => removeUploadedFile(file.id)}
                        style={{
                          fontSize: '12px',
                          color: 'rgba(255, 107, 107, 0.7)',
                          cursor: 'pointer',
                          padding: '2px',
                          borderRadius: '4px',
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.color = '#ff6b6b';
                          e.target.style.background = 'rgba(255, 107, 107, 0.1)';
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.color = 'rgba(255, 107, 107, 0.7)';
                          e.target.style.background = 'transparent';
                        }}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 🎨 科技感输入区域 */}
            <div style={{
              display: 'flex',
              gap: 12,
              alignItems: 'flex-end',
              background: isDragOver
                ? 'rgba(0, 212, 255, 0.1)'
                : 'rgba(26, 31, 46, 0.8)',
              borderRadius: 16,
              padding: 16,
              border: isDragOver
                ? '2px dashed #00d4ff'
                : '1px solid rgba(0, 212, 255, 0.3)',
              transition: 'all 0.3s ease',
              backdropFilter: 'blur(20px)',
              boxShadow: '0 8px 32px rgba(0, 212, 255, 0.1)',
              position: 'relative',
              overflow: 'hidden'
            }}>
              {/* 🎨 输入区域背景动画 */}
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'linear-gradient(90deg, transparent 0%, rgba(0, 212, 255, 0.05) 50%, transparent 100%)',
                animation: 'pulse 4s ease-in-out infinite',
                pointerEvents: 'none'
              }} />
              {/* 文件上传按钮 */}
              <Upload
                beforeUpload={handleFileUpload}
                showUploadList={false}
                multiple
                accept="image/*,.txt,.md,.json,.csv,.pdf,.html,.css,.js"
              >
                <Tooltip title="上传文件 (支持拖拽和Ctrl+V粘贴)">
                  <Button
                    icon={<PaperClipOutlined />}
                    style={{
                      height: 44,
                      width: 44,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: 'rgba(0, 212, 255, 0.1)',
                      border: '1px solid rgba(0, 212, 255, 0.3)',
                      borderRadius: '12px',
                      color: '#00d4ff',
                      transition: 'all 0.3s ease',
                      zIndex: 1
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.background = 'rgba(0, 212, 255, 0.2)';
                      e.target.style.boxShadow = '0 0 15px rgba(0, 212, 255, 0.4)';
                      e.target.style.transform = 'translateY(-2px)';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.background = 'rgba(0, 212, 255, 0.1)';
                      e.target.style.boxShadow = 'none';
                      e.target.style.transform = 'translateY(0)';
                    }}
                  />
                </Tooltip>
              </Upload>

              {/* 🚀 智能剪贴板管理器切换按钮 */}
              <Tooltip title={showClipboardManager ? "隐藏智能剪贴板" : "显示智能剪贴板"}>
                <Button
                  icon={<CopyOutlined />}
                  onClick={() => setShowClipboardManager(!showClipboardManager)}
                  style={{
                    height: 44,
                    width: 44,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: showClipboardManager ? 'rgba(82, 196, 26, 0.2)' : 'rgba(82, 196, 26, 0.1)',
                    border: showClipboardManager ? '1px solid rgba(82, 196, 26, 0.5)' : '1px solid rgba(82, 196, 26, 0.3)',
                    borderRadius: '12px',
                    color: '#52c41a',
                    transition: 'all 0.3s ease',
                    zIndex: 1,
                    boxShadow: showClipboardManager ? '0 0 10px rgba(82, 196, 26, 0.3)' : 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = 'rgba(82, 196, 26, 0.3)';
                    e.target.style.boxShadow = '0 0 15px rgba(82, 196, 26, 0.4)';
                    e.target.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = showClipboardManager ? 'rgba(82, 196, 26, 0.2)' : 'rgba(82, 196, 26, 0.1)';
                    e.target.style.boxShadow = showClipboardManager ? '0 0 10px rgba(82, 196, 26, 0.3)' : 'none';
                    e.target.style.transform = 'translateY(0)';
                  }}
                />
              </Tooltip>

              {/* 🎨 科技感输入框 */}
              <Input.TextArea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={
                  isDragOver
                    ? "🚀 松开鼠标上传文件..."
                    : isAIDiscussionActive
                      ? "🤖 AI模型正在协作讨论中，输入消息可打断讨论..."
                      : selectedModelForInput
                        ? `💬 向 ${selectedModelForInput.name} 发送消息...`
                        : "💭 输入消息... (输入 / 可选择特定模型)"
                }
                autoSize={{ minRows: 1, maxRows: 4 }}
                // 🎯 输入法兼容的回车键处理
                onPressEnter={(e) => {
                  // 🎯 输入法状态检测：如果正在使用输入法，不处理回车键
                  if (isComposing) {
                    console.log('🎯 [输入法] 输入法激活中，忽略回车键发送');
                    return;
                  }

                  // 🎯 Shift+Enter 换行功能保持不变
                  if (e.shiftKey) {
                    console.log('🎯 [输入法] Shift+Enter 换行');
                    return;
                  }

                  // 🎯 正常的回车发送消息
                  console.log('🎯 [输入法] 回车发送消息');
                  e.preventDefault();
                  sendMessage();
                }}
                // 🎯 输入法状态监听 - 修复macOS中文输入法冲突
                onCompositionStart={handleCompositionStart}
                onCompositionEnd={handleCompositionEnd}
                // 🎯 额外的键盘事件处理（备用方案）
                onKeyDown={(e) => {
                  // 记录按键事件用于调试
                  if (e.key === 'Enter') {
                    console.log('🎯 [输入法] KeyDown Enter事件 - isComposing:', isComposing, 'shiftKey:', e.shiftKey);
                  }
                }}
                style={{
                  flex: 1,
                  resize: 'none',
                  background: 'rgba(10, 14, 26, 0.8)',
                  border: '1px solid rgba(0, 212, 255, 0.3)',
                  borderRadius: '12px',
                  color: '#ffffff',
                  fontSize: '14px',
                  padding: '12px 16px',
                  transition: 'all 0.3s ease',
                  zIndex: 1
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#00d4ff';
                  e.target.style.boxShadow = '0 0 15px rgba(0, 212, 255, 0.3)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = 'rgba(0, 212, 255, 0.3)';
                  e.target.style.boxShadow = 'none';
                }}
              />

              {/* 模型选择下拉框 */}
              <Select
                placeholder="选择模型"
                value={selectedModelForInput?.id}
                onChange={(value) => {
                  const model = invitedModels.find(m => m.id === value);
                  setSelectedModelForInput(model);
                }}
                style={{
                  width: 120,
                  height: 40
                }}
                allowClear
              >
                {invitedModels.map(model => (
                  <Select.Option key={model.id} value={model.id}>
                    {model.name}
                  </Select.Option>
                ))}
              </Select>

              {/* 🌐 网络搜索控制按钮 */}
              <Button
                icon={<GlobalOutlined />}
                onClick={() => setEnableWebSearch(!enableWebSearch)}
                style={{
                  height: 40,
                  background: enableWebSearch
                    ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)'
                    : 'linear-gradient(135deg, #8c8c8c 0%, #595959 100%)',
                  border: 'none',
                  borderRadius: '8px',
                  color: '#ffffff',
                  boxShadow: enableWebSearch
                    ? '0 2px 8px rgba(82, 196, 26, 0.3)'
                    : '0 2px 8px rgba(140, 140, 140, 0.3)',
                  transition: 'all 0.3s ease'
                }}
                title={enableWebSearch
                  ? `网络搜索已启用 (点击关闭，快捷键: ${getWebSearchShortcut()})`
                  : `网络搜索已关闭 (点击启用，快捷键: ${getWebSearchShortcut()})`}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'translateY(-1px)';
                  e.target.style.boxShadow = enableWebSearch
                    ? '0 4px 12px rgba(82, 196, 26, 0.4)'
                    : '0 4px 12px rgba(140, 140, 140, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = enableWebSearch
                    ? '0 2px 8px rgba(82, 196, 26, 0.3)'
                    : '0 2px 8px rgba(140, 140, 140, 0.3)';
                }}
              />

              {/* 🎨 科技感发送/停止按钮 */}
              <Button
                type="primary"
                icon={isConversationActive ? <StopOutlined /> : <SendOutlined />}
                onClick={isConversationActive ? stopAIDiscussion : sendMessage}
                disabled={!isConversationActive && !inputValue.trim() && uploadedFiles.length === 0}
                style={{
                  height: 44,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minWidth: 100,
                  background: isConversationActive
                    ? 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)'
                    : 'linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%)',
                  border: 'none',
                  borderRadius: '12px',
                  fontWeight: '600',
                  fontSize: '14px',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px',
                  boxShadow: isConversationActive
                    ? '0 4px 20px rgba(255, 107, 107, 0.4)'
                    : '0 4px 20px rgba(0, 212, 255, 0.4)',
                  transition: 'all 0.3s ease',
                  zIndex: 1,
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  if (!e.target.disabled) {
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.boxShadow = isConversationActive
                      ? '0 6px 25px rgba(255, 107, 107, 0.6)'
                      : '0 6px 25px rgba(0, 212, 255, 0.6)';
                  }
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = isConversationActive
                    ? '0 4px 20px rgba(255, 107, 107, 0.4)'
                    : '0 4px 20px rgba(0, 212, 255, 0.4)';
                }}
              >
                {/* 按钮背景动画 */}
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%)',
                  animation: 'shimmer 3s ease-in-out infinite',
                  transform: 'translateX(-100%)',
                  pointerEvents: 'none'
                }} />
                <span style={{ zIndex: 1, position: 'relative' }}>
                  {isConversationActive
                    ? (isAIDiscussionActive ? '⚡ 停止讨论' : '🛑 停止生成')
                    : '🚀 发送'
                  }
                </span>
              </Button>
            </div>

            {/* 🎨 科技感使用提示 */}
            <div style={{
              marginTop: 12,
              fontSize: '12px',
              textAlign: 'center',
              position: 'relative',
              zIndex: 1
            }}>
              {isConversationActive ? (
                <div style={{
                  color: '#00d4ff',
                  fontWeight: '500',
                  textShadow: '0 0 5px rgba(0, 212, 255, 0.3)',
                  animation: 'pulse 2s ease-in-out infinite'
                }}>
                  {isAIDiscussionActive
                    ? `🤖 ${currentSchedulingStatus || `AI模型正在协作讨论中 (${discussionRounds}/${maxRounds})`}`
                    : '🤖 AI模型正在生成回复...'
                  }
                  <br />
                  <span style={{
                    color: 'rgba(136, 146, 176, 0.8)',
                    fontSize: '11px',
                    animation: 'none'
                  }}>
                    输入消息可随时打断讨论
                  </span>
                </div>
              ) : (
                <div style={{
                  color: 'rgba(136, 146, 176, 0.8)',
                  lineHeight: '1.4'
                }}>
                  <span style={{ color: '#8a2be2' }}>💡</span> 提示：输入
                  <span style={{
                    color: '#00d4ff',
                    fontWeight: '500',
                    margin: '0 4px'
                  }}>
                    "/模型名 消息内容"
                  </span>
                  可快速指定模型
                  <br />
                  <span style={{ fontSize: '11px' }}>
                    当前调度模式：
                    {schedulingMode === 'intelligent' && <span style={{ color: '#8a2be2' }}>🧠 智能调度</span>}
                    {schedulingMode === 'roundRobin' && <span style={{ color: '#00d4ff' }}>🔄 轮流调度</span>}
                    {schedulingMode === 'random' && <span style={{ color: '#ffa500' }}>🎲 随机调度</span>}
                    {/* 🎯 输入法状态指示器（开发调试用） */}
                    {isComposing && (
                      <span style={{
                        color: '#ffa500',
                        marginLeft: '8px',
                        fontWeight: '500'
                      }}>
                        | 🎌 输入法激活
                      </span>
                    )}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </Content>
    </Layout>

    {/* 新建会话弹窗 */}
    <Modal
      title="新建会话"
      open={createSessionModalVisible}
      onOk={createNewSession}
      onCancel={() => setCreateSessionModalVisible(false)}
      okText="创建"
      cancelText="取消"
      width={600}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text strong>会话名称：</Text>
          <Input
            value={newSessionName}
            onChange={(e) => setNewSessionName(e.target.value)}
            placeholder="请输入会话名称"
            style={{ marginTop: 8 }}
          />
        </div>

        <div>
          <Text strong>选择模型：</Text>
          <div style={{
            marginTop: 8,
            border: '1px solid #d9d9d9',
            borderRadius: 6,
            padding: 12,
            maxHeight: 200,
            overflowY: 'auto'
          }}>
            <Checkbox.Group
              value={selectedModelsForNewSession}
              onChange={setSelectedModelsForNewSession}
              style={{ width: '100%' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                {allModels
                  .filter(model => model.type !== 'embedding') // 🚀 修复：过滤掉向量模型，只显示聊天模型
                  .map(model => (
                    <Checkbox key={model.id} value={model.id}>
                      <Space>
                        <RobotOutlined />
                        <span>{model.name}</span>
                        {model.supportThinking && <Tag size="small" color="blue">Thinking</Tag>}
                        {model.supportVision && <Tag size="small" color="green">Vision</Tag>}
                        {model.supportWebSearch && <Tag size="small" color="orange">联网</Tag>}
                      </Space>
                    </Checkbox>
                  ))}
              </Space>
            </Checkbox.Group>
          </div>
        </div>

        <div>
          <Text strong>中控模型：</Text>
          <Select
            placeholder="选择中控模型（用于智能调度）"
            value={newSessionCoordinator}
            onChange={setNewSessionCoordinator}
            style={{ width: '100%', marginTop: 8 }}
            allowClear
          >
            {allModels
              .filter(model => model.type !== 'embedding') // 🚀 修复：过滤掉向量模型，只显示聊天模型
              .map(model => (
                <Option key={model.id} value={model.id}>
                  <Space>
                    <RobotOutlined />
                    {model.name}
                    {model.supportThinking && <Tag size="small" color="blue">T</Tag>}
                  </Space>
                </Option>
              ))}
          </Select>
        </div>

        <div>
          <Text strong>搭档角色：</Text>
          <Select
            placeholder="选择搭档角色（可选）"
            value={newSessionPartner}
            onChange={setNewSessionPartner}
            style={{ width: '100%', marginTop: 8 }}
            allowClear
          >
            {allPartners.map(partner => (
              <Option key={partner.id} value={partner.id}>
                {partner.name}
              </Option>
            ))}
          </Select>
        </div>

        <div>
          <Text strong>最大讨论轮数：</Text>
          <Select
            value={newSessionMaxRounds}
            onChange={setNewSessionMaxRounds}
            style={{ width: '100%', marginTop: 8 }}
          >
            <Option value={6}>6轮（快速讨论）</Option>
            <Option value={12}>12轮（标准讨论）</Option>
            <Option value={20}>20轮（深度讨论）</Option>
            <Option value={30}>30轮（详细讨论）</Option>
          </Select>
        </div>

        {/* 🚀 新增：知识库选择 */}
        <div>
          <Text strong>关联知识库：</Text>
          <div style={{
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            padding: 12,
            maxHeight: 200,
            overflowY: 'auto',
            marginTop: 8
          }}>
            <Checkbox.Group
              value={selectedKnowledgeBases}
              onChange={setSelectedKnowledgeBases}
              style={{ width: '100%' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                {allKnowledgeBases.length > 0 ? (
                  allKnowledgeBases.map(kb => (
                    <Checkbox key={kb.id} value={kb.id}>
                      <Space>
                        <span>{kb.name}</span>
                        <Tag color="blue" size="small">{kb.embeddingModel}</Tag>
                        <Tag color="green" size="small">{kb.documents?.length || 0} 文档</Tag>
                      </Space>
                    </Checkbox>
                  ))
                ) : (
                  <Text type="secondary">暂无可用知识库，请先在知识库管理中创建</Text>
                )}
              </Space>
            </Checkbox.Group>
          </div>
        </div>
      </Space>
    </Modal>

    {/* 🎨 设置弹窗 - 科技感设计 */}
    <Modal
      title={
        <div style={{
          color: '#00d4ff',
          textShadow: '0 0 10px rgba(0, 212, 255, 0.3)',
          fontWeight: '600',
          fontSize: '18px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <SettingOutlined style={{
            filter: 'drop-shadow(0 0 5px rgba(0, 212, 255, 0.5))'
          }} />
          聊天室设置
        </div>
      }
      open={settingsModalVisible}
      onOk={saveSettings} // 🚀 修改：调用保存设置函数
      onCancel={() => setSettingsModalVisible(false)}
      okText="保存设置"
      cancelText="取消"
      width={750}
      style={{
        top: 50
      }}
      styles={{
        mask: {
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(10px)'
        },
        content: {
          background: 'rgba(26, 31, 46, 0.95)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(0, 212, 255, 0.3)',
          boxShadow: '0 20px 60px rgba(0, 212, 255, 0.2)'
        },
        header: {
          background: 'transparent',
          borderBottom: '1px solid rgba(0, 212, 255, 0.3)'
        },
        body: {
          background: 'transparent'
        },
        footer: {
          background: 'transparent',
          borderTop: '1px solid rgba(0, 212, 255, 0.3)'
        }
      }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 模型管理 */}
        <Card size="small" title="模型管理">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>当前邀请的模型：</Text>
              <div style={{ marginTop: 8 }}>
                {invitedModels.map(model => (
                  <Tag
                    key={model.id}
                    closable
                    onClose={() => {
                      const newInvitedModels = invitedModels.filter(m => m.id !== model.id);
                      setInvitedModels(newInvitedModels);

                      // 🔥 修复：同时更新会话数据中的邀请模型列表
                      if (currentSession) {
                        const updatedSession = {
                          ...currentSession,
                          invitedModels: newInvitedModels.map(m => m.id) // 保存模型ID列表
                        };
                        setCurrentSession(updatedSession);
                        dataManager.updateSession(currentSession.id, {
                          invitedModels: newInvitedModels.map(m => m.id)
                        });
                        console.log('✅ [ChatRoom] 已从会话移除模型:', model.name);
                        console.log('✅ [ChatRoom] 更新后的邀请模型:', newInvitedModels.map(m => m.name));

                      // 🔥 修复：刷新会话列表以更新模型数量显示
                      loadData();
                      }

                      message.success(`已移除模型: ${model.name}`);
                    }}
                    style={{ marginBottom: 4 }}
                  >
                    {model.name}
                  </Tag>
                ))}
              </div>
            </div>

            <div>
              <Text strong>添加模型：</Text>
              <Select
                placeholder="选择要添加的模型"
                style={{ width: '100%', marginTop: 8 }}
                onSelect={(value) => {
                  const model = allModels.find(m => m.id === value);
                  if (model && !invitedModels.find(m => m.id === model.id)) {
                    const newInvitedModels = [...invitedModels, model];
                    setInvitedModels(newInvitedModels);

                    // 🔥 修复：同时更新会话数据中的邀请模型列表
                    if (currentSession) {
                      const updatedSession = {
                        ...currentSession,
                        invitedModels: newInvitedModels.map(m => m.id) // 保存模型ID列表
                      };
                      setCurrentSession(updatedSession);
                      dataManager.updateSession(currentSession.id, {
                        invitedModels: newInvitedModels.map(m => m.id)
                      });
                      console.log('✅ [ChatRoom] 已添加模型到会话:', model.name);
                      console.log('✅ [ChatRoom] 更新后的邀请模型:', newInvitedModels.map(m => m.name));

                      // 🔥 修复：刷新会话列表以更新模型数量显示
                      loadData();
                    }

                    message.success(`已添加模型: ${model.name}`);
                  }
                }}
                value={undefined}
              >
                {allModels
                  .filter(model => model.type !== 'embedding' && !invitedModels.find(m => m.id === model.id)) // 🚀 修复：过滤掉向量模型
                  .map(model => (
                    <Option key={model.id} value={model.id}>
                      <Space>
                        <RobotOutlined />
                        {model.name}
                        {model.supportThinking && <Tag size="small" color="blue">T</Tag>}
                        {model.supportVision && <Tag size="small" color="green">V</Tag>}
                      </Space>
                    </Option>
                  ))}
              </Select>
            </div>
          </Space>
        </Card>

        {/* 🚀 新增：知识库配置 */}
        <Card size="small" title="知识库配置">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>当前关联的知识库：</Text>
              <div style={{
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                padding: 12,
                maxHeight: 200,
                overflowY: 'auto',
                marginTop: 8,
                backgroundColor: 'rgba(255, 255, 255, 0.05)'
              }}>
                <Checkbox.Group
                  value={currentSessionKnowledgeBases}
                  onChange={setCurrentSessionKnowledgeBases}
                  style={{ width: '100%' }}
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {allKnowledgeBases.length > 0 ? (
                      allKnowledgeBases.map(kb => (
                        <Checkbox key={kb.id} value={kb.id}>
                          <Space>
                            <span style={{ color: '#ffffff' }}>{kb.name}</span>
                            <Tag color="blue" size="small">{kb.embeddingModel}</Tag>
                            <Tag color="green" size="small">{kb.documents?.length || 0} 文档</Tag>
                          </Space>
                        </Checkbox>
                      ))
                    ) : (
                      <Text type="secondary" style={{ color: '#cccccc' }}>
                        暂无可用知识库，请先在知识库管理中创建
                      </Text>
                    )}
                  </Space>
                </Checkbox.Group>
              </div>
            </div>

            {currentSessionKnowledgeBases.length > 0 && (
              <div>
                <Text type="secondary" style={{ color: '#cccccc', fontSize: '12px' }}>
                  💡 提示：选中的知识库将在AI回答时提供相关背景信息
                </Text>
              </div>
            )}
          </Space>
        </Card>

        {/* 调度设置 */}
        <Card size="small" title="调度设置">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>调度模式：</Text>
              <Select
                value={schedulingMode}
                onChange={setSchedulingMode}
                style={{ width: '100%', marginTop: 8 }}
              >
                <Option value="intelligent">🧠 智能调度（中控模型决策）</Option>
                <Option value="roundRobin">🔄 轮流调度（所有模型依次回答）</Option>
                <Option value="random">🎲 随机调度（随机选择一个模型）</Option>
              </Select>
            </div>

            <div>
              <Text strong>中控模型：</Text>
              <Select
                placeholder="选择中控模型"
                value={coordinatorModel?.id}
                onChange={(value) => {
                  const model = allModels.find(m => m.id === value);
                  setCoordinatorModel(model);
                  
                  // 🔥 修复：持久化保存中控模型到会话数据
                  if (currentSession) {
                    const updatedSession = {
                      ...currentSession,
                      coordinator: value // 保存中控模型ID
                    };
                    setCurrentSession(updatedSession);
                    dataManager.updateSession(currentSession.id, {
                      coordinator: value
                    });
                    console.log('✅ [ChatRoom] 已更新会话中控模型:', model?.name || '无');
                    
                    // 刷新会话列表以更新显示
                    loadData();
                  }
                }}
                style={{ width: '100%', marginTop: 8 }}
                allowClear
              >
                {allModels.map(model => (
                  <Option key={model.id} value={model.id}>
                    {model.name}
                  </Option>
                ))}
              </Select>
            </div>

            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Text>启用中控模型</Text>
              <Switch
                checked={enableCoordinator}
                onChange={setEnableCoordinator}
              />
            </div>

            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Text>中控模型可参与回答</Text>
              <Switch
                checked={allowCoordinatorToSpeak}
                onChange={setAllowCoordinatorToSpeak}
              />
            </div>

            {/* 🔥 新增：智能结束检测控制 */}
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <Text>智能讨论结束检测</Text>
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                  自动检测讨论完成度，避免无意义重复
                </div>
              </div>
              <Switch
                checked={enableIntelligentEnd}
                onChange={setEnableIntelligentEnd}
              />
            </div>

            <div>
              <Text strong>讨论质量阈值：</Text>
              <div style={{ marginTop: 8 }}>
                <Slider
                  min={0.5}
                  max={0.9}
                  step={0.05}
                  value={discussionQualityThreshold}
                  onChange={setDiscussionQualityThreshold}
                  marks={{
                    0.5: '宽松',
                    0.75: '标准',
                    0.9: '严格'
                  }}
                  disabled={!enableIntelligentEnd}
                />
                <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                  当前阈值：{(discussionQualityThreshold * 100).toFixed(0)}%
                  {!enableIntelligentEnd && ' (已禁用)'}
                </div>
              </div>
            </div>
          </Space>
        </Card>

        {/* 其他设置 */}
        <Card size="small" title="其他设置">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Text>显示思考过程</Text>
              <Switch
                checked={showThinking}
                onChange={setShowThinking}
              />
            </div>

            {/* 🌐 网络搜索设置 */}
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <Text>网络搜索功能</Text>
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                  启用BING网络搜索，为AI提供实时信息
                  <br />
                  快捷键: {getWebSearchShortcut()}
                </div>
              </div>
              <Switch
                checked={enableWebSearch}
                onChange={setEnableWebSearch}
              />
            </div>

            <div>
              <Text strong>最大讨论轮数：</Text>
              <Select
                value={maxRounds}
                onChange={(value) => {
                  setMaxRounds(value);
                  
                  // 🔥 修复：持久化保存最大轮数到会话数据
                  if (currentSession) {
                    const updatedSession = {
                      ...currentSession,
                      maxRounds: value // 保存最大轮数
                    };
                    setCurrentSession(updatedSession);
                    dataManager.updateSession(currentSession.id, {
                      maxRounds: value
                    });
                    console.log('✅ [ChatRoom] 已更新会话最大讨论轮数:', value);
                    
                    // 刷新会话列表以更新显示
                    loadData();
                  }
                }}
                style={{ width: '100%', marginTop: 8 }}
              >
                <Option value={6}>6轮（快速讨论）</Option>
                <Option value={12}>12轮（标准讨论）</Option>
                <Option value={20}>20轮（深度讨论）</Option>
                <Option value={30}>30轮（详细讨论）</Option>
              </Select>
            </div>

            <div>
              <Text strong>搭档角色：</Text>
              <Select
                placeholder="选择搭档角色"
                value={selectedPartner?.id}
                onChange={(value) => {
                  const partner = allPartners.find(p => p.id === value);
                  setSelectedPartner(partner);
                  
                  // 🔥 修复：持久化保存搭档角色到会话数据
                  if (currentSession) {
                    const updatedSession = {
                      ...currentSession,
                      partnerId: value // 保存搭档角色ID
                    };
                    setCurrentSession(updatedSession);
                    dataManager.updateSession(currentSession.id, {
                      partnerId: value
                    });
                    console.log('✅ [ChatRoom] 已更新会话搭档角色:', partner?.name || '无');
                    
                    // 刷新会话列表以更新显示
                    loadData();
                  }
                }}
                style={{ width: '100%', marginTop: 8 }}
                allowClear
              >
                {allPartners.map(partner => (
                  <Option key={partner.id} value={partner.id}>
                    {partner.name}
                  </Option>
                ))}
              </Select>
            </div>
          </Space>
        </Card>
      </Space>
    </Modal>



    {/* 🚀 智能剪贴板管理器 */}
    <SmartClipboardManager
      visible={showClipboardManager}
      onClose={() => setShowClipboardManager(false)}
      onInsertContent={handleClipboardInsert}
    />


    </>
  );
});

// 添加displayName用于调试
ChatRoom.displayName = 'ChatRoom';

export default ChatRoom;
