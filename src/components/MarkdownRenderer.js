import React, { memo, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import 'highlight.js/styles/atom-one-dark.css'; // 🎨 深色主题代码高亮样式
import { CodeCopyButton } from './CopyButton'; // 🎯 导入代码复制按钮
import { extractCodeFromChildren } from '../utils/copyUtils'; // 🎯 导入代码提取函数
import ImagePreview from './ImagePreview'; // 🖼️ 图片预览组件
import MermaidChart from './MermaidChart'; // 🎨 Mermaid图表组件

// 🚀 使用memo优化MarkdownRenderer，避免不必要的重渲染
const MarkdownRenderer = memo(({ content, className = '' }) => {
  // 🎯 缓存组件配置，避免每次渲染都重新创建
  const components = useMemo(() => ({
    // 🎨 科技感代码块样式 + Mermaid图表支持
    code({ node, inline, className, children, ...props }) {
      const match = /language-(\w+)/.exec(className || '');
      const language = match ? match[1] : '';
      const codeContent = extractCodeFromChildren(children);

      // 🎨 Mermaid图表特殊处理
      if (!inline && language === 'mermaid') {
        return <MermaidChart code={codeContent} title="Mermaid 图表" />;
      }

      return !inline && match ? (
        <div style={{ position: 'relative' }}>
          {/* 🎯 语言标签 - 优化字体大小和位置 */}
          <div
            style={{
              position: 'absolute',
              top: '8px',
              left: '8px',
              fontSize: '9px',
              color: '#00d4ff',
              background: 'rgba(0, 212, 255, 0.08)',
              border: '1px solid rgba(0, 212, 255, 0.25)',
              padding: '2px 6px',
              borderRadius: '4px',
              zIndex: 2,
              fontWeight: '600',
              textTransform: 'uppercase',
              letterSpacing: '0.3px',
              backdropFilter: 'blur(10px)',
              opacity: 0.8,
              lineHeight: '1',
              minWidth: 'auto',
              whiteSpace: 'nowrap'
            }}
          >
            {language}
          </div>

          {/* 🎯 代码复制按钮 */}
          <CodeCopyButton
            code={codeContent}
            language={language}
          />
          <pre
            style={{
              background: 'rgba(26, 31, 46, 0.9)',
              border: '1px solid rgba(0, 212, 255, 0.3)',
              borderRadius: '12px',
              padding: '20px',
              overflow: 'auto',
              fontSize: '14px',
              lineHeight: '1.6',
              margin: '16px 0',
              boxShadow: '0 8px 32px rgba(0, 212, 255, 0.1)',
              backdropFilter: 'blur(20px)',
              position: 'relative'
            }}
            {...props}
          >
            {/* 代码块背景装饰 */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(45deg, transparent 30%, rgba(0, 212, 255, 0.03) 50%, transparent 70%)',
              borderRadius: '12px',
              pointerEvents: 'none'
            }} />
            <code className={className} style={{ position: 'relative', zIndex: 1 }} {...props}>
              {children}
            </code>
          </pre>
        </div>
      ) : (
        <code
          className={className}
          style={{
            background: 'rgba(0, 212, 255, 0.1)',
            color: '#00d4ff',
            padding: '3px 6px',
            borderRadius: '6px',
            fontSize: '0.9em',
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            border: '1px solid rgba(0, 212, 255, 0.3)',
            fontWeight: '500'
          }}
          {...props}
        >
          {children}
        </code>
      );
    },
          // 🎨 科技感表格样式
          table({ children, ...props }) {
            return (
              <div style={{
                overflow: 'auto',
                margin: '16px 0',
                borderRadius: '12px',
                boxShadow: '0 8px 32px rgba(0, 212, 255, 0.1)',
                border: '1px solid rgba(0, 212, 255, 0.3)'
              }}>
                <table
                  style={{
                    borderCollapse: 'collapse',
                    width: '100%',
                    background: 'rgba(26, 31, 46, 0.8)',
                    backdropFilter: 'blur(20px)'
                  }}
                  {...props}
                >
                  {children}
                </table>
              </div>
            );
          },
          th({ children, ...props }) {
            return (
              <th
                style={{
                  border: '1px solid rgba(0, 212, 255, 0.3)',
                  padding: '12px 16px',
                  background: 'rgba(0, 212, 255, 0.2)',
                  fontWeight: '600',
                  textAlign: 'left',
                  color: '#00d4ff',
                  textShadow: '0 0 5px rgba(0, 212, 255, 0.3)'
                }}
                {...props}
              >
                {children}
              </th>
            );
          },
          td({ children, ...props }) {
            return (
              <td
                style={{
                  border: '1px solid rgba(0, 212, 255, 0.3)',
                  padding: '12px 16px',
                  color: '#ffffff'
                }}
                {...props}
              >
                {children}
              </td>
            );
          },
          // 🎨 科技感引用块样式
          blockquote({ children, ...props }) {
            return (
              <blockquote
                style={{
                  borderLeft: '4px solid #00d4ff',
                  paddingLeft: '20px',
                  padding: '16px 20px',
                  margin: '16px 0',
                  color: 'rgba(255, 255, 255, 0.9)',
                  fontStyle: 'italic',
                  background: 'rgba(0, 212, 255, 0.05)',
                  borderRadius: '0 12px 12px 0',
                  boxShadow: '0 4px 15px rgba(0, 212, 255, 0.1)',
                  backdropFilter: 'blur(10px)',
                  position: 'relative'
                }}
                {...props}
              >
                {/* 引用块装饰 */}
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  bottom: 0,
                  width: '4px',
                  background: 'linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%)',
                  boxShadow: '0 0 10px rgba(0, 212, 255, 0.5)'
                }} />
                {children}
              </blockquote>
            );
          },
          // 自定义链接样式
          a({ children, href, ...props }) {
            return (
              <a
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: '#0366d6',
                  textDecoration: 'none'
                }}
                onMouseEnter={(e) => {
                  e.target.style.textDecoration = 'underline';
                }}
                onMouseLeave={(e) => {
                  e.target.style.textDecoration = 'none';
                }}
                {...props}
              >
                {children}
              </a>
            );
          },
          // 自定义标题样式
          h1({ children, ...props }) {
            return (
              <h1
                style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  marginTop: '24px',
                  marginBottom: '16px',
                  borderBottom: '1px solid #e1e4e8',
                  paddingBottom: '8px'
                }}
                {...props}
              >
                {children}
              </h1>
            );
          },
          h2({ children, ...props }) {
            return (
              <h2
                style={{
                  fontSize: '20px',
                  fontWeight: 'bold',
                  marginTop: '20px',
                  marginBottom: '12px',
                  borderBottom: '1px solid #e1e4e8',
                  paddingBottom: '6px'
                }}
                {...props}
              >
                {children}
              </h2>
            );
          },
          h3({ children, ...props }) {
            return (
              <h3
                style={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  marginTop: '16px',
                  marginBottom: '8px'
                }}
                {...props}
              >
                {children}
              </h3>
            );
          },
          // 自定义列表样式
          ul({ children, ...props }) {
            return (
              <ul
                style={{
                  paddingLeft: '20px',
                  margin: '16px 0'
                }}
                {...props}
              >
                {children}
              </ul>
            );
          },
          ol({ children, ...props }) {
            return (
              <ol
                style={{
                  paddingLeft: '20px',
                  margin: '16px 0'
                }}
                {...props}
              >
                {children}
              </ol>
            );
          },
          li({ children, ...props }) {
            return (
              <li
                style={{
                  marginBottom: '4px',
                  lineHeight: '1.6'
                }}
                {...props}
              >
                {children}
              </li>
            );
          },
          // 自定义段落样式
          p({ children, ...props }) {
            return (
              <p
                style={{
                  margin: '16px 0',
                  lineHeight: '1.6'
                }}
                {...props}
              >
                {children}
              </p>
            );
          },
          // 🖼️ 图片预览支持
          img({ src, alt, ...props }) {
            return (
              <ImagePreview
                src={src}
                alt={alt || '图片'}
                style={{
                  margin: '16px 0',
                  display: 'block',
                  maxWidth: '100%'
                }}
                {...props}
              />
            );
          }
  }), []); // 🎯 空依赖数组，组件配置只创建一次

  // 🎯 缓存插件配置
  const plugins = useMemo(() => ({
    remarkPlugins: [remarkGfm],
    rehypePlugins: [rehypeHighlight]
  }), []);

  return (
    <div className={`markdown-content ${className}`}>
      <ReactMarkdown
        {...plugins}
        components={components}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}, (prevProps, nextProps) => {
  // 🎯 只在内容或类名变化时重渲染
  return prevProps.content === nextProps.content &&
         prevProps.className === nextProps.className;
});

MarkdownRenderer.displayName = 'MarkdownRenderer';

export default MarkdownRenderer;
