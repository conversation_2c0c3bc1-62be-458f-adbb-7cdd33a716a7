import React, { memo, useState, useEffect, useRef, useCallback } from 'react';
import { EyeOutlined, EyeInvisibleOutlined, CloseOutlined } from '@ant-design/icons';

// 🚀 性能监控组件 - 实时监控滚动性能
const PerformanceMonitor = memo(({
  enabled = false,
  onMetrics = null,
  onToggle = null,
  position = 'top-center' // 'top-right', 'top-center'
}) => {
  const [metrics, setMetrics] = useState({
    fps: 0,
    frameTime: 0,
    memoryUsage: 0,
    renderCount: 0,
    scrollEvents: 0
  });

  const [isCollapsed, setIsCollapsed] = useState(() => {
    // 🎯 从localStorage读取用户偏好
    return localStorage.getItem('performanceMonitor-collapsed') === 'true';
  });

  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const renderCountRef = useRef(0);
  const scrollCountRef = useRef(0);
  const animationFrameRef = useRef();
  const scrollListenerRef = useRef();
  const renderObserverRef = useRef();

  // 🎯 FPS计算
  const calculateFPS = () => {
    frameCountRef.current++;
    const now = performance.now();
    const delta = now - lastTimeRef.current;

    if (delta >= 1000) {
      const fps = Math.round((frameCountRef.current * 1000) / delta);
      const frameTime = delta / frameCountRef.current;
      
      setMetrics(prev => ({
        ...prev,
        fps,
        frameTime: Math.round(frameTime * 100) / 100,
        renderCount: renderCountRef.current,
        scrollEvents: scrollCountRef.current
      }));

      frameCountRef.current = 0;
      lastTimeRef.current = now;
      renderCountRef.current = 0;
      scrollCountRef.current = 0;
    }

    if (enabled) {
      animationFrameRef.current = requestAnimationFrame(calculateFPS);
    }
  };

  // 🎯 内存使用监控
  const updateMemoryUsage = () => {
    if (performance.memory) {
      const memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
      setMetrics(prev => ({
        ...prev,
        memoryUsage
      }));
    }
  };

  // 🎯 滚动事件监听 - 优化版本
  const handleScroll = useCallback(() => {
    scrollCountRef.current++;
  }, []);

  // 🎯 渲染事件监听 - 使用MutationObserver
  const setupRenderObserver = useCallback(() => {
    if (renderObserverRef.current) {
      renderObserverRef.current.disconnect();
    }

    const observer = new MutationObserver(() => {
      renderCountRef.current++;
    });

    // 监听消息容器的DOM变化
    const messageContainers = document.querySelectorAll('[data-scroll-container], .message-list, .virtualized-list');
    messageContainers.forEach(container => {
      observer.observe(container, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
      });
    });

    renderObserverRef.current = observer;
  }, []);

  // 🎯 切换收起/展开状态
  const toggleCollapsed = useCallback(() => {
    const newCollapsed = !isCollapsed;
    setIsCollapsed(newCollapsed);
    localStorage.setItem('performanceMonitor-collapsed', newCollapsed.toString());
  }, [isCollapsed]);

  // 🎯 关闭监控
  const handleClose = useCallback(() => {
    if (onToggle) {
      onToggle(false);
    }
  }, [onToggle]);

  // 🎯 快捷键监听
  useEffect(() => {
    const handleKeyDown = (e) => {
      // F12 或 Ctrl+Shift+P 切换性能监控
      if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'P')) {
        e.preventDefault();
        if (onToggle) {
          onToggle(!enabled);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [enabled, onToggle]);

  useEffect(() => {
    if (!enabled) return;

    // 启动FPS监控
    animationFrameRef.current = requestAnimationFrame(calculateFPS);

    // 启动内存监控
    const memoryInterval = setInterval(updateMemoryUsage, 2000);

    // 🎯 改进的滚动事件监听
    const setupScrollListeners = () => {
      // 移除旧的监听器
      if (scrollListenerRef.current) {
        scrollListenerRef.current.forEach(({ element, handler }) => {
          element.removeEventListener('scroll', handler);
        });
      }

      const listeners = [];

      // 监听多种可能的滚动容器
      const selectors = [
        '[data-scroll-container]',
        '.message-list',
        '.virtualized-list',
        '.ant-layout-content',
        '.chat-messages'
      ];

      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          el.addEventListener('scroll', handleScroll, { passive: true });
          listeners.push({ element: el, handler: handleScroll });
        });
      });

      scrollListenerRef.current = listeners;
    };

    // 设置滚动监听器
    setupScrollListeners();

    // 设置渲染观察器
    setupRenderObserver();

    // 定期重新设置监听器（防止DOM变化导致监听器失效）
    const refreshInterval = setInterval(() => {
      setupScrollListeners();
      setupRenderObserver();
    }, 5000);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      clearInterval(memoryInterval);
      clearInterval(refreshInterval);

      // 清理滚动监听器
      if (scrollListenerRef.current) {
        scrollListenerRef.current.forEach(({ element, handler }) => {
          element.removeEventListener('scroll', handler);
        });
      }

      // 清理渲染观察器
      if (renderObserverRef.current) {
        renderObserverRef.current.disconnect();
      }
    };
  }, [enabled, handleScroll, setupRenderObserver]);

  // 🎯 向父组件报告性能指标
  useEffect(() => {
    if (onMetrics && enabled) {
      onMetrics(metrics);
    }
  }, [metrics, onMetrics, enabled]);

  if (!enabled) return null;

  // 🎯 根据位置计算样式
  const getPositionStyle = () => {
    const baseStyle = {
      position: 'fixed',
      background: 'rgba(0, 0, 0, 0.9)',
      color: '#00d4ff',
      borderRadius: '8px',
      fontSize: '11px',
      fontFamily: 'Monaco, monospace',
      zIndex: 9999,
      border: '1px solid rgba(0, 212, 255, 0.3)',
      backdropFilter: 'blur(10px)',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
      transition: 'all 0.3s ease'
    };

    switch (position) {
      case 'top-center':
        return {
          ...baseStyle,
          top: '10px',
          left: '50%',
          transform: 'translateX(-50%)',
          minWidth: isCollapsed ? '120px' : '200px'
        };
      case 'top-right':
      default:
        return {
          ...baseStyle,
          top: '10px',
          right: '10px',
          minWidth: isCollapsed ? '120px' : '180px'
        };
    }
  };

  return (
    <div style={getPositionStyle()}>
      {/* 🎯 标题栏和控制按钮 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: isCollapsed ? '0' : '8px',
        padding: '6px 8px',
        borderBottom: isCollapsed ? 'none' : '1px solid rgba(0, 212, 255, 0.2)'
      }}>
        <div style={{
          fontWeight: 'bold',
          color: '#ffffff',
          fontSize: '12px'
        }}>
          性能监控
        </div>

        <div style={{ display: 'flex', gap: '4px' }}>
          {/* 收起/展开按钮 */}
          <div
            onClick={toggleCollapsed}
            style={{
              cursor: 'pointer',
              padding: '2px 4px',
              borderRadius: '3px',
              color: '#00d4ff',
              fontSize: '10px',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.target.style.background = 'rgba(0, 212, 255, 0.2)';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'transparent';
            }}
            title={isCollapsed ? '展开' : '收起'}
          >
            {isCollapsed ? <EyeOutlined /> : <EyeInvisibleOutlined />}
          </div>

          {/* 关闭按钮 */}
          <div
            onClick={handleClose}
            style={{
              cursor: 'pointer',
              padding: '2px 4px',
              borderRadius: '3px',
              color: '#ff4d4f',
              fontSize: '10px',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.target.style.background = 'rgba(255, 77, 79, 0.2)';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'transparent';
            }}
            title="关闭 (F12)"
          >
            <CloseOutlined />
          </div>
        </div>
      </div>

      {/* 🎯 性能指标 - 只在展开时显示 */}
      {!isCollapsed && (
        <div style={{ padding: '0 8px 6px' }}>
          <div style={{
            color: metrics.fps >= 55 ? '#52c41a' : metrics.fps >= 30 ? '#faad14' : '#ff4d4f',
            marginBottom: '3px'
          }}>
            FPS: {metrics.fps}
          </div>

          <div style={{ color: '#ffffff', marginBottom: '3px' }}>
            帧时间: {metrics.frameTime}ms
          </div>

          <div style={{
            color: metrics.memoryUsage > 100 ? '#ff4d4f' : metrics.memoryUsage > 50 ? '#faad14' : '#52c41a',
            marginBottom: '3px'
          }}>
            内存: {metrics.memoryUsage}MB
          </div>

          <div style={{ color: '#8a2be2', marginBottom: '3px' }}>
            渲染: {metrics.renderCount}/s
          </div>

          <div style={{ color: '#00d4ff' }}>
            滚动: {metrics.scrollEvents}/s
          </div>

          {/* 🎯 快捷键提示 */}
          <div style={{
            marginTop: '6px',
            paddingTop: '4px',
            borderTop: '1px solid rgba(0, 212, 255, 0.1)',
            fontSize: '9px',
            color: 'rgba(255, 255, 255, 0.6)',
            textAlign: 'center'
          }}>
            F12 或 Ctrl+Shift+P 切换
          </div>
        </div>
      )}
    </div>
  );
});

PerformanceMonitor.displayName = 'PerformanceMonitor';

export default PerformanceMonitor;
