import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Drawer,
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Typography,
  Space,
  Button,
  Select,
  DatePicker,
  Tooltip,
  Alert,
  Divider,
  Badge,
  List,
  Avatar
} from 'antd';
import {
  BarChartOutlined,
  ClockCircleOutlined,
  ThunderboltOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  RobotOutlined,
  TrophyOutlined,
  WarningOutlined,
  ReloadOutlined,
  ExportOutlined
} from '@ant-design/icons';
import { performanceDataManager } from '../utils/performanceDataManager.js';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

/**
 * 🚀 AI模型性能分析仪表板
 * 实时监控各模型的响应时间、成功率、Token消耗等指标
 */
const AIModelPerformanceDashboard = ({
  visible,
  onClose,
  models = [],
  messages = [],
  currentSession = null,
  style = {}
}) => {
  const [performanceData, setPerformanceData] = useState({});
  const [timeRange, setTimeRange] = useState('today'); // 🚀 修改：默认显示今日数据
  const [selectedModel, setSelectedModel] = useState('all');
  const [refreshing, setRefreshing] = useState(false);
  const [alerts, setAlerts] = useState([]);
  const [globalStats, setGlobalStats] = useState({}); // 🚀 新增：全局统计
  const [isInitialized, setIsInitialized] = useState(false);

  const intervalRef = useRef(null);

  // 🚀 新增：初始化性能数据管理器
  const initializePerformanceManager = useCallback(async () => {
    if (!isInitialized) {
      await performanceDataManager.initialize();
      setIsInitialized(true);
      console.log('✅ [性能分析] 性能数据管理器初始化完成');
    }
  }, [isInitialized]);

  // 🚀 重写：性能指标计算（使用独立的性能数据）
  const calculatePerformanceMetrics = useCallback(() => {
    if (!isInitialized) {
      console.log('⚠️ [性能分析] 性能数据管理器未初始化');
      return;
    }

    console.log('📊 [性能分析] 开始计算性能指标，时间范围:', timeRange);

    // 获取全局统计
    const globalStats = performanceDataManager.getGlobalStats(timeRange);
    console.log('📊 [性能分析] 全局统计:', globalStats);
    setGlobalStats(globalStats);

    // 获取所有模型的性能统计
    const allModelStats = performanceDataManager.getAllModelStats(timeRange);
    console.log('📊 [性能分析] 所有模型统计:', allModelStats);
    console.log('📊 [性能分析] 模型统计数量:', Object.keys(allModelStats).length);

    // 🚀 新增：只显示有数据的模型，或者当前会话中配置的模型
    let relevantModels = [];

    if (timeRange === 'session' && currentSession) {
      // 会话模式：显示当前会话中配置的模型
      relevantModels = models.filter(model => {
        // 检查模型是否在当前会话的消息中出现过
        const hasMessages = messages.some(msg => msg.sender === model.name);
        return hasMessages;
      });
    } else {
      // 其他时间范围：显示有性能数据的模型
      const modelNamesWithData = Object.keys(allModelStats).filter(name =>
        allModelStats[name] && allModelStats[name].totalRequests > 0
      );
      relevantModels = models.filter(model => modelNamesWithData.includes(model.name));
    }

    console.log('📊 [性能分析] 相关模型数量:', relevantModels.length);
    console.log('📊 [性能分析] 全局统计:', globalStats);

    // 构建模型性能数据
    const modelMetrics = {};

    relevantModels.forEach(model => {
      const modelStats = allModelStats[model.name];
      if (modelStats) {
        modelMetrics[model.name] = {
          ...modelStats,
          model: model // 保存模型信息以便后续使用
        };
      }
    });

    setPerformanceData(modelMetrics);

    // 🚀 重写：生成性能警告
    const newAlerts = [];
    Object.entries(modelMetrics).forEach(([modelName, metrics]) => {
      if (metrics.successRate < 90) {
        newAlerts.push({
          type: 'error',
          model: modelName,
          message: `成功率过低 (${metrics.successRate.toFixed(1)}%)`
        });
      }
      if (metrics.avgResponseTime > 5000) {
        newAlerts.push({
          type: 'warning',
          model: modelName,
          message: `响应时间过长 (${metrics.avgResponseTime}ms)`
        });
      }
      if (metrics.totalTokens > 100000) {
        newAlerts.push({
          type: 'info',
          model: modelName,
          message: `Token使用量较高 (${(metrics.totalTokens / 1000).toFixed(1)}K)`
        });
      }
    });

    setAlerts(newAlerts);

    console.log('📊 [性能分析] 性能指标计算完成');
    console.log('📊 [性能分析] 模型数量:', Object.keys(modelMetrics).length);
    console.log('📊 [性能分析] 警告数量:', newAlerts.length);

  }, [isInitialized, timeRange, models, messages, currentSession]);



  // 🚀 重写：初始化和定时刷新
  useEffect(() => {
    if (visible) {
      // 初始化性能数据管理器
      initializePerformanceManager().then(() => {
        // 计算性能指标
        calculatePerformanceMetrics();
        // 设置定时刷新
        intervalRef.current = setInterval(calculatePerformanceMetrics, 30000); // 30秒刷新一次
      });
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [visible, initializePerformanceManager, calculatePerformanceMetrics]);

  // 🚀 新增：时间范围变化时重新计算
  useEffect(() => {
    if (visible && isInitialized) {
      calculatePerformanceMetrics();
    }
  }, [timeRange, visible, isInitialized, calculatePerformanceMetrics]);

  // 手动刷新
  const handleRefresh = async () => {
    setRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟刷新延迟
    calculatePerformanceMetrics();
    setRefreshing(false);
  };

  // 获取状态颜色
  const getStatusColor = (status) => {
    switch (status) {
      case 'fast': return '#52c41a';
      case 'normal': return '#1890ff';
      case 'slow': return '#fa8c16';
      default: return '#8c8c8c';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status) => {
    switch (status) {
      case 'fast': return <ThunderboltOutlined />;
      case 'normal': return <ClockCircleOutlined />;
      case 'slow': return <WarningOutlined />;
      default: return <ExclamationCircleOutlined />;
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '模型',
      dataIndex: 'name',
      key: 'name',
      render: (name) => (
        <Space>
          <Avatar size="small" icon={<RobotOutlined />} style={{ backgroundColor: '#1890ff' }} />
          <Text strong>{name}</Text>
        </Space>
      )
    },
    {
      title: '请求数',
      dataIndex: 'totalRequests',
      key: 'totalRequests',
      sorter: (a, b) => a.totalRequests - b.totalRequests,
      render: (value) => <Badge count={value} style={{ backgroundColor: '#52c41a' }} />
    },
    {
      title: '成功率',
      dataIndex: 'successRate',
      key: 'successRate',
      sorter: (a, b) => a.successRate - b.successRate,
      render: (value) => (
        <Progress
          percent={value}
          size="small"
          strokeColor={value >= 95 ? '#52c41a' : value >= 90 ? '#1890ff' : '#ff4d4f'}
          format={percent => `${percent.toFixed(1)}%`}
        />
      )
    },
    {
      title: '平均响应时间',
      dataIndex: 'avgResponseTime',
      key: 'avgResponseTime',
      sorter: (a, b) => a.avgResponseTime - b.avgResponseTime,
      render: (value, record) => (
        <Tag color={getStatusColor(record.status)} icon={getStatusIcon(record.status)}>
          {value}ms
        </Tag>
      )
    },
    {
      title: 'Token消耗',
      dataIndex: 'totalTokens',
      key: 'totalTokens',
      sorter: (a, b) => a.totalTokens - b.totalTokens,
      render: (value) => (
        <Tooltip title={`总计 ${value.toLocaleString()} tokens`}>
          <Text>{(value / 1000).toFixed(1)}K</Text>
        </Tooltip>
      )
    },
    {
      title: 'Token使用',
      dataIndex: 'totalTokens',
      key: 'totalTokens',
      sorter: (a, b) => a.totalTokens - b.totalTokens,
      render: (value, record) => (
        <Space direction="vertical" size="small">
          <Text strong>{(value / 1000).toFixed(1)}K</Text>
          <Text type="secondary" style={{ fontSize: '11px' }}>
            输入: {((record.requestTokens || 0) / 1000).toFixed(1)}K
          </Text>
          <Text type="secondary" style={{ fontSize: '11px' }}>
            输出: {((record.responseTokens || 0) / 1000).toFixed(1)}K
          </Text>
        </Space>
      )
    },
    {
      title: '平均Token/请求',
      dataIndex: 'avgTokensPerRequest',
      key: 'avgTokensPerRequest',
      sorter: (a, b) => a.avgTokensPerRequest - b.avgTokensPerRequest,
      render: (value) => <Text>{value}</Text>
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: '快速', value: 'fast' },
        { text: '正常', value: 'normal' },
        { text: '缓慢', value: 'slow' },
        { text: '空闲', value: 'idle' }
      ],
      onFilter: (value, record) => record.status === value,
      render: (status) => {
        const statusMap = {
          fast: { color: 'green', text: '快速' },
          normal: { color: 'blue', text: '正常' },
          slow: { color: 'orange', text: '缓慢' },
          idle: { color: 'default', text: '空闲' }
        };
        const config = statusMap[status] || statusMap.idle;
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    }
  ];

  // 准备表格数据
  const tableData = Object.entries(performanceData).map(([name, metrics]) => ({
    key: name,
    name,
    ...metrics
  }));

  // 🚀 修改：使用全局统计数据
  const totalRequests = globalStats.totalRequests || 0;
  const avgSuccessRate = globalStats.successRate || 0;
  const totalTokens = globalStats.totalTokens || 0;
  const totalErrors = globalStats.failedRequests || 0;
  const avgResponseTime = globalStats.avgResponseTime || 0;
  const activeModels = globalStats.activeModels || 0;

  return (
    <Drawer
      title={
        <Space>
          <BarChartOutlined style={{ color: '#1890ff' }} />
          <span>AI模型性能分析</span>
          <Badge count={alerts.length} style={{ backgroundColor: '#ff4d4f' }} />
        </Space>
      }
      placement="right"
      width={800}
      open={visible}
      onClose={onClose}
      style={{
        background: 'linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0a0e1a 100%)',
        ...style
      }}
      styles={{
        header: {
          background: 'rgba(26, 31, 46, 0.95)',
          borderBottom: '1px solid rgba(0, 212, 255, 0.2)',
          color: '#fff'
        },
        body: {
          background: 'transparent',
          padding: '16px'
        }
      }}
    >
      {/* 控制面板 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: '100%' }}
          >
            <Option value="today">今日</Option>
            <Option value="week">本周</Option>
            <Option value="month">本月</Option>
            <Option value="all">全部时间</Option>
            <Option value="session">当前会话</Option>
          </Select>
        </Col>
        <Col span={8}>
          <Select
            value={selectedModel}
            onChange={setSelectedModel}
            style={{ width: '100%' }}
            placeholder="选择模型"
          >
            <Option value="all">所有模型</Option>
            {models.map(model => (
              <Option key={model.id} value={model.name}>{model.name}</Option>
            ))}
          </Select>
        </Col>
        <Col span={8}>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              loading={refreshing}
              onClick={handleRefresh}
            >
              刷新
            </Button>
            <Button icon={<ExportOutlined />}>
              导出
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 性能警告 */}
      {alerts.length > 0 && (
        <Alert
          message="性能警告"
          description={
            <List
              size="small"
              dataSource={alerts}
              renderItem={alert => (
                <List.Item>
                  <Text type={alert.type === 'error' ? 'danger' : 'warning'}>
                    {alert.model}: {alert.message}
                  </Text>
                </List.Item>
              )}
            />
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 🚀 修改：会话信息卡片 - 仅在会话模式下显示 */}
      {timeRange === 'session' && currentSession && (
        <Card
          size="small"
          title={`📊 ${currentSession.title || currentSession.name || '当前会话'} - 性能分析`}
          style={{
            background: 'rgba(26, 31, 46, 0.6)',
            border: '1px solid rgba(0, 212, 255, 0.2)',
            marginBottom: 16
          }}
          headStyle={{
            background: 'rgba(0, 0, 0, 0.2)',
            borderBottom: '1px solid rgba(0, 212, 255, 0.2)',
            color: '#fff'
          }}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Statistic
                title="会话消息数"
                value={messages.length}
                valueStyle={{ color: '#1890ff', fontSize: '16px' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="用户消息"
                value={messages.filter(msg => msg.sender === 'user').length}
                valueStyle={{ color: '#52c41a', fontSize: '16px' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="AI回复"
                value={messages.filter(msg => msg.sender !== 'user' && msg.sender !== 'system').length}
                valueStyle={{ color: '#fa8c16', fontSize: '16px' }}
              />
            </Col>
          </Row>
        </Card>
      )}

      {/* 总体统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card size="small" style={{ background: 'rgba(26, 31, 46, 0.6)', border: '1px solid rgba(0, 212, 255, 0.2)' }}>
            <Statistic
              title="模型请求数"
              value={totalRequests}
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small" style={{ background: 'rgba(26, 31, 46, 0.6)', border: '1px solid rgba(82, 196, 26, 0.2)' }}>
            <Statistic
              title="平均成功率"
              value={avgSuccessRate}
              precision={1}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small" style={{ background: 'rgba(26, 31, 46, 0.6)', border: '1px solid rgba(250, 140, 22, 0.2)' }}>
            <Statistic
              title="Token消耗"
              value={totalTokens / 1000}
              precision={1}
              suffix="K"
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small" style={{ background: 'rgba(26, 31, 46, 0.6)', border: '1px solid rgba(255, 77, 79, 0.2)' }}>
            <Statistic
              title="错误次数"
              value={totalErrors}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 🚀 新增：扩展统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card size="small" style={{ background: 'rgba(26, 31, 46, 0.6)', border: '1px solid rgba(135, 208, 104, 0.2)' }}>
            <Statistic
              title="平均响应时间"
              value={avgResponseTime}
              suffix="ms"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#87d068' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small" style={{ background: 'rgba(26, 31, 46, 0.6)', border: '1px solid rgba(114, 46, 209, 0.2)' }}>
            <Statistic
              title="活跃模型"
              value={activeModels}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small" style={{ background: 'rgba(26, 31, 46, 0.6)', border: '1px solid rgba(19, 194, 194, 0.2)' }}>
            <Statistic
              title="输入Token"
              value={(globalStats.requestTokens || 0) / 1000}
              precision={1}
              suffix="K"
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small" style={{ background: 'rgba(26, 31, 46, 0.6)', border: '1px solid rgba(235, 47, 150, 0.2)' }}>
            <Statistic
              title="输出Token"
              value={(globalStats.responseTokens || 0) / 1000}
              precision={1}
              suffix="K"
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
      </Row>

      <Divider style={{ borderColor: 'rgba(0, 212, 255, 0.2)' }} />

      {/* 详细性能表格 */}
      <Card
        title="模型性能详情"
        size="small"
        style={{
          background: 'rgba(26, 31, 46, 0.6)',
          border: '1px solid rgba(0, 212, 255, 0.2)'
        }}
        headStyle={{
          background: 'rgba(0, 0, 0, 0.2)',
          borderBottom: '1px solid rgba(0, 212, 255, 0.2)',
          color: '#fff'
        }}
        bodyStyle={{ padding: '12px' }}
      >
        <Table
          columns={columns}
          dataSource={tableData}
          size="small"
          pagination={false}
          style={{
            background: 'transparent'
          }}
        />
      </Card>
    </Drawer>
  );
};

export default AIModelPerformanceDashboard;
