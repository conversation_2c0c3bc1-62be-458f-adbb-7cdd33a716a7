import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Input,
  Button,
  Select,
  Space,
  Tabs,
  Typography,
  Alert,
  Spin,
  Tag,
  Progress,
  message,
  Checkbox,
  Row,
  Col,
  Statistic,
  Empty
} from 'antd';
import {
  SendOutlined,
  DownloadOutlined,
  RobotOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';

import { APIManager } from '../utils/apiManager';
import MarkdownRenderer from './MarkdownRenderer';

const { TextArea } = Input;
const { Option } = Select;
const { Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const MultiModelAnswer = ({ dataManager }) => {
  // 基础状态
  const [allModels, setAllModels] = useState([]);
  const [allPartners, setAllPartners] = useState([]);
  const [selectedModels, setSelectedModels] = useState([]);
  const [selectedPartner, setSelectedPartner] = useState(null);
  
  // 问题和回答
  const [question, setQuestion] = useState('');
  const [responses, setResponses] = useState(new Map());
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingModels, setProcessingModels] = useState(new Set());
  
  // 统计信息
  const [statistics, setStatistics] = useState({
    totalModels: 0,
    completedModels: 0,
    failedModels: 0,
    averageTime: 0,
    startTime: null
  });

  // 管理器实例
  const apiManagerRef = useRef(new APIManager());

  // 初始化数据
  useEffect(() => {
    if (dataManager) {
      loadData();
    }
  }, [dataManager]); // loadData 在组件内部定义，不需要添加到依赖

  // 清理资源
  useEffect(() => {
    const apiManager = apiManagerRef.current;

    return () => {
      apiManager.cleanup();
    };
  }, []);

  const loadData = () => {
    setAllModels(dataManager.getAllModels());
    setAllPartners(dataManager.getPartners());
  };

  // 选择/取消选择模型
  const toggleModelSelection = (modelId) => {
    setSelectedModels(prev => {
      const isSelected = prev.find(m => m.id === modelId);
      if (isSelected) {
        return prev.filter(m => m.id !== modelId);
      } else {
        const model = allModels.find(m => m.id === modelId);
        return model ? [...prev, model] : prev;
      }
    });
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedModels.length === allModels.length) {
      setSelectedModels([]);
    } else {
      setSelectedModels([...allModels]);
    }
  };

  // 发送问题给所有选中的模型
  const sendToAllModels = async () => {
    if (!question.trim()) {
      message.warning('请输入问题');
      return;
    }
    
    if (selectedModels.length === 0) {
      message.warning('请选择至少一个模型');
      return;
    }

    setIsProcessing(true);
    setProcessingModels(new Set(selectedModels.map(m => m.id)));
    setResponses(new Map());
    
    const startTime = Date.now();
    setStatistics({
      totalModels: selectedModels.length,
      completedModels: 0,
      failedModels: 0,
      averageTime: 0,
      startTime
    });

    // 并行发送请求
    const promises = selectedModels.map(model => 
      sendToSingleModel(model, question, startTime)
    );

    try {
      await Promise.allSettled(promises);
    } catch (error) {
      console.error('批量请求过程中出错:', error);
    } finally {
      setIsProcessing(false);
      setProcessingModels(new Set());
    }
  };

  // 发送问题给单个模型
  const sendToSingleModel = async (model, questionText, startTime) => {
    const modelStartTime = Date.now();
    
    try {
      // 构建消息
      const messages = [
        {
          sender: 'user',
          content: questionText,
          timestamp: new Date().toISOString()
        }
      ];

      // 发送请求
      const response = await apiManagerRef.current.sendChatRequest(
        model,
        messages,
        {
          partnerPrompt: selectedPartner?.prompt || null,
          enableThinking: model.supportThinking,
          maxTokens: 200000,
          temperature: 0.7
        }
      );

      const duration = Date.now() - modelStartTime;

      if (response.success) {
        // 成功响应
        const responseData = {
          model,
          content: response.data.content,
          thinking: response.data.thinking,
          usage: response.data.usage,
          duration,
          timestamp: new Date().toISOString(),
          status: 'success'
        };

        setResponses(prev => new Map(prev.set(model.id, responseData)));
        
        // 更新统计
        setStatistics(prev => ({
          ...prev,
          completedModels: prev.completedModels + 1,
          averageTime: (prev.averageTime * prev.completedModels + duration) / (prev.completedModels + 1)
        }));

      } else {
        // 失败响应
        const errorData = {
          model,
          error: response.error,
          duration,
          timestamp: new Date().toISOString(),
          status: 'error'
        };

        setResponses(prev => new Map(prev.set(model.id, errorData)));
        
        // 更新统计
        setStatistics(prev => ({
          ...prev,
          failedModels: prev.failedModels + 1
        }));
      }

    } catch (error) {
      const duration = Date.now() - modelStartTime;
      
      const errorData = {
        model,
        error: error.message,
        duration,
        timestamp: new Date().toISOString(),
        status: 'error'
      };

      setResponses(prev => new Map(prev.set(model.id, errorData)));
      
      setStatistics(prev => ({
        ...prev,
        failedModels: prev.failedModels + 1
      }));
    } finally {
      // 移除处理状态
      setProcessingModels(prev => {
        const newSet = new Set(prev);
        newSet.delete(model.id);
        return newSet;
      });
    }
  };

  // 重新发送给特定模型
  const retryModel = async (modelId) => {
    const model = selectedModels.find(m => m.id === modelId);
    if (!model || !question.trim()) return;

    setProcessingModels(prev => new Set([...prev, modelId]));
    
    try {
      await sendToSingleModel(model, question, Date.now());
      message.success(`${model.name} 重试成功`);
    } catch (error) {
      message.error(`${model.name} 重试失败`);
    }
  };

  // 导出结果
  const exportResults = () => {
    if (responses.size === 0) {
      message.warning('没有可导出的结果');
      return;
    }

    const exportData = {
      question,
      timestamp: new Date().toISOString(),
      partner: selectedPartner?.name || null,
      statistics,
      responses: Array.from(responses.values()).map(response => ({
        modelName: response.model.name,
        configName: response.model.configName,
        status: response.status,
        content: response.content || null,
        thinking: response.thinking || null,
        error: response.error || null,
        duration: response.duration,
        usage: response.usage || null
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `multi-model-results-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    message.success('结果已导出');
  };

  // 渲染模型响应
  const renderResponse = (response) => {
    const { model, status, content, thinking, error, duration, usage } = response;
    
    if (status === 'error') {
      return (
        <Card
          size="small"
          title={
            <Space>
              <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
              {model.name}
              <Tag color="red">失败</Tag>
            </Space>
          }
          extra={
            <Button
              size="small"
              type="link"
              onClick={() => retryModel(model.id)}
              loading={processingModels.has(model.id)}
            >
              重试
            </Button>
          }
        >
          <Alert
            message="请求失败"
            description={error}
            type="error"
            showIcon
          />
        </Card>
      );
    }

    return (
      <Card
        size="small"
        title={
          <Space>
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
            {model.name}
            <Tag color="green">成功</Tag>
            <Tag color="blue">{duration}ms</Tag>
          </Space>
        }
        extra={
          usage && (
            <Text type="secondary" style={{ fontSize: 12 }}>
              {usage.total_tokens} tokens
            </Text>
          )
        }
      >
        <div style={{ marginBottom: thinking ? 12 : 0 }}>
          <MarkdownRenderer
            content={content}
            className="ai-message-content"
          />
        </div>

        {thinking && (
          <div className="thinking-content" style={{
            marginTop: 12,
            padding: '8px 12px',
            background: 'rgba(0,0,0,0.05)',
            borderRadius: '8px',
            fontSize: '12px',
            border: '1px solid rgba(0,0,0,0.1)'
          }}>
            <div style={{
              marginBottom: 4,
              fontSize: 11,
              fontWeight: 'bold',
              color: '#666'
            }}>
              <ThunderboltOutlined /> 思考过程：
            </div>
            <MarkdownRenderer
              content={thinking}
              className="thinking-content"
            />
          </div>
        )}
      </Card>
    );
  };

  return (
    <div style={{ padding: 24, height: '100%', overflow: 'auto' }}>
      <Card title="多模型并行回答">
        {/* 问题输入区域 */}
        <Card size="small" title="问题设置" style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <TextArea
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              placeholder="请输入您的问题..."
              autoSize={{ minRows: 3, maxRows: 6 }}
            />
            
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>搭档角色：</Text>
                <Select
                  placeholder="选择搭档角色（可选）"
                  style={{ width: '100%', marginTop: 4 }}
                  value={selectedPartner?.id}
                  onChange={(value) => {
                    const partner = allPartners.find(p => p.id === value);
                    setSelectedPartner(partner);
                  }}
                  allowClear
                >
                  {allPartners.map(partner => (
                    <Option key={partner.id} value={partner.id}>
                      {partner.name}
                    </Option>
                  ))}
                </Select>
              </Col>
              
              <Col span={12}>
                <div style={{ textAlign: 'right', paddingTop: 24 }}>
                  <Space>
                    <Button
                      type="primary"
                      icon={<SendOutlined />}
                      onClick={sendToAllModels}
                      loading={isProcessing}
                      disabled={!question.trim() || selectedModels.length === 0}
                    >
                      发送给所有模型
                    </Button>
                    
                    <Button
                      icon={<DownloadOutlined />}
                      onClick={exportResults}
                      disabled={responses.size === 0}
                    >
                      导出结果
                    </Button>
                  </Space>
                </div>
              </Col>
            </Row>
          </Space>
        </Card>

        {/* 模型选择区域 */}
        <Card size="small" title="模型选择" style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Checkbox
                checked={selectedModels.length === allModels.length && allModels.length > 0}
                indeterminate={selectedModels.length > 0 && selectedModels.length < allModels.length}
                onChange={toggleSelectAll}
              >
                全选 ({selectedModels.length}/{allModels.length})
              </Checkbox>
            </Space>
          </div>
          
          <Row gutter={[16, 16]}>
            {allModels.map(model => (
              <Col key={model.id} span={8}>
                <Card
                  size="small"
                  hoverable
                  style={{
                    border: selectedModels.find(m => m.id === model.id) 
                      ? '2px solid #1890ff' 
                      : '1px solid #d9d9d9'
                  }}
                  onClick={() => toggleModelSelection(model.id)}
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Space>
                      <RobotOutlined />
                      <Text strong>{model.name}</Text>
                      {processingModels.has(model.id) && (
                        <Spin size="small" />
                      )}
                    </Space>
                    
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {model.configName}
                    </Text>
                    
                    <Space>
                      {model.supportThinking && <Tag size="small" color="blue">Thinking</Tag>}
                      {model.supportVision && <Tag size="small" color="green">Vision</Tag>}
                    </Space>
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>
        </Card>

        {/* 统计信息 */}
        {(isProcessing || responses.size > 0) && (
          <Card size="small" title="执行统计" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="总模型数"
                  value={statistics.totalModels}
                  prefix={<RobotOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="已完成"
                  value={statistics.completedModels}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="失败数"
                  value={statistics.failedModels}
                  prefix={<ExclamationCircleOutlined />}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="平均耗时"
                  value={Math.round(statistics.averageTime)}
                  suffix="ms"
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
            </Row>
            
            {isProcessing && (
              <Progress
                percent={Math.round(((statistics.completedModels + statistics.failedModels) / statistics.totalModels) * 100)}
                status="active"
                style={{ marginTop: 16 }}
              />
            )}
          </Card>
        )}

        {/* 响应结果 */}
        {responses.size > 0 ? (
          <Card size="small" title="模型响应">
            <Tabs type="card">
              {Array.from(responses.values()).map((response, index) => (
                <TabPane
                  tab={
                    <Space>
                      {response.status === 'success' ? (
                        <CheckCircleOutlined style={{ color: '#52c41a' }} />
                      ) : (
                        <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                      )}
                      {response.model.name}
                    </Space>
                  }
                  key={response.model.id}
                >
                  {renderResponse(response)}
                </TabPane>
              ))}
            </Tabs>
          </Card>
        ) : (
          !isProcessing && (
            <Empty
              description="请输入问题并选择模型开始对比"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )
        )}
      </Card>
    </div>
  );
};

export default MultiModelAnswer;
