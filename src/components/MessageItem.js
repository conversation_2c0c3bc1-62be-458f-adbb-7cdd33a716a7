import React, { memo, useMemo, useState } from 'react';
import { RobotOutlined, UserOutlined, ThunderboltOutlined, EyeOutlined, CopyOutlined, FileTextOutlined, EditOutlined, CheckOutlined, CloseOutlined, DeleteOutlined, ReloadOutlined } from '@ant-design/icons';
import { Input, message, Popconfirm } from 'antd';
import MarkdownRenderer from './MarkdownRenderer';
import EnhancedMessageRenderer from './EnhancedMessageRenderer'; // 🚀 新增：增强的消息渲染器
import { ThinkingCopyButton } from './CopyButton'; // 🎯 导入复制按钮组件
import { copyToClipboard, markdownToPlainText } from '../utils/copyUtils'; // 🎯 导入复制工具函数
import KnowledgeReferences from './KnowledgeReferences'; // 🚀 新增：知识库引用组件

const { TextArea } = Input;

// 🚀 优化的消息项组件 - 使用React.memo防止不必要的重渲染
const MessageItem = memo(({
  msg,
  index,
  isUser,
  isSystem,
  isStreaming,
  showThinking,
  isScrolling = false, // 🚀 新增：滚动状态
  optimized = false,   // 🚀 新增：优化模式
  onEditMessage = null, // 🚀 新增：编辑消息回调
  onDeleteMessage = null, // 🚀 新增：删除消息回调
  onRegenerateMessage = null // 🚀 新增：重新生成消息回调
}) => {
  // 🎯 悬浮状态管理 - 滚动时禁用以提升性能
  const [isHovered, setIsHovered] = useState(false);

  // 🚀 新增：编辑状态管理
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(msg.content || '');

  // 🚀 性能优化：滚动时禁用悬浮效果
  const shouldShowHover = !isScrolling && isHovered;

  // 🚀 编辑功能处理函数
  const handleStartEdit = () => {
    setIsEditing(true);
    setEditContent(msg.content || '');
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditContent(msg.content || '');
  };

  const handleSaveEdit = async () => {
    if (!editContent.trim()) {
      message.warning('消息内容不能为空');
      return;
    }

    if (editContent.trim() === msg.content?.trim()) {
      setIsEditing(false);
      return;
    }

    try {
      if (onEditMessage) {
        await onEditMessage(msg, editContent.trim());
        setIsEditing(false);
        message.success('消息已更新');
      }
    } catch (error) {
      console.error('❌ [MessageItem] 编辑消息失败:', error);
      message.error('编辑消息失败: ' + error.message);
    }
  };

  // 🚀 新增：删除消息处理函数
  const handleDeleteMessage = async () => {
    try {
      await onDeleteMessage?.(msg);
      message.success('消息已删除');
    } catch (error) {
      console.error('❌ [MessageItem] 删除消息失败:', error);
      message.error('删除失败: ' + error.message);
    }
  };

  // 🚀 新增：重新生成消息处理函数
  const handleRegenerateMessage = async () => {
    try {
      // 🎯 修复：立即显示提示信息，不等待异步操作完成
      message.success('正在重新生成回复...');

      // 然后执行重新生成操作
      await onRegenerateMessage?.(msg);
    } catch (error) {
      console.error('❌ [MessageItem] 重新生成失败:', error);
      message.error('重新生成失败: ' + error.message);
    }
  };

  // 🎯 获取完整的消息内容（包含思考标签）
  const getFullMessageContent = useMemo(() => {
    if (!msg.content) return '';

    // 如果消息内容包含思考标签占位符，需要重建完整内容
    if (typeof msg.content === 'string' && msg.content.includes('[THINKING_PLACEHOLDER_')) {
      // 这种情况下内容已经被解析过，需要从原始内容获取
      return msg.originalContent || msg.content;
    }

    return msg.content;
  }, [msg.content, msg.originalContent]);
  // 🎯 使用useMemo缓存复杂的样式计算
  const messageStyles = useMemo(() => {
    if (isSystem) {
      // 系统消息样式
      let systemStyle = {
        background: 'rgba(26, 31, 46, 0.8)',
        color: '#ffffff',
        padding: '12px 20px',
        borderRadius: '20px',
        fontSize: '13px',
        maxWidth: '85%',
        textAlign: 'center',
        border: '1px solid rgba(0, 212, 255, 0.3)',
        boxShadow: '0 4px 20px rgba(0, 212, 255, 0.1)',
        backdropFilter: 'blur(20px)',
        position: 'relative',
        overflow: 'hidden'
      };

      // 根据消息类型调整样式
      if (msg.type === 'user_interrupt') {
        systemStyle = {
          ...systemStyle,
          background: 'rgba(255, 165, 0, 0.2)',
          color: '#ffa500',
          border: '1px solid rgba(255, 165, 0, 0.5)',
          boxShadow: '0 4px 20px rgba(255, 165, 0, 0.2)',
          textShadow: '0 0 8px rgba(255, 165, 0, 0.3)'
        };
      } else if (msg.type === 'discussion_end') {
        systemStyle = {
          ...systemStyle,
          background: 'rgba(82, 196, 26, 0.2)',
          color: '#52c41a',
          border: '1px solid rgba(82, 196, 26, 0.5)',
          boxShadow: '0 4px 20px rgba(82, 196, 26, 0.2)',
          textShadow: '0 0 8px rgba(82, 196, 26, 0.3)'
        };
      } else if (msg.type === 'error' || msg.type === 'failure') {
        systemStyle = {
          ...systemStyle,
          background: 'rgba(255, 107, 107, 0.2)',
          color: '#ff6b6b',
          border: '1px solid rgba(255, 107, 107, 0.5)',
          boxShadow: '0 4px 20px rgba(255, 107, 107, 0.2)',
          textShadow: '0 0 8px rgba(255, 107, 107, 0.3)'
        };
      } else {
        systemStyle = {
          ...systemStyle,
          background: 'rgba(0, 212, 255, 0.1)',
          color: '#00d4ff',
          border: '1px solid rgba(0, 212, 255, 0.4)',
          boxShadow: '0 4px 20px rgba(0, 212, 255, 0.2)',
          textShadow: '0 0 8px rgba(0, 212, 255, 0.3)'
        };
      }

      return { systemStyle };
    }

    // 普通消息样式
    const messageStyle = {
      maxWidth: '75%',
      padding: '16px 20px',
      borderRadius: isUser ? '20px 20px 4px 20px' : '20px 20px 20px 4px',
      background: isUser 
        ? 'linear-gradient(135deg, rgba(138, 43, 226, 0.9) 0%, rgba(138, 43, 226, 0.7) 100%)'
        : 'rgba(26, 31, 46, 0.8)',
      border: isUser 
        ? '1px solid rgba(138, 43, 226, 0.5)'
        : '1px solid rgba(0, 212, 255, 0.3)',
      boxShadow: isUser
        ? '0 8px 32px rgba(138, 43, 226, 0.3)'
        : '0 8px 32px rgba(0, 212, 255, 0.2)',
      backdropFilter: 'blur(20px)',
      position: 'relative',
      overflow: 'hidden'
    };

    return { messageStyle };
  }, [isUser, isSystem, msg.type]);

  // 🎯 系统消息渲染
  if (isSystem) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        margin: '20px 0'
      }}>
        <div style={messageStyles.systemStyle}>
          {/* 系统消息背景动画 */}
          <div className="shimmer-bg" />
          <div style={{ position: 'relative', zIndex: 1 }}>
            <MarkdownRenderer content={msg.content} />
          </div>
        </div>
      </div>
    );
  }

  // 🎯 普通消息渲染
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: isUser ? 'flex-end' : 'flex-start',
        margin: '16px 0',
        alignItems: 'flex-start'
      }}
      onMouseEnter={() => !isScrolling && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* AI模型头像 */}
      {!isUser && (
        <div className="ai-avatar">
          <RobotOutlined style={{ fontSize: '18px', color: '#ffffff' }} />
        </div>
      )}

      {/* 消息内容 */}
      <div style={messageStyles.messageStyle}>
        {/* 消息背景动画 */}
        <div className="message-shimmer" />



        {/* 发送者信息 */}
        {!isUser && (
          <div className="sender-info">
            <RobotOutlined style={{ fontSize: '12px' }} />
            <span>{msg.sender}</span>
            {isStreaming && <span className="streaming-indicator">正在输入...</span>}
          </div>
        )}

        {/* 消息内容 - 用户消息支持编辑 */}
        <div style={{ position: 'relative', zIndex: 1 }}>
          {isUser && isEditing ? (
            // 用户消息编辑模式
            <div style={{ width: '100%' }}>
              <TextArea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                autoSize={{ minRows: 2, maxRows: 8 }}
                style={{
                  background: 'rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(138, 43, 226, 0.5)',
                  borderRadius: '8px',
                  color: '#ffffff',
                  fontSize: '14px',
                  resize: 'none'
                }}
                placeholder="编辑消息内容..."
              />
              <div style={{
                display: 'flex',
                justifyContent: 'flex-end',
                gap: '8px',
                marginTop: '8px'
              }}>
                <div
                  onClick={handleCancelEdit}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    padding: '4px 8px',
                    background: 'rgba(255, 77, 79, 0.1)',
                    border: '1px solid rgba(255, 77, 79, 0.3)',
                    borderRadius: '6px',
                    color: '#ff4d4f',
                    fontSize: '12px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = 'rgba(255, 77, 79, 0.2)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = 'rgba(255, 77, 79, 0.1)';
                  }}
                >
                  <CloseOutlined style={{ fontSize: '10px' }} />
                  <span>取消</span>
                </div>
                <div
                  onClick={handleSaveEdit}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    padding: '4px 8px',
                    background: 'rgba(82, 196, 26, 0.1)',
                    border: '1px solid rgba(82, 196, 26, 0.3)',
                    borderRadius: '6px',
                    color: '#52c41a',
                    fontSize: '12px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = 'rgba(82, 196, 26, 0.2)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = 'rgba(82, 196, 26, 0.1)';
                  }}
                >
                  <CheckOutlined style={{ fontSize: '10px' }} />
                  <span>保存</span>
                </div>
              </div>
            </div>
          ) : (
            // 正常显示模式
            <EnhancedMessageRenderer
              content={msg.content}
              showThinking={showThinking}
              defaultThinkingExpanded={false}
              className="message-content"
            />
          )}
        </div>

        {/* 思考过程 */}
        {msg.thinking && showThinking && (
          <div className="thinking-section">
            {/* 🎯 思考过程复制按钮 */}
            {shouldShowHover && (
              <ThinkingCopyButton
                content={msg.thinking}
                className="thinking-copy"
              />
            )}

            <div className="thinking-header">
              <ThunderboltOutlined />
              <span>思考过程</span>
            </div>
            <div className="thinking-content">
              <MarkdownRenderer content={msg.thinking} className="thinking-content" />
            </div>
          </div>
        )}

        {/* 调度信息 */}
        {msg.schedulingInfo && msg.schedulingInfo.reasoning && (
          <div className="scheduling-info">
            <EyeOutlined />
            <span>{msg.schedulingInfo.reasoning}</span>
          </div>
        )}

        {/* 时间戳 */}
        <div className="timestamp">
          {new Date(msg.timestamp).toLocaleTimeString()}
        </div>

        {/* 🚀 新增：知识库引用组件 */}
        {!isUser && msg.knowledgeReferences && msg.knowledgeReferences.length > 0 && (
          <KnowledgeReferences
            references={msg.knowledgeReferences}
            style={{ marginTop: '12px' }}
          />
        )}

        {/* 🎯 AI消息底部操作按钮区域 - 始终可见 */}
        {!isUser && (
          <div style={{
            display: 'flex',
            gap: '8px',
            marginTop: '12px',
            paddingTop: '8px',
            borderTop: '1px solid rgba(0, 212, 255, 0.1)',
            opacity: 1,
            transform: 'translateY(0)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            position: 'relative',
            zIndex: 2,
            flexWrap: 'wrap'
          }}>
            {/* 复制原始内容（包含Markdown格式和思考标签） */}
            <div
              onClick={(e) => {
                e.stopPropagation();
                copyToClipboard(getFullMessageContent, '原始内容已复制到剪贴板');
              }}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
                padding: '4px 8px',
                background: 'rgba(0, 212, 255, 0.1)',
                border: '1px solid rgba(0, 212, 255, 0.3)',
                borderRadius: '6px',
                color: '#00d4ff',
                fontSize: '11px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                fontWeight: '500'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'rgba(0, 212, 255, 0.2)';
                e.target.style.borderColor = '#00d4ff';
                e.target.style.boxShadow = '0 0 8px rgba(0, 212, 255, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'rgba(0, 212, 255, 0.1)';
                e.target.style.borderColor = 'rgba(0, 212, 255, 0.3)';
                e.target.style.boxShadow = 'none';
              }}
            >
              <CopyOutlined style={{ fontSize: '10px' }} />
              <span>复制原文</span>
            </div>

            {/* 复制纯文本内容 */}
            <div
              onClick={(e) => {
                e.stopPropagation();
                // 🎯 使用Markdown转纯文本函数，处理完整内容
                const plainText = markdownToPlainText(getFullMessageContent);
                copyToClipboard(plainText, '纯文本已复制到剪贴板');
              }}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
                padding: '4px 8px',
                background: 'rgba(138, 43, 226, 0.1)',
                border: '1px solid rgba(138, 43, 226, 0.3)',
                borderRadius: '6px',
                color: '#8a2be2',
                fontSize: '11px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                fontWeight: '500'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'rgba(138, 43, 226, 0.2)';
                e.target.style.borderColor = '#8a2be2';
                e.target.style.boxShadow = '0 0 8px rgba(138, 43, 226, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'rgba(138, 43, 226, 0.1)';
                e.target.style.borderColor = 'rgba(138, 43, 226, 0.3)';
                e.target.style.boxShadow = 'none';
              }}
            >
              <FileTextOutlined style={{ fontSize: '10px' }} />
              <span>复制文本</span>
            </div>

            {/* 🚀 新增：删除按钮 */}
            {onDeleteMessage && (
              <Popconfirm
                title="确定删除这条消息吗？"
                description="此操作不可撤销"
                onConfirm={handleDeleteMessage}
                okText="确定删除"
                cancelText="取消"
                okButtonProps={{ danger: true }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    padding: '4px 8px',
                    background: 'rgba(245, 34, 45, 0.1)',
                    border: '1px solid rgba(245, 34, 45, 0.3)',
                    borderRadius: '6px',
                    color: '#f5222d',
                    fontSize: '11px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    fontWeight: '500'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = 'rgba(245, 34, 45, 0.2)';
                    e.target.style.borderColor = '#f5222d';
                    e.target.style.boxShadow = '0 0 8px rgba(245, 34, 45, 0.3)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = 'rgba(245, 34, 45, 0.1)';
                    e.target.style.borderColor = 'rgba(245, 34, 45, 0.3)';
                    e.target.style.boxShadow = 'none';
                  }}
                >
                  <DeleteOutlined style={{ fontSize: '10px' }} />
                  <span>删除</span>
                </div>
              </Popconfirm>
            )}

            {/* 🚀 新增：重新生成按钮 */}
            {onRegenerateMessage && (
              <div
                onClick={(e) => {
                  e.stopPropagation();
                  handleRegenerateMessage();
                }}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  padding: '4px 8px',
                  background: 'rgba(250, 140, 22, 0.1)',
                  border: '1px solid rgba(250, 140, 22, 0.3)',
                  borderRadius: '6px',
                  color: '#fa8c16',
                  fontSize: '11px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  fontWeight: '500'
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = 'rgba(250, 140, 22, 0.2)';
                  e.target.style.borderColor = '#fa8c16';
                  e.target.style.boxShadow = '0 0 8px rgba(250, 140, 22, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = 'rgba(250, 140, 22, 0.1)';
                  e.target.style.borderColor = 'rgba(250, 140, 22, 0.3)';
                  e.target.style.boxShadow = 'none';
                }}
              >
                <ReloadOutlined style={{ fontSize: '10px' }} />
                <span>重新生成</span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 用户头像和编辑按钮 */}
      {isUser && (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
          <div className="user-avatar">
            <UserOutlined style={{ fontSize: '18px', color: '#ffffff' }} />
          </div>

          {/* 用户消息编辑按钮 */}
          {shouldShowHover && !isEditing && onEditMessage && (
            <div
              onClick={handleStartEdit}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '24px',
                height: '24px',
                background: 'rgba(138, 43, 226, 0.1)',
                border: '1px solid rgba(138, 43, 226, 0.3)',
                borderRadius: '6px',
                color: '#8a2be2',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                opacity: shouldShowHover ? 1 : 0,
                transform: shouldShowHover ? 'scale(1)' : 'scale(0.8)'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'rgba(138, 43, 226, 0.2)';
                e.target.style.borderColor = '#8a2be2';
                e.target.style.boxShadow = '0 0 8px rgba(138, 43, 226, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'rgba(138, 43, 226, 0.1)';
                e.target.style.borderColor = 'rgba(138, 43, 226, 0.3)';
                e.target.style.boxShadow = 'none';
              }}
              title="编辑消息"
            >
              <EditOutlined style={{ fontSize: '12px' }} />
            </div>
          )}
        </div>
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  // 🎯 自定义比较函数，只在关键属性变化时重渲染
  return (
    prevProps.msg.id === nextProps.msg.id &&
    prevProps.msg.content === nextProps.msg.content &&
    prevProps.msg.thinking === nextProps.msg.thinking &&
    prevProps.msg.isStreaming === nextProps.msg.isStreaming &&
    prevProps.showThinking === nextProps.showThinking &&
    // 🚀 新增：比较知识库引用
    JSON.stringify(prevProps.msg.knowledgeReferences) === JSON.stringify(nextProps.msg.knowledgeReferences)
  );
});

MessageItem.displayName = 'MessageItem';

export default MessageItem;
