import React, { useState, useEffect, useCallback } from 'react';
import { Layout, Menu, theme, ConfigProvider, FloatButton, message } from 'antd';
import {
  SettingOutlined,
  MessageOutlined,
  TeamOutlined,
  UserOutlined,
  RobotOutlined,
  ThunderboltOutlined,
  ExperimentOutlined,
  ApiOutlined,
  DatabaseOutlined,
  ControlOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';

// 导入各个模块组件
import ModelConfig from './components/ModelConfig';
import ChatRoom from './components/ChatRoom';
import MultiModelAnswer from './components/MultiModelAnswer';
import PartnerConfig from './components/PartnerConfig';
import KnowledgeBase from './components/KnowledgeBase'; // 🚀 新增：知识库组件
import ErrorBoundary from './components/ErrorBoundary';
import ShortcutsHelp from './components/ShortcutsHelp'; // 🎯 新增：快捷键帮助组件

// 导入数据管理和快捷键管理
import { DataManager } from './utils/dataManager';
import { globalShortcuts } from './utils/keyboardShortcuts'; // 🎯 新增：快捷键管理器

// 🎨 导入科技感主题样式
import './styles/techTheme.css';

const { Header, Sider, Content } = Layout;

const App = () => {
  const [selectedKey, setSelectedKey] = useState('chatroom');
  const [dataManager, setDataManager] = useState(null);
  
  // 🎯 快捷键帮助状态
  const [showShortcutsHelp, setShowShortcutsHelp] = useState(false);
  
  // 🎯 获取ChatRoom组件的引用，用于快捷键控制
  const [chatRoomRef, setChatRoomRef] = useState(null);

  // 🎨 科技感主题配置
  const techTheme = {
    token: {
      colorPrimary: '#00d4ff',
      colorBgContainer: '#0a0e1a',
      colorBgElevated: '#1a1f2e',
      colorBorder: '#2a3441',
      colorText: '#ffffff',
      colorTextSecondary: '#8892b0',
      borderRadius: 8,
      boxShadow: '0 8px 32px rgba(0, 212, 255, 0.1)',
    },
    algorithm: theme.darkAlgorithm,
  };

  // 🔧 获取主题token值（用于向后兼容）
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  // 初始化数据管理器
  useEffect(() => {
    const initDataManager = async () => {
      const dm = new DataManager();
      await dm.initialize();
      setDataManager(dm);
    };
    
    initDataManager();
  }, []);

  // 🎯 快捷键处理函数
  const handleNavigationShortcut = useCallback((targetKey) => {
    setSelectedKey(targetKey);
    message.success(`已切换到${getModuleName(targetKey)}`);
  }, []);

  const handleToggleClipboard = useCallback(() => {
    if (selectedKey === 'chatroom' && chatRoomRef?.toggleClipboard) {
      chatRoomRef.toggleClipboard();
    } else {
      message.info('智能剪贴板功能仅在智能对话模块中可用');
    }
  }, [selectedKey, chatRoomRef]);

  const handleNewSession = useCallback(() => {
    if (selectedKey === 'chatroom' && chatRoomRef?.createNewSession) {
      chatRoomRef.createNewSession();
    } else if (selectedKey === 'multimodel' && dataManager) {
      // 多模型对比新建会话逻辑可以后续添加
      message.info('新建会话功能开发中...');
    } else {
      message.info('新建会话功能仅在对话模块中可用');
    }
  }, [selectedKey, chatRoomRef, dataManager]);

  const handleSaveSession = useCallback(() => {
    if (dataManager) {
      dataManager.saveToStorage();
      message.success('会话已保存');
    }
  }, [dataManager]);

  const handleRefresh = useCallback(() => {
    window.location.reload();
  }, []);

  const handleShowShortcuts = useCallback(() => {
    setShowShortcutsHelp(true);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setShowShortcutsHelp(false);
  }, []);

  const handleQuitApp = useCallback(() => {
    if (window.electronAPI?.app?.quit) {
      window.electronAPI.app.quit();
    }
  }, []);

  const handleToggleFullscreen = useCallback(() => {
    if (window.electronAPI?.window?.toggleFullscreen) {
      window.electronAPI.window.toggleFullscreen();
    }
  }, []);

  const handleToggleDevtools = useCallback(() => {
    if (window.electronAPI?.window?.toggleDevtools) {
      window.electronAPI.window.toggleDevtools();
    }
  }, []);

  // 获取模块名称
  const getModuleName = (key) => {
    const moduleNames = {
      'chatroom': '智能对话',
      'multimodel': '模型对比',
      'knowledge': '知识库',
      'config': '模型管理',
      'partner': '协作配置'
    };
    return moduleNames[key] || '未知模块';
  };

  // 🎯 注册快捷键监听器
  useEffect(() => {
    if (!dataManager) return;

    // 注册快捷键回调
    globalShortcuts.register('switch_to_chat', () => handleNavigationShortcut('chatroom'));
    globalShortcuts.register('switch_to_multimodel', () => handleNavigationShortcut('multimodel'));
    globalShortcuts.register('switch_to_knowledge', () => handleNavigationShortcut('knowledge'));
    globalShortcuts.register('switch_to_config', () => handleNavigationShortcut('config'));
    globalShortcuts.register('switch_to_partner', () => handleNavigationShortcut('partner'));
    
    globalShortcuts.register('toggle_clipboard', handleToggleClipboard);
    globalShortcuts.register('new_session', handleNewSession);
    globalShortcuts.register('save_session', handleSaveSession);
    globalShortcuts.register('refresh', handleRefresh);
    globalShortcuts.register('show_shortcuts', handleShowShortcuts);
    globalShortcuts.register('close_dialog', handleCloseDialog);
    globalShortcuts.register('quit_app', handleQuitApp);
    globalShortcuts.register('toggle_fullscreen', handleToggleFullscreen);
    globalShortcuts.register('toggle_devtools', handleToggleDevtools);

    // 启用快捷键监听
    globalShortcuts.enable();

    // 清理函数
    return () => {
      globalShortcuts.disable();
    };
  }, [
    dataManager,
    handleNavigationShortcut,
    handleToggleClipboard,
    handleNewSession,
    handleSaveSession,
    handleRefresh,
    handleShowShortcuts,
    handleCloseDialog,
    handleQuitApp,
    handleToggleFullscreen,
    handleToggleDevtools
  ]);

  // 🎨 科技感菜单项配置
  const menuItems = [
    {
      key: 'chatroom',
      icon: <RobotOutlined style={{ fontSize: '16px' }} />,
      label: '智能对话',
    },
    {
      key: 'multimodel',
      icon: <ThunderboltOutlined style={{ fontSize: '16px' }} />,
      label: '模型对比',
    },
    {
      key: 'knowledge',
      icon: <DatabaseOutlined style={{ fontSize: '16px' }} />,
      label: '知识库',
    },
    {
      key: 'config',
      icon: <ExperimentOutlined style={{ fontSize: '16px' }} />,
      label: '模型管理',
    },
    {
      key: 'partner',
      icon: <ApiOutlined style={{ fontSize: '16px' }} />,
      label: '协作配置',
    },
  ];

  // 渲染主内容区域
  const renderContent = () => {
    if (!dataManager) {
      return <div style={{ padding: 24, textAlign: 'center' }}>正在初始化...</div>;
    }

    switch (selectedKey) {
      case 'config':
        return (
          <ErrorBoundary>
            <ModelConfig dataManager={dataManager} />
          </ErrorBoundary>
        );
      case 'chatroom':
        return (
          <ErrorBoundary>
            <ChatRoom dataManager={dataManager} ref={setChatRoomRef} />
          </ErrorBoundary>
        );
      case 'multimodel':
        return (
          <ErrorBoundary>
            <MultiModelAnswer dataManager={dataManager} />
          </ErrorBoundary>
        );
      case 'knowledge':
        return (
          <ErrorBoundary>
            <KnowledgeBase dataManager={dataManager} />
          </ErrorBoundary>
        );
      case 'partner':
        return (
          <ErrorBoundary>
            <PartnerConfig dataManager={dataManager} />
          </ErrorBoundary>
        );
      default:
        return (
          <ErrorBoundary>
            <ChatRoom dataManager={dataManager} />
          </ErrorBoundary>
        );
    }
  };

  return (
    <ConfigProvider theme={techTheme}>
      <div style={{
        background: 'linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0a0e1a 100%)',
        minHeight: '100vh'
      }}>
        <Layout style={{
          height: '100vh',
          background: 'transparent'
        }}>
          <Sider
            trigger={null}
            collapsible
            collapsed={false}
            style={{
              background: 'rgba(26, 31, 46, 0.95)',
              backdropFilter: 'blur(20px)',
              borderRight: '1px solid rgba(0, 212, 255, 0.2)',
              boxShadow: '4px 0 20px rgba(0, 212, 255, 0.1)'
            }}
          >
        <div style={{
          height: 80,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: '1px solid rgba(0, 212, 255, 0.3)',
          padding: '0 16px',
          background: 'linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(138, 43, 226, 0.1) 100%)',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* 🎨 科技感背景动画 */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(45deg, transparent 30%, rgba(0, 212, 255, 0.05) 50%, transparent 70%)',
            animation: 'shimmer 3s ease-in-out infinite',
            transform: 'translateX(-100%)'
          }} />

          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '4px',
            zIndex: 1
          }}>
            <img
              src={process.env.PUBLIC_URL + "/AiChatLogo.png"}
              alt="JDCAIChat Logo"
              style={{
                height: 36,
                width: 'auto',
                maxWidth: '100%',
                objectFit: 'contain',
                filter: 'drop-shadow(0 0 10px rgba(0, 212, 255, 0.5))',
                transition: 'all 0.3s ease'
              }}
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.parentNode.innerHTML = `
                  <div style="
                    color: #00d4ff;
                    font-size: 18px;
                    font-weight: bold;
                    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
                    letter-spacing: 1px;
                  ">
                    JDCAIChat
                  </div>
                `;
              }}
            />
            <div style={{
              fontSize: '10px',
              color: 'rgba(0, 212, 255, 0.8)',
              fontWeight: '500',
              letterSpacing: '2px',
              textTransform: 'uppercase'
            }}>
              AI STUDIO
            </div>
          </div>
        </div>
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          items={menuItems}
          onClick={({ key }) => setSelectedKey(key)}
          style={{
            borderRight: 0,
            background: 'transparent',
            fontSize: '14px',
            fontWeight: '500'
          }}
          theme="dark"
        />
      </Sider>
      
      <Layout>
        <Header
          style={{
            padding: '0 24px',
            background: 'rgba(26, 31, 46, 0.95)',
            backdropFilter: 'blur(20px)',
            borderBottom: '1px solid rgba(0, 212, 255, 0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 2px 20px rgba(0, 212, 255, 0.1)',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* 🎨 Header背景动画 */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(90deg, transparent 0%, rgba(0, 212, 255, 0.03) 50%, transparent 100%)',
            animation: 'pulse 4s ease-in-out infinite'
          }} />

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            zIndex: 1
          }}>
            <div style={{
              width: '4px',
              height: '24px',
              background: 'linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%)',
              borderRadius: '2px',
              boxShadow: '0 0 10px rgba(0, 212, 255, 0.5)'
            }} />
            <img
              src={process.env.PUBLIC_URL + "/AiChatLogo.png"}
              alt="JDCAIChat"
              style={{
                height: 28,
                width: 'auto',
                objectFit: 'contain',
                filter: 'drop-shadow(0 0 8px rgba(0, 212, 255, 0.3))'
              }}
            />
            <span style={{
              fontSize: 18,
              fontWeight: 600,
              color: '#ffffff',
              textShadow: '0 0 10px rgba(0, 212, 255, 0.3)',
              letterSpacing: '0.5px'
            }}>
              {menuItems.find(item => item.key === selectedKey)?.label}
            </span>
          </div>

          <div style={{
            fontSize: 13,
            color: 'rgba(0, 212, 255, 0.8)',
            fontWeight: '500',
            letterSpacing: '1px',
            textTransform: 'uppercase',
            zIndex: 1
          }}>
            AI Collaboration Platform
          </div>
        </Header>
        
        <Content
          style={{
            margin: 0,
            padding: 0,
            background: 'rgba(10, 14, 26, 0.8)',
            backdropFilter: 'blur(10px)',
            overflow: 'hidden',
            position: 'relative'
          }}
        >
          {/* 🎨 Content背景装饰 */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(138, 43, 226, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 40% 60%, rgba(0, 212, 255, 0.03) 0%, transparent 50%)
            `,
            pointerEvents: 'none'
          }} />

          <div style={{ position: 'relative', zIndex: 1, height: '100%' }}>
            {renderContent()}
          </div>
        </Content>
      </Layout>
        </Layout>
        
        {/* 🎯 快捷键帮助浮动按钮 */}
        <FloatButton.Group
          trigger="click"
          type="primary"
          style={{ right: 24, bottom: 24 }}
          icon={<QuestionCircleOutlined />}
        >
          <FloatButton
            icon={<ControlOutlined />}
            tooltip="快捷键帮助 (Ctrl+O)"
            onClick={handleShowShortcuts}
            style={{
              background: 'rgba(0, 212, 255, 0.9)',
              borderColor: 'rgba(0, 212, 255, 0.5)'
            }}
          />
        </FloatButton.Group>

        {/* 🎯 快捷键帮助对话框 */}
        <ShortcutsHelp
          visible={showShortcutsHelp}
          onClose={() => setShowShortcutsHelp(false)}
          shortcuts={globalShortcuts.getAllShortcuts()}
        />
      </div>
    </ConfigProvider>
  );
};

export default App;
