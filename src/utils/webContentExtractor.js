/**
 * 网页内容提取服务
 * 用于提取搜索结果页面的详细内容
 */

class WebContentExtractor {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 10 * 60 * 1000; // 10分钟缓存
    this.maxContentLength = 5000; // 最大内容长度
    this.requestTimeout = 8000; // 请求超时时间
  }

  /**
   * 提取网页内容
   * @param {string} url - 网页URL
   * @param {Object} options - 提取选项
   * @returns {Promise<Object>} 提取的内容
   */
  async extractContent(url, options = {}) {
    const {
      maxLength = this.maxContentLength,
      includeImages = false,
      includeLinks = false
    } = options;

    // 检查缓存
    const cacheKey = `${url}_${JSON.stringify(options)}`;
    const cachedContent = this.getFromCache(cacheKey);
    if (cachedContent) {
      console.log('🎯 [WebExtractor] 使用缓存内容:', url);
      return cachedContent;
    }

    try {
      console.log('📄 [WebExtractor] 开始提取内容:', url);
      
      // 获取网页HTML
      const html = await this.fetchPageContent(url);
      
      // 解析和提取内容
      const content = this.parseContent(html, {
        maxLength,
        includeImages,
        includeLinks
      });
      
      // 缓存结果
      this.setCache(cacheKey, content);
      
      console.log(`✅ [WebExtractor] 内容提取完成，长度: ${content.text?.length || 0}`);
      return content;

    } catch (error) {
      console.error('❌ [WebExtractor] 内容提取失败:', error);
      return {
        url,
        title: '',
        text: '',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 获取网页HTML内容
   */
  async fetchPageContent(url) {
    try {
      // 🌐 使用Electron的IPC通信进行网络请求，避免CORS问题
      if (window.electronAPI && window.electronAPI.webSearch) {
        console.log('📄 [WebExtractor] 使用Electron IPC进行内容提取');

        const response = await window.electronAPI.webSearch.extract(url, {
          timeout: this.requestTimeout
        });

        if (response.statusCode !== 200) {
          throw new Error(`HTTP ${response.statusCode}: 请求失败`);
        }

        return response.data;
      } else {
        // 降级到fetch（用于开发环境或非Electron环境）
        console.log('📄 [WebExtractor] 使用fetch进行内容提取（可能遇到CORS问题）');

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

        try {
          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
              'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'Accept-Encoding': 'gzip, deflate, br',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            },
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const contentType = response.headers.get('content-type') || '';
          if (!contentType.includes('text/html')) {
            throw new Error('不支持的内容类型: ' + contentType);
          }

          return await response.text();

        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * 解析网页内容
   */
  parseContent(html, options) {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      
      // 移除不需要的元素
      this.removeUnwantedElements(doc);
      
      // 提取标题
      const title = this.extractTitle(doc);
      
      // 提取主要文本内容
      const text = this.extractMainText(doc, options.maxLength);
      
      // 提取其他信息
      const result = {
        title: title,
        text: text,
        url: doc.location?.href || '',
        timestamp: new Date().toISOString(),
        wordCount: text.split(/\s+/).length,
        language: this.detectLanguage(text)
      };
      
      // 可选：提取图片
      if (options.includeImages) {
        result.images = this.extractImages(doc);
      }
      
      // 可选：提取链接
      if (options.includeLinks) {
        result.links = this.extractLinks(doc);
      }
      
      return result;
      
    } catch (error) {
      console.error('❌ [WebExtractor] 解析内容失败:', error);
      throw error;
    }
  }

  /**
   * 移除不需要的HTML元素
   */
  removeUnwantedElements(doc) {
    const unwantedSelectors = [
      'script', 'style', 'nav', 'header', 'footer',
      '.advertisement', '.ads', '.sidebar', '.menu',
      '.navigation', '.breadcrumb', '.social-share',
      '.comments', '.related-posts', '.popup',
      '[role="banner"]', '[role="navigation"]',
      '[role="complementary"]', '[role="contentinfo"]'
    ];
    
    unwantedSelectors.forEach(selector => {
      const elements = doc.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });
  }

  /**
   * 提取页面标题
   */
  extractTitle(doc) {
    // 尝试多种方式获取标题
    const titleSources = [
      () => doc.querySelector('h1')?.textContent,
      () => doc.querySelector('title')?.textContent,
      () => doc.querySelector('[property="og:title"]')?.content,
      () => doc.querySelector('[name="twitter:title"]')?.content,
      () => doc.querySelector('.title, .headline, .post-title')?.textContent
    ];
    
    for (const getTitle of titleSources) {
      try {
        const title = getTitle();
        if (title && title.trim()) {
          return this.cleanText(title.trim());
        }
      } catch (error) {
        // 继续尝试下一个方法
      }
    }
    
    return '无标题';
  }

  /**
   * 提取主要文本内容
   */
  extractMainText(doc, maxLength) {
    // 尝试找到主要内容区域
    const contentSelectors = [
      'article',
      '[role="main"]',
      '.content',
      '.post-content',
      '.article-content',
      '.main-content',
      '#content',
      '#main',
      '.entry-content'
    ];
    
    let mainContent = null;
    
    // 尝试找到主要内容容器
    for (const selector of contentSelectors) {
      const element = doc.querySelector(selector);
      if (element) {
        mainContent = element;
        break;
      }
    }
    
    // 如果没找到特定容器，使用body
    if (!mainContent) {
      mainContent = doc.body;
    }
    
    if (!mainContent) {
      return '';
    }
    
    // 提取文本内容
    const textContent = this.extractTextFromElement(mainContent);
    
    // 清理和截断文本
    const cleanedText = this.cleanText(textContent);
    
    if (cleanedText.length > maxLength) {
      return cleanedText.substring(0, maxLength) + '...';
    }
    
    return cleanedText;
  }

  /**
   * 从元素中提取文本
   */
  extractTextFromElement(element) {
    const textParts = [];
    
    // 递归提取文本，保持段落结构
    const extractText = (node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent.trim();
        if (text) {
          textParts.push(text);
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const tagName = node.tagName.toLowerCase();
        
        // 在块级元素前后添加换行
        if (['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'br'].includes(tagName)) {
          if (textParts.length > 0 && !textParts[textParts.length - 1].endsWith('\n')) {
            textParts.push('\n');
          }
        }
        
        // 递归处理子节点
        for (const child of node.childNodes) {
          extractText(child);
        }
        
        // 在块级元素后添加换行
        if (['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li'].includes(tagName)) {
          if (textParts.length > 0 && !textParts[textParts.length - 1].endsWith('\n')) {
            textParts.push('\n');
          }
        }
      }
    };
    
    extractText(element);
    return textParts.join('');
  }

  /**
   * 清理文本内容
   */
  cleanText(text) {
    if (!text) return '';
    
    return text
      // 移除多余的空白字符
      .replace(/\s+/g, ' ')
      // 移除多余的换行
      .replace(/\n\s*\n/g, '\n')
      // 移除首尾空白
      .trim();
  }

  /**
   * 检测文本语言
   */
  detectLanguage(text) {
    if (!text) return 'unknown';
    
    // 简单的语言检测
    const chineseChars = text.match(/[\u4e00-\u9fff]/g);
    const englishChars = text.match(/[a-zA-Z]/g);
    
    const chineseRatio = chineseChars ? chineseChars.length / text.length : 0;
    const englishRatio = englishChars ? englishChars.length / text.length : 0;
    
    if (chineseRatio > 0.3) return 'zh';
    if (englishRatio > 0.5) return 'en';
    
    return 'unknown';
  }

  /**
   * 提取图片信息
   */
  extractImages(doc) {
    const images = [];
    const imgElements = doc.querySelectorAll('img');
    
    imgElements.forEach(img => {
      const src = img.src || img.getAttribute('data-src');
      const alt = img.alt || '';
      
      if (src && !src.startsWith('data:')) {
        images.push({
          src: src,
          alt: alt,
          width: img.width || null,
          height: img.height || null
        });
      }
    });
    
    return images.slice(0, 10); // 最多返回10张图片
  }

  /**
   * 提取链接信息
   */
  extractLinks(doc) {
    const links = [];
    const linkElements = doc.querySelectorAll('a[href]');
    
    linkElements.forEach(link => {
      const href = link.href;
      const text = link.textContent.trim();
      
      if (href && text && !href.startsWith('#') && !href.startsWith('javascript:')) {
        links.push({
          url: href,
          text: text
        });
      }
    });
    
    return links.slice(0, 20); // 最多返回20个链接
  }

  /**
   * 缓存管理方法
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
    this.cleanExpiredCache();
  }

  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= this.cacheTimeout) {
        this.cache.delete(key);
      }
    }
  }

  clearCache() {
    this.cache.clear();
  }
}

export default WebContentExtractor;
