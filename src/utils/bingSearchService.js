/**
 * BING网页搜索服务
 * 使用网页爬取方式实现BING搜索功能
 */

class BingSearchService {
  constructor() {
    this.baseUrl = 'https://www.bing.com/search';
    this.cache = new Map(); // 搜索结果缓存
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    this.maxRetries = 3;
    this.requestDelay = 1000; // 请求间隔
    this.lastRequestTime = 0;
  }

  /**
   * 执行BING搜索
   * @param {string} query - 搜索查询
   * @param {Object} options - 搜索选项
   * @returns {Promise<Array>} 搜索结果
   */
  async search(query, options = {}) {
    const {
      maxResults = 10,
      language = 'zh-CN',
      region = 'CN',
      freshness = null, // 'Day', 'Week', 'Month'
      safeSearch = 'Moderate' // 'Off', 'Moderate', 'Strict'
    } = options;

    // 检查缓存
    const cacheKey = this.generateCacheKey(query, options);
    const cachedResult = this.getFromCache(cacheKey);
    if (cachedResult) {
      console.log('🎯 [BingSearch] 使用缓存结果:', query);
      return cachedResult;
    }

    try {
      console.log('🔍 [BingSearch] 开始搜索:', query);
      
      // 控制请求频率
      await this.throttleRequest();

      // 构建搜索URL
      const searchUrl = this.buildSearchUrl(query, {
        count: maxResults,
        setLang: language,
        cc: region,
        freshness,
        safeSearch
      });

      console.log('🌐 [BingSearch] 搜索URL:', searchUrl);

      // 执行搜索请求
      const results = await this.performSearch(searchUrl, maxResults);
      
      // 缓存结果
      this.setCache(cacheKey, results);
      
      console.log(`✅ [BingSearch] 搜索完成，找到 ${results.length} 个结果`);
      return results;

    } catch (error) {
      console.error('❌ [BingSearch] 搜索失败:', error);
      throw new Error(`搜索失败: ${error.message}`);
    }
  }

  /**
   * 构建搜索URL - 使用简化的直接方式
   */
  buildSearchUrl(query, params) {
    // 🔧 使用简化的URL构建方式，参考您提供的格式
    const url = new URL(this.baseUrl);

    // 基本搜索参数
    url.searchParams.set('q', query);
    url.searchParams.set('count', params.count || 5);
    url.searchParams.set('setlang', params.setLang || 'zh-CN');
    url.searchParams.set('cc', params.cc || 'CN');
    url.searchParams.set('safesearch', params.safeSearch || 'Moderate');
    url.searchParams.set('form', 'QBRE');
    url.searchParams.set('first', '1');
    url.searchParams.set('mkt', 'zh-CN'); // 添加市场参数

    // 可选参数
    if (params.freshness) {
      url.searchParams.set('freshness', params.freshness);
    }

    const finalUrl = url.toString();
    console.log('� [BingSearch] 构建搜索URL:', finalUrl);
    return finalUrl;
  }

  /**
   * 执行搜索请求
   */
  async performSearch(searchUrl, maxResults, redirectCount = 0) {
    const maxRedirects = 5; // 最大重定向次数
    let attempt = 0;

    console.log(`🔍 [BingSearch] 开始搜索请求 (重定向次数: ${redirectCount}/${maxRedirects})`);
    console.log(`🔍 [BingSearch] 请求URL: ${searchUrl}`);

    while (attempt < this.maxRetries) {
      try {
        // 🌐 使用Electron的IPC通信进行网络请求，避免CORS问题
        if (window.electronAPI && window.electronAPI.webSearch) {
          console.log('🔍 [BingSearch] 使用Electron IPC进行搜索请求');

          const response = await window.electronAPI.webSearch.search(searchUrl, {
            timeout: 15000, // 增加超时时间
            maxRedirects: 5, // 允许重定向
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
              'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
              'DNT': '1',
              'Connection': 'keep-alive',
              'Upgrade-Insecure-Requests': '1',
              'Cache-Control': 'max-age=0',
              'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Microsoft Edge";v="120"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"'
            }
          });

          if (response.statusCode !== 200) {
            throw new Error(`HTTP ${response.statusCode}: 请求失败`);
          }

          console.log(`✅ [BingSearch] 搜索成功，重定向次数: ${response.redirectCount || 0}`);
          console.log(`✅ [BingSearch] 最终URL: ${response.finalUrl || searchUrl}`);
          console.log(`📄 [BingSearch] 响应数据长度: ${response.data.length} 字符`);

          const results = this.parseSearchResults(response.data, maxResults);
          console.log(`🎯 [BingSearch] 解析得到 ${results.length} 个搜索结果`);
          return results;
        } else {
          // 降级到fetch（用于开发环境或非Electron环境）
          console.log('🔍 [BingSearch] 使用fetch进行搜索请求（可能遇到CORS问题）');

          const response = await fetch(searchUrl, {
            method: 'GET',
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
              'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'Accept-Encoding': 'gzip, deflate, br',
              'DNT': '1',
              'Connection': 'keep-alive',
              'Upgrade-Insecure-Requests': '1',
              'Sec-Fetch-Dest': 'document',
              'Sec-Fetch-Mode': 'navigate',
              'Sec-Fetch-Site': 'none',
              'Cache-Control': 'max-age=0'
            }
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const html = await response.text();
          return this.parseSearchResults(html, maxResults);
        }

      } catch (error) {
        // 🔧 处理重定向
        if (error.message.startsWith('REDIRECT:')) {
          const redirectUrl = error.message.substring(9);

          if (redirectCount >= maxRedirects) {
            console.error(`❌ [BingSearch] 重定向次数超过限制 (${maxRedirects})`);
            console.log('🔄 [BingSearch] 重定向失败，尝试返回空结果而不是抛出错误');
            return []; // 返回空结果而不是抛出错误
          }

          // 验证重定向URL是否有效
          if (!redirectUrl || redirectUrl.includes('[') || redirectUrl.includes('([^')) {
            console.error(`❌ [BingSearch] 无效的重定向URL: ${redirectUrl}`);
            return []; // 返回空结果
          }

          console.log(`🔄 [BingSearch] 跟随重定向 ${redirectCount + 1}/${maxRedirects}: ${redirectUrl}`);

          // 添加延迟避免过快请求
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 递归跟随重定向
          return await this.performSearch(redirectUrl, maxResults, redirectCount + 1);
        }

        attempt++;
        console.warn(`⚠️ [BingSearch] 搜索尝试 ${attempt} 失败:`, error.message);

        if (attempt >= this.maxRetries) {
          // 🔧 提供更详细的错误信息
          if (error.message.includes('302') || error.message.includes('redirect') || error.message.includes('REDIRECT')) {
            throw new Error('BING搜索被重定向，可能触发了反爬虫机制。请稍后重试或检查网络连接。');
          } else if (error.message.includes('timeout')) {
            throw new Error('搜索请求超时，请检查网络连接。');
          } else if (error.message.includes('CORS')) {
            throw new Error('跨域请求被阻止，请确保在Electron环境中运行。');
          }
          throw error;
        }

        // 🔧 根据错误类型调整重试延迟
        let retryDelay = Math.pow(2, attempt) * 1000;
        if (error.message.includes('302') || error.message.includes('redirect') || error.message.includes('REDIRECT:')) {
          retryDelay = Math.max(retryDelay, 3000); // 重定向错误至少等待3秒
        }

        console.log(`🔄 [BingSearch] ${retryDelay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }

  /**
   * 解析搜索结果HTML
   */
  parseSearchResults(html, maxResults) {
    try {
      // 🔧 检测重定向页面并尝试提取重定向URL
      if (this.isLikelyRedirectPage(html)) {
        console.warn('⚠️ [BingSearch] 检测到重定向页面，尝试提取重定向URL');
        const redirectUrl = this.extractRedirectUrl(html);
        if (redirectUrl) {
          throw new Error(`REDIRECT:${redirectUrl}`);
        } else {
          console.warn('⚠️ [BingSearch] 无法提取重定向URL，返回空结果');
          return [];
        }
      }

      // 创建DOM解析器
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      const results = [];

      // 🔧 尝试多种选择器来查找搜索结果（包括中国版BING）
      const selectors = [
        // 标准BING选择器
        '#b_results .b_algo',
        '#b_results li.b_algo',
        '.b_algo',
        '.b_algoheader',
        '#b_results .b_algoheader',
        // 中国版BING可能的选择器
        '#b_results li',
        '#b_results > li',
        '.b_results .b_algo',
        '.b_results li',
        // 通用选择器
        '[data-priority]',
        '.b_title',
        // 备用选择器
        '.results .result',
        '.web-results .result',
        '.result',
        '.sr_res',
        // 更广泛的选择器（作为最后尝试）
        'li[class*="algo"]',
        'div[class*="algo"]',
        'li[class*="result"]'
      ];

      let resultElements = [];
      for (const selector of selectors) {
        resultElements = doc.querySelectorAll(selector);
        if (resultElements.length > 0) {
          console.log(`✅ [BingSearch] 使用选择器 "${selector}" 找到 ${resultElements.length} 个结果`);
          break;
        }
      }

      if (resultElements.length === 0) {
        console.warn('⚠️ [BingSearch] 未找到搜索结果元素');
        console.log('📄 [BingSearch] HTML长度:', html.length);
        console.log('📄 [BingSearch] 检查关键字:');
        console.log('  - b_results:', html.includes('b_results'));
        console.log('  - b_algo:', html.includes('b_algo'));
        console.log('  - class="b_algo":', html.includes('class="b_algo"'));
        console.log('  - id="b_results":', html.includes('id="b_results"'));

        // 尝试查找任何可能的搜索结果容器
        const doc = new DOMParser().parseFromString(html, 'text/html');
        const anyLi = doc.querySelectorAll('li').length;
        const anyDiv = doc.querySelectorAll('div').length;
        const anyResults = doc.querySelectorAll('#b_results, .b_results, [id*="result"], [class*="result"]').length;

        console.log('📄 [BingSearch] 页面元素统计:');
        console.log('  - li元素数量:', anyLi);
        console.log('  - div元素数量:', anyDiv);
        console.log('  - 可能的结果容器:', anyResults);

        // 显示页面标题和一些关键内容
        const title = doc.querySelector('title')?.textContent || '无标题';
        console.log('📄 [BingSearch] 页面标题:', title);

        // 显示HTML的关键部分
        const bodyStart = html.indexOf('<body');
        if (bodyStart > -1) {
          const bodyContent = html.substring(bodyStart, bodyStart + 2000);
          console.log('📄 [BingSearch] Body开头内容:', bodyContent);
        }

        return [];
      }

      for (let i = 0; i < Math.min(resultElements.length, maxResults); i++) {
        const element = resultElements[i];
        const result = this.extractResultData(element);

        if (result && result.title && result.url) {
          results.push(result);
        }
      }

      console.log(`✅ [BingSearch] 成功解析 ${results.length} 个搜索结果`);
      return results;

    } catch (error) {
      console.error('❌ [BingSearch] 解析搜索结果失败:', error);
      return [];
    }
  }

  /**
   * 提取单个搜索结果数据
   */
  extractResultData(element) {
    try {
      // 🔧 尝试多种选择器来提取标题和链接（包括移动版）
      const titleSelectors = [
        'h2 a',
        '.b_algoheader a',
        'h3 a',
        '.b_title a',
        'a[href]',
        // 移动版选择器
        '.result-title a',
        '.title a',
        '.web-result-title a',
        '.sr_res h3 a'
      ];

      let titleElement = null;
      for (const selector of titleSelectors) {
        titleElement = element.querySelector(selector);
        if (titleElement) break;
      }

      const title = titleElement?.textContent?.trim();
      const url = titleElement?.href;

      // 🔧 尝试多种选择器来提取描述（包括移动版）
      const descSelectors = [
        '.b_caption p',
        '.b_descript',
        '.b_snippet',
        '.b_caption',
        'p',
        // 移动版选择器
        '.result-desc',
        '.description',
        '.web-result-desc',
        '.sr_res .b_caption'
      ];

      let description = '';
      for (const selector of descSelectors) {
        const descElement = element.querySelector(selector);
        if (descElement && descElement.textContent.trim()) {
          description = descElement.textContent.trim();
          break;
        }
      }

      // 🔧 尝试多种选择器来提取显示URL
      const urlSelectors = [
        '.b_attribution cite',
        '.b_adurl cite',
        '.b_attribution',
        'cite'
      ];

      let displayUrl = url;
      for (const selector of urlSelectors) {
        const urlElement = element.querySelector(selector);
        if (urlElement && urlElement.textContent.trim()) {
          displayUrl = urlElement.textContent.trim();
          break;
        }
      }

      // 提取时间信息（如果有）
      const timeSelectors = [
        '.b_factrow .b_attribution',
        '.b_meta',
        '.b_attribution time',
        'time'
      ];

      let publishTime = null;
      for (const selector of timeSelectors) {
        const timeElement = element.querySelector(selector);
        if (timeElement && timeElement.textContent.trim()) {
          publishTime = timeElement.textContent.trim();
          break;
        }
      }

      if (!title || !url) {
        console.warn('⚠️ [BingSearch] 缺少标题或URL:', { title, url });
        return null;
      }

      const result = {
        title: this.cleanText(title),
        url: this.cleanUrl(url),
        description: this.cleanText(description),
        displayUrl: this.cleanText(displayUrl),
        publishTime: publishTime ? this.cleanText(publishTime) : null,
        source: 'bing',
        timestamp: new Date().toISOString(),
        relevanceScore: this.calculateRelevanceScore(title, description)
      };

      console.log('✅ [BingSearch] 提取结果:', result.title);
      return result;

    } catch (error) {
      console.warn('⚠️ [BingSearch] 提取结果数据失败:', error);
      return null;
    }
  }

  /**
   * 清理文本内容
   */
  cleanText(text) {
    if (!text) return '';
    return text
      .replace(/\s+/g, ' ')
      .replace(/[\r\n\t]/g, ' ')
      .trim();
  }

  /**
   * 清理URL
   */
  cleanUrl(url) {
    if (!url) return '';
    
    try {
      // 移除BING的重定向参数
      if (url.includes('bing.com/ck/a?')) {
        const urlParams = new URLSearchParams(url.split('?')[1]);
        const targetUrl = urlParams.get('u');
        if (targetUrl) {
          return decodeURIComponent(targetUrl.replace(/^a1/, 'http'));
        }
      }
      
      return url;
    } catch (error) {
      console.warn('⚠️ [BingSearch] URL清理失败:', error);
      return url;
    }
  }

  /**
   * 计算相关性分数
   */
  calculateRelevanceScore(title, description) {
    let score = 0.5; // 基础分数
    
    // 根据标题长度调整
    if (title && title.length > 10 && title.length < 100) {
      score += 0.1;
    }
    
    // 根据描述长度调整
    if (description && description.length > 50 && description.length < 300) {
      score += 0.1;
    }
    
    // 检查是否包含常见的高质量指标
    const qualityIndicators = ['官网', '官方', 'wikipedia', 'baidu', 'zhihu'];
    const text = (title + ' ' + description).toLowerCase();
    
    for (const indicator of qualityIndicators) {
      if (text.includes(indicator)) {
        score += 0.1;
        break;
      }
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * 生成客户端ID
   */
  generateCvid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * 控制请求频率
   */
  async throttleRequest() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.requestDelay) {
      const waitTime = this.requestDelay - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.lastRequestTime = Date.now();
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(query, options) {
    return `${query}_${JSON.stringify(options)}`;
  }

  /**
   * 获取缓存结果
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  /**
   * 设置缓存
   */
  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
    
    // 清理过期缓存
    this.cleanExpiredCache();
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= this.cacheTimeout) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 检测是否是真正的重定向页面（更严格的检测）
   */
  isLikelyRedirectPage(html) {
    console.log('🔍 [BingSearch] 开始重定向检测...');
    console.log('📄 [BingSearch] HTML长度:', html.length);

    // 🔧 首先检查是否包含搜索结果 - 如果有搜索结果，绝对不是重定向页面
    const hasSearchResults = html.includes('b_results') ||
                           html.includes('b_algo') ||
                           html.includes('search-results') ||
                           html.includes('results') ||
                           html.includes('web-results') ||
                           html.includes('ol id="b_results"') ||
                           html.includes('class="b_algo"') ||
                           html.includes('id="b_results"');

    console.log('🔍 [BingSearch] 是否包含搜索结果:', hasSearchResults);

    // 🔧 如果有搜索结果容器，则绝对不是重定向页面
    if (hasSearchResults) {
      console.log('✅ [BingSearch] 检测到搜索结果容器，这是搜索结果页面');
      return false;
    }

    // 🔧 只有在HTML很短时才可能是重定向页面
    if (html.length < 500) {
      console.log('🔍 [BingSearch] HTML内容较短，检查重定向标识');

      const lowerHtml = html.toLowerCase();

      // 检查明确的重定向标识（只在短HTML中检查）
      const strongRedirectIndicators = [
        'window.location.href',
        'document.location',
        'location.replace',
        'meta http-equiv="refresh"',
        'please wait while we redirect',
        'you are being redirected',
        'redirecting you to'
      ];

      let foundIndicators = [];
      for (const indicator of strongRedirectIndicators) {
        if (lowerHtml.includes(indicator)) {
          foundIndicators.push(indicator);
        }
      }

      if (foundIndicators.length > 0) {
        console.log(`🔍 [BingSearch] 在短HTML中检测到重定向标识:`, foundIndicators);
        return true;
      }
    }

    // 🔧 如果HTML长度合理（>500字符），即使没有明显的搜索结果也不认为是重定向
    // 可能是BING返回了"没有结果"的页面或其他内容页面
    if (html.length > 500) {
      console.log('🔍 [BingSearch] HTML长度合理，不认为是重定向页面');
      return false;
    }

    console.log('🔍 [BingSearch] 未明确检测到重定向或搜索结果');
    return false;
  }

  /**
   * 从HTML中提取重定向URL
   */
  extractRedirectUrl(html) {
    try {
      console.log('🔍 [BingSearch] 尝试提取重定向URL...');
      console.log('📄 [BingSearch] HTML内容长度:', html.length);
      console.log('📄 [BingSearch] HTML前500字符:', html.substring(0, 500));

      // 尝试多种方式提取重定向URL - 修复正则表达式
      const patterns = [
        // JavaScript重定向
        /window\.location\.href\s*=\s*["']([^"']+)["']/i,
        /location\.href\s*=\s*["']([^"']+)["']/i,
        /document\.location\s*=\s*["']([^"']+)["']/i,
        /location\.replace\s*\(\s*["']([^"']+)["']\s*\)/i,
        // Meta重定向
        /<meta[^>]+http-equiv=["']refresh["'][^>]+content=["'][^;]*;\s*url=([^"'\s>]+)/i,
        // URL参数中的重定向 - 修复正则表达式
        /[?&]url=([^&\s"'<>]+)/i,
        /[?&]redirect=([^&\s"'<>]+)/i,
        /[?&]goto=([^&\s"'<>]+)/i,
        // BING特定的重定向模式
        /bing\.com\/search\?[^"'\s<>]+/i,
        // 简单的URL匹配（作为最后的尝试）
        /https?:\/\/[^\s"'<>]+bing\.com[^\s"'<>]*/i
      ];

      for (const pattern of patterns) {
        const match = html.match(pattern);
        if (match && match[1]) {
          let url = match[1];

          try {
            // 解码URL
            url = decodeURIComponent(url);
          } catch (e) {
            // 如果解码失败，使用原始URL
          }

          // 确保URL是完整的
          if (url.startsWith('/')) {
            url = 'https://www.bing.com' + url;
          } else if (!url.startsWith('http')) {
            url = 'https://www.bing.com/' + url;
          }

          // 验证URL是否有效且合理
          try {
            const urlObj = new URL(url);

            // 确保URL是有效的BING URL
            if (!urlObj.hostname.includes('bing.com')) {
              console.warn(`⚠️ [BingSearch] 非BING域名的重定向URL: ${url}`);
              continue;
            }

            // 确保URL不包含明显的错误字符
            if (url.includes('[') || url.includes(']') || url.includes('([^')) {
              console.warn(`⚠️ [BingSearch] URL包含无效字符: ${url}`);
              continue;
            }

            console.log(`✅ [BingSearch] 提取到有效重定向URL: ${url}`);
            return url;
          } catch (e) {
            console.warn(`⚠️ [BingSearch] 无效的重定向URL: ${url}`, e.message);
            continue;
          }
        }
      }

      console.log('❌ [BingSearch] 未找到有效的重定向URL');
      return null;
    } catch (error) {
      console.warn('⚠️ [BingSearch] 提取重定向URL失败:', error);
      return null;
    }
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.cache.clear();
  }
}

export default BingSearchService;
