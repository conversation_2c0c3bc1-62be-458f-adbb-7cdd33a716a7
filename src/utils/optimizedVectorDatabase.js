/**
 * 优化的向量数据库 - 渲染进程版本
 * 集成主进程Worker Threads和浏览器兼容的缓存优化
 */
export class OptimizedVectorDatabase {
  constructor() {
    this.vectors = new Map(); // 存储向量数据
    this.dimension = null;
    this.dbName = 'JDCAIChat_VectorDB';
    this.version = 1;
    this.db = null;
    
    // 🚀 主进程服务集成
    this.useMainProcess = window.electronAPI ? true : false;
    this.cache = new Map(); // 本地缓存
    this.maxCacheSize = 1000;
    
    // 性能统计
    this.stats = {
      totalSearches: 0,
      cacheHits: 0,
      mainProcessHits: 0,
      avgSearchTime: 0,
      lastSearchTime: 0
    };

    console.log(`🚀 [OptimizedVectorDB] 初始化${this.useMainProcess ? '（主进程加速）' : '（本地模式）'}`);
  }

  /**
   * 初始化IndexedDB数据库
   */
  async initialize() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ [OptimizedVectorDB] IndexedDB初始化完成');
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // 创建向量存储表
        if (!db.objectStoreNames.contains('vectors')) {
          const vectorStore = db.createObjectStore('vectors', { keyPath: 'id' });
          vectorStore.createIndex('knowledgeBaseId', 'knowledgeBaseId', { unique: false });
          vectorStore.createIndex('documentId', 'documentId', { unique: false });
        }
        
        // 创建配置存储表
        if (!db.objectStoreNames.contains('config')) {
          db.createObjectStore('config', { keyPath: 'key' });
        }
      };
    });
  }

  /**
   * 🚀 优化的向量搜索
   * 优先使用主进程Worker Threads，回退到本地计算
   */
  async search(queryVector, options = {}) {
    const startTime = Date.now();
    const {
      limit = 5,
      threshold = 0.0,
      knowledgeBaseId = null,
      documentId = null,
      useCache = true
    } = options;

    this.stats.totalSearches++;
    
    // 🚀 检查本地缓存
    const cacheKey = this.generateCacheKey(queryVector, options);
    if (useCache && this.cache.has(cacheKey)) {
      this.stats.cacheHits++;
      const result = this.cache.get(cacheKey);
      console.log('🎯 [OptimizedVectorDB] 本地缓存命中');
      this.updateStats(Date.now() - startTime);
      return result;
    }

    // 🚀 优先使用主进程高性能搜索
    if (this.useMainProcess && window.electronAPI?.vectorSearch) {
      try {
        const vectors = await this.getAllVectors();
        const result = await window.electronAPI.vectorSearch(queryVector, vectors, {
          topK: limit,
          threshold,
          knowledgeBaseId,
          documentId
        });
        
        this.stats.mainProcessHits++;
        
        // 缓存结果
        if (useCache) {
          this.addToCache(cacheKey, result);
        }
        
        const searchTime = Date.now() - startTime;
        this.updateStats(searchTime);
        
        console.log(`⚡ [OptimizedVectorDB] 主进程搜索完成: ${result.length}个结果, 耗时${searchTime}ms`);
        return result;
        
      } catch (error) {
        console.warn('⚠️ [OptimizedVectorDB] 主进程搜索失败，回退到本地搜索:', error.message);
      }
    }

    // 🚀 回退到本地搜索
    return await this.localSearch(queryVector, options, startTime, cacheKey, useCache);
  }

  /**
   * 🚀 本地搜索实现（优化版）
   */
  async localSearch(queryVector, options, startTime, cacheKey, useCache) {
    const {
      limit = 5,
      threshold = 0.0,
      knowledgeBaseId = null,
      documentId = null
    } = options;

    if (!this.db) await this.initialize();
    
    // 加载所有向量到内存（如果还没有加载）
    if (this.vectors.size === 0) {
      await this.loadVectorsToMemory();
    }

    const results = [];
    
    // 🚀 批量计算相似度（优化版）
    for (const [id, vectorData] of this.vectors) {
      // 过滤条件
      if (knowledgeBaseId && vectorData.metadata.knowledgeBaseId !== knowledgeBaseId) {
        continue;
      }
      if (documentId && vectorData.metadata.documentId !== documentId) {
        continue;
      }

      // 计算余弦相似度
      const similarity = this.calculateCosineSimilarity(queryVector, vectorData.vector);
      
      if (similarity >= threshold) {
        results.push({
          id,
          similarity,
          metadata: vectorData.metadata,
          vector: vectorData.vector
        });
      }
    }

    // 按相似度降序排序并限制结果数量
    const finalResults = results
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit);
    
    // 缓存结果
    if (useCache) {
      this.addToCache(cacheKey, finalResults);
    }
    
    const searchTime = Date.now() - startTime;
    this.updateStats(searchTime);
    
    console.log(`🔍 [OptimizedVectorDB] 本地搜索完成: ${finalResults.length}个结果, 耗时${searchTime}ms`);
    return finalResults;
  }

  /**
   * 添加向量到数据库
   */
  async addVector(id, vector, metadata = {}) {
    if (!this.db) await this.initialize();
    
    // 验证向量维度
    if (this.dimension === null) {
      this.dimension = vector.length;
      await this.saveConfig('dimension', this.dimension);
    } else if (vector.length !== this.dimension) {
      throw new Error(`向量维度不匹配。期望: ${this.dimension}, 实际: ${vector.length}`);
    }

    const vectorData = {
      id,
      vector: Array.from(vector),
      metadata,
      createdAt: new Date().toISOString()
    };

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['vectors'], 'readwrite');
      const store = transaction.objectStore('vectors');
      const request = store.put(vectorData);
      
      request.onsuccess = () => {
        this.vectors.set(id, vectorData);
        resolve(vectorData);
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * 批量添加向量
   */
  async addVectors(vectorList) {
    if (!this.db) await this.initialize();
    
    const transaction = this.db.transaction(['vectors'], 'readwrite');
    const store = transaction.objectStore('vectors');
    
    const promises = vectorList.map(({ id, vector, metadata }) => {
      return new Promise((resolve, reject) => {
        const vectorData = {
          id,
          vector: Array.from(vector),
          metadata,
          createdAt: new Date().toISOString()
        };
        
        const request = store.put(vectorData);
        request.onsuccess = () => {
          this.vectors.set(id, vectorData);
          resolve(vectorData);
        };
        request.onerror = () => reject(request.error);
      });
    });
    
    return Promise.all(promises);
  }

  /**
   * 🚀 获取所有向量（用于主进程搜索）
   */
  async getAllVectors() {
    if (!this.db) await this.initialize();
    
    // 如果内存中有数据，直接返回
    if (this.vectors.size > 0) {
      return Array.from(this.vectors.values());
    }
    
    // 从IndexedDB加载
    await this.loadVectorsToMemory();
    return Array.from(this.vectors.values());
  }

  /**
   * 加载向量到内存
   */
  async loadVectorsToMemory() {
    if (!this.db) await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['vectors'], 'readonly');
      const store = transaction.objectStore('vectors');
      const request = store.getAll();
      
      request.onsuccess = () => {
        const vectors = request.result;
        this.vectors.clear();
        
        vectors.forEach(vectorData => {
          this.vectors.set(vectorData.id, vectorData);
        });
        
        console.log(`📚 [OptimizedVectorDB] 已加载 ${vectors.length} 个向量到内存`);
        resolve();
      };
      
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * 计算余弦相似度
   */
  calculateCosineSimilarity(vectorA, vectorB) {
    if (vectorA.length !== vectorB.length) {
      throw new Error('向量维度不匹配');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      normA += vectorA[i] * vectorA[i];
      normB += vectorB[i] * vectorB[i];
    }

    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    
    if (magnitude === 0) {
      return 0;
    }

    return dotProduct / magnitude;
  }

  /**
   * 🚀 生成缓存键（浏览器兼容版）
   */
  generateCacheKey(queryVector, options) {
    const vectorHash = this.hashVector(queryVector);
    const optionsHash = this.hashObject(options);
    return `search_${vectorHash}_${optionsHash}`;
  }

  /**
   * 🚀 向量哈希（浏览器兼容版）
   */
  hashVector(vector) {
    // 使用前10个元素生成哈希
    const str = vector.slice(0, 10).join(',');
    return this.simpleHash(str);
  }

  /**
   * 🚀 对象哈希（浏览器兼容版）
   */
  hashObject(obj) {
    const str = JSON.stringify(obj);
    return this.simpleHash(str);
  }

  /**
   * 🚀 简单哈希函数（浏览器兼容版）
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(16).substring(0, 8);
  }

  /**
   * 🚀 添加到缓存
   */
  addToCache(key, data) {
    // LRU缓存策略
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, data);
  }

  /**
   * 🚀 更新性能统计
   */
  updateStats(searchTime) {
    this.stats.lastSearchTime = searchTime;
    this.stats.avgSearchTime = (this.stats.avgSearchTime * (this.stats.totalSearches - 1) + searchTime) / this.stats.totalSearches;
  }

  /**
   * 🚀 获取性能统计
   */
  getStats() {
    return {
      ...this.stats,
      cacheHitRate: this.stats.totalSearches > 0 ? (this.stats.cacheHits / this.stats.totalSearches * 100).toFixed(2) + '%' : '0%',
      mainProcessHitRate: this.stats.totalSearches > 0 ? (this.stats.mainProcessHits / this.stats.totalSearches * 100).toFixed(2) + '%' : '0%',
      vectorCount: this.vectors.size,
      cacheSize: this.cache.size,
      useMainProcess: this.useMainProcess
    };
  }

  /**
   * 🚀 清理缓存
   */
  clearCache() {
    this.cache.clear();
    console.log('🧹 [OptimizedVectorDB] 缓存已清理');
  }

  /**
   * 保存配置
   */
  async saveConfig(key, value) {
    if (!this.db) await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['config'], 'readwrite');
      const store = transaction.objectStore('config');
      const request = store.put({ key, value });
      
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * 获取配置
   */
  async getConfig(key) {
    if (!this.db) await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['config'], 'readonly');
      const store = transaction.objectStore('config');
      const request = store.get(key);
      
      request.onsuccess = () => {
        resolve(request.result ? request.result.value : null);
      };
      request.onerror = () => reject(request.error);
    });
  }
}
