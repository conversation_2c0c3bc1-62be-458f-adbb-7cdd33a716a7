/**
 * 网络搜索管理器
 * 集成BING搜索和内容提取功能
 */

import BingSearchService from './bingSearchService.js';
import WebContentExtractor from './webContentExtractor.js';

class WebSearchManager {
  constructor() {
    this.bingSearch = new BingSearchService();
    this.contentExtractor = new WebContentExtractor();
    this.isEnabled = false;
    this.searchHistory = [];
    this.maxHistorySize = 100;
  }

  /**
   * 启用/禁用网络搜索
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
    console.log(`🌐 [WebSearch] 网络搜索已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 检查是否启用
   */
  getEnabled() {
    return this.isEnabled;
  }

  /**
   * 执行网络搜索
   * @param {string} query - 搜索查询
   * @param {Object} options - 搜索选项
   * @returns {Promise<Object>} 搜索结果
   */
  async search(query, options = {}) {
    if (!this.isEnabled) {
      throw new Error('网络搜索功能未启用');
    }

    const {
      maxResults = 5,
      includeContent = true,
      language = 'zh-CN',
      region = 'CN',
      freshness = null
    } = options;

    try {
      console.log('🔍 [WebSearch] 开始网络搜索:', query);
      
      const startTime = Date.now();
      
      // 执行BING搜索
      const searchResults = await this.bingSearch.search(query, {
        maxResults,
        language,
        region,
        freshness
      });

      if (!searchResults || searchResults.length === 0) {
        return {
          query,
          results: [],
          totalResults: 0,
          searchTime: Date.now() - startTime,
          timestamp: new Date().toISOString(),
          source: 'bing'
        };
      }

      // 如果需要详细内容，提取前几个结果的内容
      let enhancedResults = searchResults;
      if (includeContent) {
        enhancedResults = await this.enhanceResultsWithContent(
          searchResults.slice(0, Math.min(3, searchResults.length))
        );
        
        // 保留其余结果但不提取内容
        if (searchResults.length > 3) {
          enhancedResults = enhancedResults.concat(searchResults.slice(3));
        }
      }

      const result = {
        query,
        results: enhancedResults,
        totalResults: enhancedResults.length,
        searchTime: Date.now() - startTime,
        timestamp: new Date().toISOString(),
        source: 'bing',
        hasDetailedContent: includeContent
      };

      // 记录搜索历史
      this.addToHistory(result);

      console.log(`✅ [WebSearch] 搜索完成，耗时: ${result.searchTime}ms，结果数: ${result.totalResults}`);
      return result;

    } catch (error) {
      console.error('❌ [WebSearch] 搜索失败:', error);
      throw new Error(`网络搜索失败: ${error.message}`);
    }
  }

  /**
   * 增强搜索结果，添加详细内容
   */
  async enhanceResultsWithContent(results) {
    const enhancedResults = [];
    
    for (const result of results) {
      try {
        console.log(`📄 [WebSearch] 提取内容: ${result.url}`);
        
        const content = await this.contentExtractor.extractContent(result.url, {
          maxLength: 2000,
          includeImages: false,
          includeLinks: false
        });

        enhancedResults.push({
          ...result,
          content: {
            title: content.title,
            text: content.text,
            wordCount: content.wordCount,
            language: content.language,
            extractedAt: content.timestamp
          },
          hasContent: true
        });

      } catch (error) {
        console.warn(`⚠️ [WebSearch] 内容提取失败 ${result.url}:`, error.message);
        
        // 即使内容提取失败，也保留基本搜索结果
        enhancedResults.push({
          ...result,
          content: null,
          hasContent: false,
          contentError: error.message
        });
      }
    }
    
    return enhancedResults;
  }

  /**
   * 格式化搜索结果为AI可读的文本
   * @param {Object} searchResult - 搜索结果
   * @returns {string} 格式化的文本
   */
  formatResultsForAI(searchResult) {
    if (!searchResult || !searchResult.results || searchResult.results.length === 0) {
      return '未找到相关的网络搜索结果。';
    }

    let formatted = `## 🌐 网络搜索结果\n\n`;
    formatted += `**搜索查询**: ${searchResult.query}\n`;
    formatted += `**搜索时间**: ${new Date(searchResult.timestamp).toLocaleString()}\n`;
    formatted += `**结果数量**: ${searchResult.totalResults}\n\n`;

    searchResult.results.forEach((result, index) => {
      formatted += `### ${index + 1}. ${result.title}\n`;
      formatted += `**链接**: ${result.url}\n`;
      formatted += `**来源**: ${result.displayUrl}\n`;
      
      if (result.publishTime) {
        formatted += `**发布时间**: ${result.publishTime}\n`;
      }
      
      formatted += `**描述**: ${result.description}\n`;
      
      if (result.content && result.content.text) {
        formatted += `**详细内容**:\n${result.content.text.substring(0, 800)}${result.content.text.length > 800 ? '...' : ''}\n`;
      }
      
      formatted += `**相关性**: ${(result.relevanceScore * 100).toFixed(1)}%\n\n`;
    });

    formatted += `---\n*搜索耗时: ${searchResult.searchTime}ms*\n\n`;
    formatted += `**重要提示**: 请基于以上最新的网络搜索结果回答用户问题。这些信息来自实时搜索，请优先使用这些数据而不是训练数据中的过时信息。`;

    return formatted;
  }

  /**
   * 智能查询预处理
   * @param {string} userMessage - 用户消息
   * @returns {string|null} 提取的搜索查询，如果不需要搜索则返回null
   */
  extractSearchQuery(userMessage) {
    if (!userMessage || typeof userMessage !== 'string') {
      return null;
    }

    const message = userMessage.toLowerCase().trim();
    
    // 检查是否包含搜索意图的关键词
    const searchIndicators = [
      '搜索', '查找', '查询', '寻找', '搜一下', '搜索一下',
      '最新', '新闻', '资讯', '消息', '动态', '情况',
      '什么是', '如何', '怎么', '为什么', '哪里',
      '现在', '目前', '当前', '今天', '最近',
      '价格', '股价', '汇率', '天气', '时间'
    ];

    const hasSearchIntent = searchIndicators.some(indicator => 
      message.includes(indicator)
    );

    if (!hasSearchIntent) {
      return null;
    }

    // 提取关键词作为搜索查询
    let query = userMessage;
    
    // 移除常见的无用词汇
    const stopWords = [
      '请', '帮我', '帮忙', '可以', '能否', '能不能',
      '一下', '看看', '告诉我', '我想', '我要'
    ];
    
    stopWords.forEach(word => {
      query = query.replace(new RegExp(word, 'gi'), '');
    });
    
    // 清理查询
    query = query.trim().replace(/\s+/g, ' ');
    
    // 如果查询太短或太长，返回原始消息的关键部分
    if (query.length < 2) {
      return userMessage.substring(0, 50);
    }
    
    if (query.length > 100) {
      return query.substring(0, 100);
    }
    
    return query;
  }

  /**
   * 添加到搜索历史
   */
  addToHistory(searchResult) {
    this.searchHistory.unshift({
      query: searchResult.query,
      timestamp: searchResult.timestamp,
      resultCount: searchResult.totalResults,
      searchTime: searchResult.searchTime
    });

    // 限制历史记录大小
    if (this.searchHistory.length > this.maxHistorySize) {
      this.searchHistory = this.searchHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * 获取搜索历史
   */
  getSearchHistory(limit = 10) {
    return this.searchHistory.slice(0, limit);
  }

  /**
   * 清空搜索历史
   */
  clearHistory() {
    this.searchHistory = [];
  }

  /**
   * 获取搜索统计信息
   */
  getSearchStats() {
    const totalSearches = this.searchHistory.length;
    const avgSearchTime = totalSearches > 0 
      ? this.searchHistory.reduce((sum, item) => sum + item.searchTime, 0) / totalSearches 
      : 0;
    
    const recentSearches = this.searchHistory.slice(0, 10);
    
    return {
      totalSearches,
      avgSearchTime: Math.round(avgSearchTime),
      recentSearches,
      cacheStats: {
        bingCacheSize: this.bingSearch.cache.size,
        contentCacheSize: this.contentExtractor.cache.size
      }
    };
  }

  /**
   * 清空所有缓存
   */
  clearAllCaches() {
    this.bingSearch.clearCache();
    this.contentExtractor.clearCache();
    console.log('🧹 [WebSearch] 所有缓存已清空');
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      enabled: this.isEnabled,
      stats: this.getSearchStats(),
      services: {
        bingSearch: 'ready',
        contentExtractor: 'ready'
      }
    };
  }
}

export default WebSearchManager;
