/**
 * 防死循环保护机制
 */
export class LoopProtectionManager {
  constructor(options = {}) {
    this.maxRounds = options.maxRounds || 12; // 最大连续AI对话回合数
    this.similarityThreshold = options.similarityThreshold || 0.8; // 相似度阈值
    this.repeatPatternLimit = options.repeatPatternLimit || 2; // 重复模式限制
    this.checkWindowSize = options.checkWindowSize || 3; // 检查窗口大小
    
    this.currentRounds = 0;
    this.messageHistory = [];
    this.patternHistory = [];
    this.isProtectionTriggered = false;
    this.protectionReason = null;
  }

  /**
   * 重置保护状态
   */
  reset() {
    this.currentRounds = 0;
    this.messageHistory = [];
    this.patternHistory = [];
    this.isProtectionTriggered = false;
    this.protectionReason = null;
  }

  /**
   * 检查是否应该触发保护机制
   * @param {Object} newMessage - 新消息
   * @param {string} newMessage.sender - 发送者
   * @param {string} newMessage.content - 消息内容
   * @returns {Object} 检查结果
   */
  checkProtection(newMessage) {
    // 如果是用户消息，重置计数
    if (newMessage.sender === 'user') {
      this.currentRounds = 0;
      this.isProtectionTriggered = false;
      this.protectionReason = null;
      return { shouldStop: false, reason: null };
    }

    // 如果是系统消息，跳过检查
    if (newMessage.sender === 'system') {
      return { shouldStop: false, reason: null };
    }

    // 增加AI回合计数
    this.currentRounds++;
    this.messageHistory.push(newMessage);

    // 检查回合数限制
    if (this.currentRounds >= this.maxRounds) {
      this.isProtectionTriggered = true;
      this.protectionReason = 'MAX_ROUNDS';
      return {
        shouldStop: true,
        reason: 'MAX_ROUNDS',
        message: `已达到最大连续对话回合数限制（${this.maxRounds}轮），为避免无限循环已自动停止。`
      };
    }

    // 检查内容重复
    const repeatCheck = this.checkContentRepeat(newMessage);
    if (repeatCheck.shouldStop) {
      this.isProtectionTriggered = true;
      this.protectionReason = 'CONTENT_REPEAT';
      return repeatCheck;
    }

    // 检查循环模式
    const patternCheck = this.checkCircularPattern(newMessage);
    if (patternCheck.shouldStop) {
      this.isProtectionTriggered = true;
      this.protectionReason = 'CIRCULAR_PATTERN';
      return patternCheck;
    }

    return { shouldStop: false, reason: null };
  }

  /**
   * 检查内容重复
   */
  checkContentRepeat(newMessage) {
    if (this.messageHistory.length < this.checkWindowSize) {
      return { shouldStop: false };
    }

    // 获取最近的消息窗口
    const recentMessages = this.messageHistory.slice(-this.checkWindowSize);
    const similarities = [];

    // 计算新消息与最近消息的相似度
    for (let i = 0; i < recentMessages.length - 1; i++) {
      const similarity = this.calculateSimilarity(
        newMessage.content,
        recentMessages[i].content
      );
      similarities.push(similarity);
    }

    // 检查是否有高相似度
    const highSimilarityCount = similarities.filter(
      sim => sim >= this.similarityThreshold
    ).length;

    if (highSimilarityCount >= this.repeatPatternLimit) {
      return {
        shouldStop: true,
        reason: 'CONTENT_REPEAT',
        message: `检测到内容重复模式（相似度超过${Math.round(this.similarityThreshold * 100)}%），已自动停止对话。`
      };
    }

    return { shouldStop: false };
  }

  /**
   * 检查循环模式
   */
  checkCircularPattern(newMessage) {
    // 记录发言模式
    this.patternHistory.push(newMessage.sender);

    // 保持模式历史在合理长度
    if (this.patternHistory.length > 20) {
      this.patternHistory = this.patternHistory.slice(-20);
    }

    // 检查简单的A→B→A模式
    if (this.patternHistory.length >= 6) {
      const recent = this.patternHistory.slice(-6);
      if (this.isABAPattern(recent)) {
        return {
          shouldStop: true,
          reason: 'CIRCULAR_PATTERN',
          message: '检测到循环发言模式（A→B→A），已自动停止对话。'
        };
      }
    }

    // 检查A→B→C→A模式
    if (this.patternHistory.length >= 8) {
      const recent = this.patternHistory.slice(-8);
      if (this.isABCAPattern(recent)) {
        return {
          shouldStop: true,
          reason: 'CIRCULAR_PATTERN',
          message: '检测到循环发言模式（A→B→C→A），已自动停止对话。'
        };
      }
    }

    return { shouldStop: false };
  }

  /**
   * 检查A→B→A模式
   */
  isABAPattern(pattern) {
    if (pattern.length < 6) return false;
    
    // 检查最近6个发言是否形成A→B→A→B→A→B模式
    const [a1, b1, a2, b2, a3, b3] = pattern;
    return a1 === a2 && a2 === a3 && b1 === b2 && b2 === b3 && a1 !== b1;
  }

  /**
   * 检查A→B→C→A模式
   */
  isABCAPattern(pattern) {
    if (pattern.length < 8) return false;
    
    // 检查最近8个发言是否形成A→B→C→A→B→C→A→B模式
    const [a1, b1, c1, a2, b2, c2, a3, b3] = pattern;
    return (
      a1 === a2 && a2 === a3 &&
      b1 === b2 && b2 === b3 &&
      c1 === c2 &&
      a1 !== b1 && b1 !== c1 && a1 !== c1
    );
  }

  /**
   * 计算文本相似度（简化版）
   */
  calculateSimilarity(text1, text2) {
    if (!text1 || !text2) return 0;
    
    // 简单的字符级相似度计算
    const len1 = text1.length;
    const len2 = text2.length;
    const maxLen = Math.max(len1, len2);
    
    if (maxLen === 0) return 1;
    
    // 计算编辑距离
    const distance = this.levenshteinDistance(text1, text2);
    return 1 - (distance / maxLen);
  }

  /**
   * 计算编辑距离
   */
  levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      currentRounds: this.currentRounds,
      maxRounds: this.maxRounds,
      isProtectionTriggered: this.isProtectionTriggered,
      protectionReason: this.protectionReason,
      messageHistoryLength: this.messageHistory.length,
      patternHistoryLength: this.patternHistory.length
    };
  }

  /**
   * 更新配置
   */
  updateConfig(options) {
    if (options.maxRounds !== undefined) {
      this.maxRounds = Math.max(8, Math.min(20, options.maxRounds));
    }
    if (options.similarityThreshold !== undefined) {
      this.similarityThreshold = Math.max(0.5, Math.min(1.0, options.similarityThreshold));
    }
    if (options.repeatPatternLimit !== undefined) {
      this.repeatPatternLimit = Math.max(1, Math.min(5, options.repeatPatternLimit));
    }
  }

  /**
   * 手动继续对话（重置保护状态）
   */
  continueConversation() {
    this.isProtectionTriggered = false;
    this.protectionReason = null;
    // 保留当前回合数和历史，但允许继续
  }
}
