/**
 * 向量嵌入服务
 * 负责调用embedding模型将文本转换为向量
 */
export class EmbeddingService {
  constructor() {
    this.cache = new Map(); // 缓存已计算的向量
    this.maxCacheSize = 1000;
  }

  /**
   * 生成文本的向量嵌入（别名方法）
   * @param {string} text - 要向量化的文本
   * @param {Object} model - embedding模型配置
   * @returns {Promise<Array>} 向量数组
   */
  async generateEmbedding(text, model) {
    return await this.textToVector(text, model);
  }

  /**
   * 将文本转换为向量
   */
  async textToVector(text, model) {
    // 检查缓存
    const cacheKey = this.getCacheKey(text, model.id);
    if (this.cache.has(cacheKey)) {
      console.log('🎯 [Embedding] 使用缓存向量');
      return this.cache.get(cacheKey);
    }

    try {
      console.log(`🔄 [Embedding] 开始向量化文本，模型: ${model.name}`);
      
      // 构建API请求
      const requestBody = {
        input: text,
        model: model.name
      };

      const response = await fetch(`${model.baseUrl}/embeddings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${model.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      
      if (!data.data || !data.data[0] || !data.data[0].embedding) {
        throw new Error('API响应格式错误，缺少embedding数据');
      }

      const vector = data.data[0].embedding;
      
      // 缓存结果
      this.addToCache(cacheKey, vector);
      
      console.log(`✅ [Embedding] 向量化完成，维度: ${vector.length}`);
      return vector;

    } catch (error) {
      console.error('❌ [Embedding] 向量化失败:', error);
      throw new Error(`向量化失败: ${error.message}`);
    }
  }

  /**
   * 批量文本向量化
   */
  async batchTextToVector(texts, model, onProgress = null) {
    const results = [];
    const total = texts.length;
    
    console.log(`🔄 [Embedding] 开始批量向量化，共 ${total} 个文本`);
    
    for (let i = 0; i < texts.length; i++) {
      try {
        const vector = await this.textToVector(texts[i], model);
        results.push({
          text: texts[i],
          vector,
          index: i
        });
        
        // 报告进度
        if (onProgress) {
          onProgress({
            current: i + 1,
            total,
            percentage: Math.round(((i + 1) / total) * 100)
          });
        }
        
        // 避免API限流，添加小延迟
        if (i < texts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
      } catch (error) {
        console.error(`❌ [Embedding] 第 ${i + 1} 个文本向量化失败:`, error);
        results.push({
          text: texts[i],
          vector: null,
          error: error.message,
          index: i
        });
      }
    }
    
    console.log(`✅ [Embedding] 批量向量化完成，成功: ${results.filter(r => r.vector).length}/${total}`);
    return results;
  }

  /**
   * 🚀 新增：分层分块批量向量化
   */
  async batchHierarchicalTextToVector(hierarchicalChunks, model, onProgress = null) {
    const results = [];
    let processedCount = 0;

    // 计算总的分块数量（父分块 + 所有子分块）
    const totalChunks = hierarchicalChunks.reduce((total, parent) => {
      return total + 1 + parent.children.length;
    }, 0);

    console.log(`🔄 [Embedding] 开始分层批量向量化，共 ${totalChunks} 个分块`);

    for (const parentChunk of hierarchicalChunks) {
      try {
        // 向量化父分块
        const parentVector = await this.textToVector(parentChunk.text, model);
        const parentResult = {
          ...parentChunk,
          vector: parentVector,
          vectorized: true
        };

        processedCount++;
        if (onProgress) {
          onProgress({
            current: processedCount,
            total: totalChunks,
            percentage: Math.round((processedCount / totalChunks) * 100),
            stage: 'parent',
            chunkId: parentChunk.id
          });
        }

        // 向量化子分块
        const childResults = [];
        for (const childChunk of parentChunk.children) {
          try {
            const childVector = await this.textToVector(childChunk.text, model);
            childResults.push({
              ...childChunk,
              vector: childVector,
              vectorized: true
            });

            processedCount++;
            if (onProgress) {
              onProgress({
                current: processedCount,
                total: totalChunks,
                percentage: Math.round((processedCount / totalChunks) * 100),
                stage: 'child',
                chunkId: childChunk.id,
                parentId: parentChunk.id
              });
            }

            // 避免API限流
            await new Promise(resolve => setTimeout(resolve, 100));

          } catch (error) {
            console.error(`❌ [Embedding] 子分块向量化失败 ${childChunk.id}:`, error);
            childResults.push({
              ...childChunk,
              vector: null,
              vectorized: false,
              error: error.message
            });
            processedCount++;
          }
        }

        parentResult.children = childResults;
        results.push(parentResult);

        // 避免API限流
        await new Promise(resolve => setTimeout(resolve, 150));

      } catch (error) {
        console.error(`❌ [Embedding] 父分块向量化失败 ${parentChunk.id}:`, error);
        results.push({
          ...parentChunk,
          vector: null,
          vectorized: false,
          error: error.message,
          children: parentChunk.children.map(child => ({
            ...child,
            vector: null,
            vectorized: false,
            error: '父分块向量化失败'
          }))
        });
        processedCount += 1 + parentChunk.children.length;
      }
    }

    const successCount = results.reduce((count, parent) => {
      const parentSuccess = parent.vector ? 1 : 0;
      const childSuccess = parent.children.filter(child => child.vector).length;
      return count + parentSuccess + childSuccess;
    }, 0);

    console.log(`✅ [Embedding] 分层批量向量化完成，成功: ${successCount}/${totalChunks}`);
    return results;
  }

  /**
   * 文档分块处理（支持分层分块和自定义分割符）
   */
  splitDocument(content, options = {}) {
    const {
      chunkSize = 500,
      overlap = 50,
      preserveParagraphs = true,
      hierarchical = false,
      parentChunkSize = 800,
      childChunkSize = 200,
      separatorType = 'paragraph', // 'paragraph', 'sentence', 'custom'
      customSeparator = '\n\n'
    } = options;

    if (!content || content.trim().length === 0) {
      return [];
    }

    if (hierarchical) {
      return this.splitDocumentHierarchical(content, {
        parentChunkSize,
        childChunkSize,
        overlap,
        preserveParagraphs,
        separatorType,
        customSeparator
      });
    }

    // 原有的单层分块逻辑（支持自定义分割符）
    const chunks = [];

    if (preserveParagraphs || separatorType !== 'none') {
      // 根据分割符类型进行分割
      const segments = this.splitBySeparator(content, separatorType, customSeparator);

      let currentChunk = '';
      const separator = this.getSeparatorString(separatorType, customSeparator);

      for (const segment of segments) {
        if (currentChunk.length + segment.length <= chunkSize) {
          currentChunk += (currentChunk ? separator : '') + segment;
        } else {
          if (currentChunk) {
            chunks.push(currentChunk.trim());
          }

          // 如果单个段落太长，需要进一步分割
          if (segment.length > chunkSize) {
            const subChunks = this.splitLongText(segment, chunkSize, overlap);
            chunks.push(...subChunks);
            currentChunk = '';
          } else {
            currentChunk = segment;
          }
        }
      }

      if (currentChunk) {
        chunks.push(currentChunk.trim());
      }
    } else {
      // 简单的固定长度分割
      const textChunks = this.splitLongText(content, chunkSize, overlap);
      chunks.push(...textChunks);
    }

    return chunks.filter(chunk => chunk.trim().length > 0);
  }

  /**
   * 🚀 新增：分层分块处理
   */
  splitDocumentHierarchical(content, options = {}) {
    const {
      parentChunkSize = 800,
      childChunkSize = 200,
      overlap = 50,
      preserveParagraphs = true
    } = options;

    const hierarchicalChunks = [];

    // 首先创建父分块
    const parentChunks = this.createParentChunks(content, {
      chunkSize: parentChunkSize,
      overlap,
      preserveParagraphs
    });

    // 为每个父分块创建子分块
    parentChunks.forEach((parentText, parentIndex) => {
      const parentId = `parent_${parentIndex}`;

      // 创建父分块对象
      const parentChunk = {
        id: parentId,
        text: parentText,
        level: 0,
        parentId: null,
        children: [],
        startPosition: this.findTextPosition(content, parentText),
        endPosition: this.findTextPosition(content, parentText) + parentText.length
      };

      // 创建子分块
      const childTexts = this.createChildChunks(parentText, {
        chunkSize: childChunkSize,
        overlap: Math.min(overlap, childChunkSize / 4)
      });

      childTexts.forEach((childText, childIndex) => {
        const childId = `${parentId}_child_${childIndex}`;

        const childChunk = {
          id: childId,
          text: childText,
          level: 1,
          parentId: parentId,
          children: [],
          startPosition: parentChunk.startPosition + this.findTextPosition(parentText, childText),
          endPosition: parentChunk.startPosition + this.findTextPosition(parentText, childText) + childText.length
        };

        parentChunk.children.push(childChunk);
      });

      hierarchicalChunks.push(parentChunk);
    });

    return hierarchicalChunks;
  }

  /**
   * 🚀 新增：创建父分块（支持自定义分割符）
   */
  createParentChunks(content, options) {
    const {
      chunkSize,
      overlap,
      preserveParagraphs,
      separatorType = 'paragraph',
      customSeparator = '\n\n'
    } = options;

    if (preserveParagraphs || separatorType !== 'none') {
      // 根据分割符类型分组创建父分块
      const segments = this.splitBySeparator(content, separatorType, customSeparator);
      const separator = this.getSeparatorString(separatorType, customSeparator);
      const chunks = [];
      let currentChunk = '';

      for (const segment of segments) {
        if (currentChunk.length + segment.length <= chunkSize) {
          currentChunk += (currentChunk ? separator : '') + segment;
        } else {
          if (currentChunk) {
            chunks.push(currentChunk.trim());
          }

          if (segment.length > chunkSize) {
            const subChunks = this.splitLongText(segment, chunkSize, overlap);
            chunks.push(...subChunks);
            currentChunk = '';
          } else {
            currentChunk = segment;
          }
        }
      }

      if (currentChunk) {
        chunks.push(currentChunk.trim());
      }

      return chunks;
    } else {
      return this.splitLongText(content, chunkSize, overlap);
    }
  }

  /**
   * 🚀 新增：创建子分块
   */
  createChildChunks(parentText, options) {
    const { chunkSize, overlap } = options;

    // 按句子分割，然后组合成子分块
    const sentences = this.splitIntoSentences(parentText);
    const chunks = [];
    let currentChunk = '';

    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length <= chunkSize) {
        currentChunk += (currentChunk ? ' ' : '') + sentence;
      } else {
        if (currentChunk) {
          chunks.push(currentChunk.trim());
        }

        if (sentence.length > chunkSize) {
          // 如果单个句子太长，按字符分割
          const subChunks = this.splitLongText(sentence, chunkSize, overlap);
          chunks.push(...subChunks);
          currentChunk = '';
        } else {
          currentChunk = sentence;
        }
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }

    return chunks.filter(chunk => chunk.trim().length > 0);
  }

  /**
   * 🚀 新增：按句子分割文本
   */
  splitIntoSentences(text) {
    // 中英文句子分割
    const sentences = text.split(/[。！？.!?]+/).filter(s => s.trim().length > 0);
    return sentences.map(s => s.trim());
  }

  /**
   * 🚀 新增：查找文本在原文中的位置
   */
  findTextPosition(fullText, searchText) {
    const index = fullText.indexOf(searchText);
    return index >= 0 ? index : 0;
  }

  /**
   * 🚀 新增：根据分割符类型分割文本（智能化增强）
   */
  splitBySeparator(content, separatorType, customSeparator) {
    // 首先尝试智能识别内容类型
    const contentType = this.detectContentType(content);

    switch (separatorType) {
      case 'paragraph':
        return this.splitByParagraph(content, contentType);

      case 'sentence':
        return this.splitBySentence(content);

      case 'line':
        return content.split(/\n/).filter(l => l.trim().length > 0);

      case 'custom':
        if (customSeparator) {
          return content.split(customSeparator).filter(s => s.trim().length > 0);
        }
        return [content];

      case 'smart':
        return this.smartSplit(content, contentType);

      default:
        return this.splitByParagraph(content, contentType);
    }
  }

  /**
   * 🚀 新增：智能检测内容类型
   */
  detectContentType(content) {
    // 检测SQL DDL语句
    if (/CREATE\s+TABLE/i.test(content)) {
      return 'sql_ddl';
    }

    // 检测代码块
    if (/```[\s\S]*```/.test(content) || /function\s+\w+\s*\(/.test(content)) {
      return 'code';
    }

    // 检测列表结构
    if (/^\s*[-*+]\s+/m.test(content) || /^\s*\d+\.\s+/m.test(content)) {
      return 'list';
    }

    // 检测表格结构
    if (/\|.*\|/.test(content) && /[-:]+/.test(content)) {
      return 'table';
    }

    // 检测JSON/XML
    if (/^\s*[{\[]/.test(content.trim()) || /<\w+.*>/.test(content)) {
      return 'structured_data';
    }

    return 'text';
  }

  /**
   * 🚀 新增：智能分割
   */
  smartSplit(content, contentType) {
    switch (contentType) {
      case 'sql_ddl':
        return this.splitSQLDDL(content);
      case 'code':
        return this.splitCode(content);
      case 'list':
        return this.splitList(content);
      case 'table':
        return this.splitTable(content);
      case 'structured_data':
        return this.splitStructuredData(content);
      default:
        return this.splitByParagraph(content, 'text');
    }
  }

  /**
   * 🚀 新增：分割SQL DDL语句
   */
  splitSQLDDL(content) {
    const segments = [];

    // 按CREATE TABLE语句分割
    const tableMatches = content.match(/CREATE\s+TABLE\s+[^;]+;/gi);
    if (tableMatches) {
      tableMatches.forEach(tableSQL => {
        // 提取表名
        const tableNameMatch = tableSQL.match(/CREATE\s+TABLE\s+`?(\w+)`?/i);
        const tableName = tableNameMatch ? tableNameMatch[1] : 'unknown_table';

        // 分割为表结构和字段定义
        const lines = tableSQL.split('\n').filter(line => line.trim());

        // 表头信息（CREATE TABLE部分）
        const headerLines = lines.filter(line =>
          /CREATE\s+TABLE/i.test(line) || /ENGINE=/i.test(line) || /COMMENT=/i.test(line)
        );

        if (headerLines.length > 0) {
          segments.push({
            type: 'table_header',
            tableName,
            content: headerLines.join('\n').trim()
          });
        }

        // 字段定义（每个字段作为一个分块）
        const fieldLines = lines.filter(line => {
          const trimmed = line.trim();
          return trimmed.startsWith('`') &&
                 !trimmed.startsWith('PRIMARY KEY') &&
                 !trimmed.startsWith('KEY ') &&
                 !trimmed.startsWith('INDEX ');
        });

        fieldLines.forEach(fieldLine => {
          const fieldMatch = fieldLine.match(/`(\w+)`/);
          const fieldName = fieldMatch ? fieldMatch[1] : 'unknown_field';

          segments.push({
            type: 'table_field',
            tableName,
            fieldName,
            content: fieldLine.trim().replace(/,$/, '')
          });
        });

        // 索引和约束
        const constraintLines = lines.filter(line => {
          const trimmed = line.trim();
          return trimmed.startsWith('PRIMARY KEY') ||
                 trimmed.startsWith('KEY ') ||
                 trimmed.startsWith('INDEX ');
        });

        if (constraintLines.length > 0) {
          segments.push({
            type: 'table_constraints',
            tableName,
            content: constraintLines.join('\n').trim()
          });
        }
      });
    }

    return segments.length > 0 ? segments.map(s => s.content) : [content];
  }

  /**
   * 🚀 新增：按段落分割（增强版）
   */
  splitByParagraph(content, contentType) {
    if (contentType === 'sql_ddl') {
      return this.splitSQLDDL(content);
    }

    return content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
  }

  /**
   * 🚀 新增：按句子分割（增强版）
   */
  splitBySentence(content) {
    // 中英文句子分割，保留更多上下文
    const sentences = content.split(/[。！？.!?]+/).filter(s => s.trim().length > 0);

    // 如果句子太短，尝试合并相邻句子
    const mergedSentences = [];
    let currentGroup = '';

    sentences.forEach(sentence => {
      const trimmed = sentence.trim();
      if (currentGroup.length + trimmed.length < 100) {
        currentGroup += (currentGroup ? '。' : '') + trimmed;
      } else {
        if (currentGroup) {
          mergedSentences.push(currentGroup);
        }
        currentGroup = trimmed;
      }
    });

    if (currentGroup) {
      mergedSentences.push(currentGroup);
    }

    return mergedSentences;
  }

  /**
   * 🚀 新增：分割代码内容
   */
  splitCode(content) {
    // 按函数或类分割
    const codeBlocks = [];
    const lines = content.split('\n');
    let currentBlock = '';
    let inFunction = false;

    lines.forEach(line => {
      if (/^\s*(function|class|def|public|private|protected)/.test(line)) {
        if (currentBlock.trim()) {
          codeBlocks.push(currentBlock.trim());
        }
        currentBlock = line;
        inFunction = true;
      } else {
        currentBlock += '\n' + line;
      }
    });

    if (currentBlock.trim()) {
      codeBlocks.push(currentBlock.trim());
    }

    return codeBlocks.length > 0 ? codeBlocks : [content];
  }

  /**
   * 🚀 新增：分割列表内容
   */
  splitList(content) {
    const items = [];
    const lines = content.split('\n');
    let currentItem = '';

    lines.forEach(line => {
      if (/^\s*[-*+]\s+/.test(line) || /^\s*\d+\.\s+/.test(line)) {
        if (currentItem.trim()) {
          items.push(currentItem.trim());
        }
        currentItem = line;
      } else if (line.trim()) {
        currentItem += '\n' + line;
      }
    });

    if (currentItem.trim()) {
      items.push(currentItem.trim());
    }

    return items.length > 0 ? items : [content];
  }

  /**
   * 🚀 新增：分割表格内容
   */
  splitTable(content) {
    const lines = content.split('\n');
    const tableRows = lines.filter(line => /\|.*\|/.test(line) && !/[-:]+/.test(line));

    if (tableRows.length > 0) {
      // 表头单独作为一个分块
      const header = tableRows[0];
      const dataRows = tableRows.slice(1);

      const segments = [header];

      // 每几行数据作为一个分块
      const chunkSize = 5;
      for (let i = 0; i < dataRows.length; i += chunkSize) {
        const chunk = dataRows.slice(i, i + chunkSize);
        segments.push(header + '\n' + chunk.join('\n'));
      }

      return segments;
    }

    return [content];
  }

  /**
   * 🚀 新增：分割结构化数据
   */
  splitStructuredData(content) {
    try {
      // 尝试解析JSON
      const parsed = JSON.parse(content);
      if (Array.isArray(parsed)) {
        return parsed.map(item => JSON.stringify(item, null, 2));
      } else if (typeof parsed === 'object') {
        return Object.keys(parsed).map(key =>
          `${key}: ${JSON.stringify(parsed[key], null, 2)}`
        );
      }
    } catch (e) {
      // 如果不是JSON，按XML标签分割
      const xmlBlocks = content.match(/<\w+[^>]*>[\s\S]*?<\/\w+>/g);
      if (xmlBlocks) {
        return xmlBlocks;
      }
    }

    return [content];
  }

  /**
   * 🚀 新增：获取分割符字符串
   */
  getSeparatorString(separatorType, customSeparator) {
    switch (separatorType) {
      case 'paragraph':
        return '\n\n';
      case 'sentence':
        return ' ';
      case 'line':
        return '\n';
      case 'custom':
        return customSeparator || '\n\n';
      default:
        return '\n\n';
    }
  }

  /**
   * 分割长文本
   */
  splitLongText(text, chunkSize, overlap) {
    const chunks = [];
    let start = 0;
    
    while (start < text.length) {
      let end = start + chunkSize;
      
      // 如果不是最后一块，尝试在单词边界分割
      if (end < text.length) {
        const lastSpace = text.lastIndexOf(' ', end);
        const lastPunctuation = Math.max(
          text.lastIndexOf('。', end),
          text.lastIndexOf('！', end),
          text.lastIndexOf('？', end),
          text.lastIndexOf('.', end),
          text.lastIndexOf('!', end),
          text.lastIndexOf('?', end)
        );
        
        if (lastPunctuation > start + chunkSize * 0.5) {
          end = lastPunctuation + 1;
        } else if (lastSpace > start + chunkSize * 0.5) {
          end = lastSpace;
        }
      }
      
      const chunk = text.substring(start, end).trim();
      if (chunk.length > 0) {
        chunks.push(chunk);
      }
      
      start = Math.max(start + 1, end - overlap);
    }
    
    return chunks;
  }

  /**
   * 生成缓存键
   */
  getCacheKey(text, modelId) {
    // 使用简单的哈希函数生成缓存键
    const hash = this.simpleHash(text + modelId);
    return `${modelId}_${hash}`;
  }

  /**
   * 简单哈希函数
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 添加到缓存
   */
  addToCache(key, vector) {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, vector);
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.cache.clear();
    console.log('🗑️ [Embedding] 缓存已清空');
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      usage: (this.cache.size / this.maxCacheSize * 100).toFixed(1) + '%'
    };
  }

  /**
   * 测试embedding模型连接
   */
  async testModel(model) {
    try {
      const testText = "这是一个测试文本，用于验证embedding模型是否正常工作。";
      const vector = await this.textToVector(testText, model);
      
      return {
        success: true,
        dimension: vector.length,
        sampleVector: vector.slice(0, 5), // 返回前5个维度作为示例
        message: `模型连接成功，向量维度: ${vector.length}`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `模型连接失败: ${error.message}`
      };
    }
  }

  /**
   * 计算文本相似度（不通过向量数据库）
   */
  async calculateTextSimilarity(text1, text2, model) {
    try {
      const [vector1, vector2] = await Promise.all([
        this.textToVector(text1, model),
        this.textToVector(text2, model)
      ]);

      // 计算余弦相似度
      let dotProduct = 0;
      let normA = 0;
      let normB = 0;

      for (let i = 0; i < vector1.length; i++) {
        dotProduct += vector1[i] * vector2[i];
        normA += vector1[i] * vector1[i];
        normB += vector2[i] * vector2[i];
      }

      normA = Math.sqrt(normA);
      normB = Math.sqrt(normB);

      if (normA === 0 || normB === 0) {
        return 0;
      }

      const similarity = dotProduct / (normA * normB);
      
      return {
        similarity,
        vector1Dimension: vector1.length,
        vector2Dimension: vector2.length
      };
    } catch (error) {
      throw new Error(`相似度计算失败: ${error.message}`);
    }
  }
}
