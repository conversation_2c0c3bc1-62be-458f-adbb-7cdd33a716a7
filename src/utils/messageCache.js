// 🚀 消息缓存管理器 - 优化大量消息的内存使用和渲染性能

class MessageCache {
  constructor(maxCacheSize = 1000) {
    this.cache = new Map(); // 使用Map保持插入顺序
    this.maxCacheSize = maxCacheSize;
    this.renderCache = new Map(); // 渲染结果缓存
    this.contentHashCache = new Map(); // 内容哈希缓存
  }

  // 🎯 生成消息内容的哈希值
  generateContentHash(msg) {
    if (!msg || typeof msg !== 'object') {
      return this.simpleHash('empty-message');
    }

    const id = msg.id || 'no-id';
    const content = msg.content || '';
    const thinking = msg.thinking || '';
    const timestamp = msg.timestamp || Date.now();

    const key = `${id}-${content}-${thinking}-${timestamp}`;
    return this.simpleHash(key);
  }

  // 🎯 简单哈希函数
  simpleHash(str) {
    if (!str || typeof str !== 'string') {
      return '0';
    }

    let hash = 0;
    if (str.length === 0) return hash.toString();

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }

  // 🎯 缓存消息
  cacheMessage(msg) {
    if (!msg || !msg.id) {
      console.warn('🚨 [MessageCache] 尝试缓存无效消息:', msg);
      return null;
    }

    try {
      const hash = this.generateContentHash(msg);

      // 如果缓存已满，删除最旧的条目
      if (this.cache.size >= this.maxCacheSize) {
        const firstKey = this.cache.keys().next().value;
        if (firstKey) {
          this.cache.delete(firstKey);
          this.renderCache.delete(firstKey);
          this.contentHashCache.delete(firstKey);
        }
      }

      this.cache.set(msg.id, { ...msg, contentHash: hash });
      this.contentHashCache.set(msg.id, hash);

      return hash;
    } catch (error) {
      console.error('🚨 [MessageCache] 缓存消息时出错:', error, msg);
      return null;
    }
  }

  // 🎯 获取缓存的消息
  getCachedMessage(msgId) {
    return this.cache.get(msgId);
  }

  // 🎯 检查消息是否已变化
  hasMessageChanged(msg) {
    const cachedHash = this.contentHashCache.get(msg.id);
    const currentHash = this.generateContentHash(msg);
    return cachedHash !== currentHash;
  }

  // 🎯 缓存渲染结果
  cacheRenderResult(msgId, renderResult) {
    this.renderCache.set(msgId, renderResult);
  }

  // 🎯 获取缓存的渲染结果
  getCachedRenderResult(msgId) {
    return this.renderCache.get(msgId);
  }

  // 🎯 清理缓存
  clearCache() {
    this.cache.clear();
    this.renderCache.clear();
    this.contentHashCache.clear();
  }

  // 🎯 获取缓存统计信息
  getCacheStats() {
    return {
      messageCount: this.cache.size,
      renderCacheCount: this.renderCache.size,
      maxSize: this.maxCacheSize,
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  // 🎯 估算内存使用量
  estimateMemoryUsage() {
    let totalSize = 0;

    try {
      // 估算消息缓存大小
      for (const [key, value] of this.cache) {
        if (key && typeof key === 'string') {
          totalSize += key.length * 2; // 字符串键
        }
        if (value && typeof value === 'object') {
          try {
            totalSize += JSON.stringify(value).length * 2; // 消息对象
          } catch (e) {
            totalSize += 1000; // 估算值，如果JSON.stringify失败
          }
        }
      }

      // 估算渲染缓存大小
      for (const [key, value] of this.renderCache) {
        if (key && typeof key === 'string') {
          totalSize += key.length * 2;
        }
        totalSize += 1000; // 估算渲染结果大小
      }
    } catch (error) {
      console.warn('🚨 [MessageCache] 估算内存使用量时出错:', error);
      return 0;
    }

    return Math.round(totalSize / 1024); // 返回KB
  }

  // 🎯 批量缓存消息（简化版本）
  batchCacheMessages(messages) {
    if (!Array.isArray(messages)) {
      console.warn('🚨 [MessageCache] batchCacheMessages 需要数组参数');
      return [];
    }

    const results = [];

    try {
      for (const msg of messages) {
        if (msg && msg.id) {
          const result = this.cacheMessage(msg);
          if (result) {
            results.push(result);
          }
        }
      }
    } catch (error) {
      console.error('🚨 [MessageCache] 批量缓存消息时出错:', error);
    }

    return results;
  }

  // 🎯 智能预加载
  preloadMessages(messageIds, loadFunction) {
    const uncachedIds = messageIds.filter(id => !this.cache.has(id));
    
    if (uncachedIds.length === 0) return Promise.resolve();
    
    // 分批预加载
    const batchSize = 20;
    const batches = [];
    
    for (let i = 0; i < uncachedIds.length; i += batchSize) {
      batches.push(uncachedIds.slice(i, i + batchSize));
    }
    
    return Promise.all(
      batches.map(batch => 
        loadFunction(batch).then(messages => 
          messages.forEach(msg => this.cacheMessage(msg))
        )
      )
    );
  }

  // 🎯 消息分页管理
  getMessagePage(allMessages, pageSize = 50, pageIndex = 0) {
    const startIndex = pageIndex * pageSize;
    const endIndex = Math.min(startIndex + pageSize, allMessages.length);
    
    return {
      messages: allMessages.slice(startIndex, endIndex),
      hasMore: endIndex < allMessages.length,
      totalPages: Math.ceil(allMessages.length / pageSize),
      currentPage: pageIndex
    };
  }

  // 🎯 清理过期缓存
  cleanupExpiredCache(maxAge = 30 * 60 * 1000) { // 默认30分钟
    const now = Date.now();
    const expiredKeys = [];
    
    for (const [key, value] of this.cache) {
      if (value.timestamp && (now - new Date(value.timestamp).getTime()) > maxAge) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => {
      this.cache.delete(key);
      this.renderCache.delete(key);
      this.contentHashCache.delete(key);
    });
    
    return expiredKeys.length;
  }
}

// 🎯 创建全局消息缓存实例
const messageCache = new MessageCache(2000); // 缓存最多2000条消息

export default messageCache;
