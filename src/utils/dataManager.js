import { v4 as uuidv4 } from 'uuid';
import { debounce } from 'lodash';
import { KnowledgeBaseManager } from './knowledgeBaseManager.js'; // 🚀 新增：向量知识库管理器

/**
 * 数据管理器 - 负责所有数据的存储和管理
 */
export class DataManager {
  constructor() {
    this.configs = [];
    this.partners = [];
    this.sessions = new Map();
    this.knowledgeBases = []; // 🚀 新增：知识库数据
    this.isInitialized = false;

    // 🚀 新增：向量知识库管理器
    this.knowledgeBaseManager = new KnowledgeBaseManager(this);

    // 防抖保存函数
    this.debouncedSave = debounce(this.saveToStorage.bind(this), 1000);
  }

  /**
   * 初始化数据管理器
   */
  async initialize() {
    try {
      await this.loadFromStorage();
      this.isInitialized = true;
      console.log('数据管理器初始化完成');

      // 🚀 初始化向量知识库管理器
      try {
        await this.knowledgeBaseManager.initialize();
        console.log('✅ 向量知识库管理器初始化完成');
      } catch (error) {
        console.warn('⚠️ 向量知识库管理器初始化失败，将在需要时重试:', error);
      }
    } catch (error) {
      console.error('数据管理器初始化失败:', error);
      // 使用默认数据
      this.configs = [];
      this.partners = [];
      this.sessions = new Map();
      this.knowledgeBases = []; // 🚀 新增：知识库默认数据
      this.isInitialized = true;
    }
  }

  /**
   * 从存储加载数据
   */
  async loadFromStorage() {
    if (window.electronAPI) {
      // Electron环境
      const configs = await window.electronAPI.store.get('configs') || [];
      const partners = await window.electronAPI.store.get('partners') || [];
      const sessions = await window.electronAPI.store.get('sessions') || {};
      const knowledgeBases = await window.electronAPI.store.get('knowledgeBases') || []; // 🚀 新增

      this.configs = configs;
      this.partners = partners;
      this.sessions = new Map(Object.entries(sessions));
      this.knowledgeBases = knowledgeBases; // 🚀 新增
    } else {
      // Web环境（开发时）
      const configs = localStorage.getItem('jdc_configs');
      const partners = localStorage.getItem('jdc_partners');
      const sessions = localStorage.getItem('jdc_sessions');
      const knowledgeBases = localStorage.getItem('jdc_knowledgeBases'); // 🚀 新增

      this.configs = configs ? JSON.parse(configs) : [];
      this.partners = partners ? JSON.parse(partners) : [];
      this.sessions = sessions ? new Map(Object.entries(JSON.parse(sessions))) : new Map();
      this.knowledgeBases = knowledgeBases ? JSON.parse(knowledgeBases) : []; // 🚀 新增
    }
  }

  /**
   * 保存数据到存储
   */
  async saveToStorage() {
    try {
      const sessionsObj = Object.fromEntries(this.sessions);
      
      if (window.electronAPI) {
        // Electron环境
        await window.electronAPI.store.set('configs', this.configs);
        await window.electronAPI.store.set('partners', this.partners);
        await window.electronAPI.store.set('sessions', sessionsObj);
        await window.electronAPI.store.set('knowledgeBases', this.knowledgeBases); // 🚀 新增
      } else {
        // Web环境（开发时）
        localStorage.setItem('jdc_configs', JSON.stringify(this.configs));
        localStorage.setItem('jdc_partners', JSON.stringify(this.partners));
        localStorage.setItem('jdc_sessions', JSON.stringify(sessionsObj));
        localStorage.setItem('jdc_knowledgeBases', JSON.stringify(this.knowledgeBases)); // 🚀 新增
      }
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  }

  // ==================== 模型配置管理 ====================

  /**
   * 获取所有配置组
   */
  getConfigs() {
    return this.configs;
  }

  /**
   * 添加配置组
   */
  addConfig(config) {
    const newConfig = {
      id: uuidv4(),
      name: config.name,
      baseUrl: config.baseUrl,
      apiKey: config.apiKey,
      models: [],
      createdAt: new Date().toISOString()
    };
    
    this.configs.push(newConfig);
    this.debouncedSave();
    return newConfig;
  }

  /**
   * 更新配置组
   */
  updateConfig(configId, updates) {
    const index = this.configs.findIndex(c => c.id === configId);
    if (index !== -1) {
      this.configs[index] = { ...this.configs[index], ...updates };
      this.debouncedSave();
      return this.configs[index];
    }
    return null;
  }

  /**
   * 删除配置组
   */
  deleteConfig(configId) {
    const index = this.configs.findIndex(c => c.id === configId);
    if (index !== -1) {
      this.configs.splice(index, 1);
      this.debouncedSave();
      return true;
    }
    return false;
  }

  /**
   * 添加模型到配置组
   */
  addModel(configId, model) {
    const config = this.configs.find(c => c.id === configId);
    if (config) {
      const newModel = {
        id: uuidv4(),
        name: model.name,
        type: model.type || 'chat', // 🚀 修复：保存模型类型
        supportThinking: model.supportThinking || false,
        supportVision: model.supportVision || false,
        supportWebSearch: model.supportWebSearch || false, // 🚀 修复：保存联网搜索支持
        uploadTypes: model.uploadTypes || [],
        maxTokens: model.maxTokens || 8192, // 🚀 修复：保存最大Token数
        createdAt: new Date().toISOString()
      };

      config.models.push(newModel);
      this.debouncedSave();
      return newModel;
    }
    return null;
  }

  /**
   * 更新模型
   */
  updateModel(configId, modelId, updates) {
    const config = this.configs.find(c => c.id === configId);
    if (config) {
      const modelIndex = config.models.findIndex(m => m.id === modelId);
      if (modelIndex !== -1) {
        config.models[modelIndex] = { ...config.models[modelIndex], ...updates };
        this.debouncedSave();
        return config.models[modelIndex];
      }
    }
    return null;
  }

  /**
   * 删除模型
   */
  deleteModel(configId, modelId) {
    const config = this.configs.find(c => c.id === configId);
    if (config) {
      const modelIndex = config.models.findIndex(m => m.id === modelId);
      if (modelIndex !== -1) {
        config.models.splice(modelIndex, 1);
        this.debouncedSave();
        return true;
      }
    }
    return false;
  }

  /**
   * 获取所有可用的模型（扁平化）
   */
  getAllModels() {
    const models = [];
    this.configs.forEach(config => {
      config.models.forEach(model => {
        models.push({
          ...model,
          configId: config.id,
          configName: config.name,
          baseUrl: config.baseUrl,
          apiKey: config.apiKey
        });
      });
    });
    return models;
  }

  // ==================== 搭档管理 ====================

  /**
   * 获取所有搭档
   */
  getPartners() {
    return this.partners;
  }

  /**
   * 添加搭档
   */
  addPartner(partner) {
    const newPartner = {
      id: uuidv4(),
      name: partner.name,
      prompt: partner.prompt,
      createdAt: new Date().toISOString()
    };
    
    this.partners.push(newPartner);
    this.debouncedSave();
    return newPartner;
  }

  /**
   * 更新搭档
   */
  updatePartner(partnerId, updates) {
    const index = this.partners.findIndex(p => p.id === partnerId);
    if (index !== -1) {
      this.partners[index] = { ...this.partners[index], ...updates };
      this.debouncedSave();
      return this.partners[index];
    }
    return null;
  }

  /**
   * 删除搭档
   */
  deletePartner(partnerId) {
    const index = this.partners.findIndex(p => p.id === partnerId);
    if (index !== -1) {
      this.partners.splice(index, 1);
      this.debouncedSave();
      return true;
    }
    return false;
  }

  // ==================== 会话管理 ====================

  /**
   * 创建新会话
   */
  createSession(type, options = {}) {
    const sessionId = uuidv4();
    const session = {
      id: sessionId,
      type, // 'chatroom' | 'multimodel'
      title: options.title || `${type === 'chatroom' ? '聊天室' : '多模型回答'} - ${new Date().toLocaleString()}`,
      invitedModels: options.invitedModels || [],
      coordinator: options.coordinator || null,
      partnerId: options.partnerId || null,
      knowledgeBases: options.knowledgeBases || [], // 🚀 新增：关联的知识库
      messages: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    this.sessions.set(sessionId, session);
    this.debouncedSave();
    return session;
  }

  /**
   * 获取会话
   */
  getSession(sessionId) {
    return this.sessions.get(sessionId);
  }

  /**
   * 获取所有会话
   */
  getAllSessions() {
    return Array.from(this.sessions.values());
  }

  /**
   * 删除会话
   */
  deleteSession(sessionId) {
    console.log('🗑️ [DataManager] 删除会话:', sessionId);

    try {
      const session = this.sessions.get(sessionId);

      if (!session) {
        console.warn('⚠️ [DataManager] 会话不存在:', sessionId);
        return false;
      }

      // 删除会话
      const deleted = this.sessions.delete(sessionId);

      if (deleted) {
        this.debouncedSave();
        console.log('✅ [DataManager] 会话删除成功:', session.title);
        return true;
      } else {
        console.error('❌ [DataManager] 删除会话失败');
        return false;
      }

    } catch (error) {
      console.error('❌ [DataManager] 删除会话异常:', error);
      return false;
    }
  }

  /**
   * 更新会话
   */
  updateSession(sessionId, updates) {
    const session = this.sessions.get(sessionId);
    if (session) {
      const updatedSession = {
        ...session,
        ...updates,
        updatedAt: new Date().toISOString()
      };
      this.sessions.set(sessionId, updatedSession);
      this.debouncedSave();
      return updatedSession;
    }
    return null;
  }



  /**
   * 添加消息到会话
   */
  addMessage(sessionId, message) {
    const session = this.sessions.get(sessionId);
    if (session) {
      const newMessage = {
        id: uuidv4(),
        ...message,
        timestamp: new Date().toISOString()
      };

      session.messages.push(newMessage);
      session.updatedAt = new Date().toISOString();
      this.sessions.set(sessionId, session);
      this.debouncedSave();
      return newMessage;
    }
    return null;
  }

  /**
   * 清空会话的所有消息
   */
  clearSessionMessages(sessionId) {
    console.log('🧹 [DataManager] 清空会话消息:', sessionId);

    const session = this.sessions.get(sessionId);
    if (session) {
      session.messages = [];
      session.updatedAt = new Date().toISOString();
      this.sessions.set(sessionId, session);
      this.debouncedSave();
      console.log('✅ [DataManager] 会话消息已清空');
      return true;
    }

    console.warn('⚠️ [DataManager] 会话不存在:', sessionId);
    return false;
  }

  /**
   * 设置会话的消息列表（替换所有消息）
   */
  setSessionMessages(sessionId, messages) {
    console.log('💾 [DataManager] 设置会话消息:', sessionId, '消息数量:', messages.length);

    const session = this.sessions.get(sessionId);
    if (session) {
      // 确保每个消息都有必要的属性
      const processedMessages = messages.map(message => ({
        id: message.id || uuidv4(),
        ...message,
        timestamp: message.timestamp || new Date().toISOString()
      }));

      session.messages = processedMessages;
      session.updatedAt = new Date().toISOString();
      this.sessions.set(sessionId, session);
      this.debouncedSave();
      console.log('✅ [DataManager] 会话消息已更新');
      return true;
    }

    console.warn('⚠️ [DataManager] 会话不存在:', sessionId);
    return false;
  }

  /**
   * 获取会话的所有消息
   */
  getSessionMessages(sessionId) {
    const session = this.sessions.get(sessionId);
    if (session) {
      return session.messages || [];
    }
    return [];
  }

  // ==================== 知识库管理 ====================

  /**
   * 获取所有知识库
   */
  getKnowledgeBases() {
    return this.knowledgeBases;
  }

  /**
   * 根据ID获取知识库
   */
  getKnowledgeBase(id) {
    return this.knowledgeBases.find(kb => kb.id === id);
  }

  /**
   * 添加知识库
   */
  addKnowledgeBase(knowledgeBase) {
    const newKB = {
      id: uuidv4(),
      ...knowledgeBase,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      documents: []
    };

    this.knowledgeBases.push(newKB);
    this.debouncedSave();
    return newKB;
  }

  /**
   * 更新知识库
   */
  updateKnowledgeBase(knowledgeBase) {
    const index = this.knowledgeBases.findIndex(kb => kb.id === knowledgeBase.id);
    if (index !== -1) {
      this.knowledgeBases[index] = {
        ...knowledgeBase,
        updatedAt: new Date().toISOString()
      };
      this.debouncedSave();
      return this.knowledgeBases[index];
    }
    return null;
  }

  /**
   * 删除知识库
   */
  deleteKnowledgeBase(id) {
    const index = this.knowledgeBases.findIndex(kb => kb.id === id);
    if (index !== -1) {
      this.knowledgeBases.splice(index, 1);
      this.debouncedSave();
      return true;
    }
    return false;
  }

  /**
   * 添加文档到知识库
   */
  addDocumentToKB(kbId, document) {
    const kb = this.getKnowledgeBase(kbId);
    if (kb) {
      const newDoc = {
        id: uuidv4(),
        ...document,
        createdAt: new Date().toISOString()
      };

      if (!kb.documents) {
        kb.documents = [];
      }

      kb.documents.push(newDoc);
      kb.updatedAt = new Date().toISOString();
      this.debouncedSave();
      return newDoc;
    }
    return null;
  }

  /**
   * 从知识库删除文档
   */
  deleteDocumentFromKB(kbId, docId) {
    const kb = this.getKnowledgeBase(kbId);
    if (kb && kb.documents) {
      const index = kb.documents.findIndex(doc => doc.id === docId);
      if (index !== -1) {
        kb.documents.splice(index, 1);
        kb.updatedAt = new Date().toISOString();
        this.debouncedSave();
        return true;
      }
    }
    return false;
  }

  /**
   * 🚀 新增：更新知识库文档（支持分层结构）
   */
  updateDocument(kbId, updatedDocument) {
    const kb = this.getKnowledgeBase(kbId);
    if (kb && kb.documents) {
      const index = kb.documents.findIndex(doc => doc.id === updatedDocument.id);
      if (index !== -1) {
        // 保持分层结构的完整性
        const existingDoc = kb.documents[index];
        kb.documents[index] = {
          ...existingDoc,
          ...updatedDocument,
          // 保留分层分块相关字段
          hierarchical: updatedDocument.hierarchical !== undefined ? updatedDocument.hierarchical : existingDoc.hierarchical,
          parentChunkCount: updatedDocument.parentChunkCount || existingDoc.parentChunkCount,
          childChunkCount: updatedDocument.childChunkCount || existingDoc.childChunkCount,
          updatedAt: new Date().toISOString()
        };
        kb.updatedAt = new Date().toISOString();
        this.debouncedSave();
        return kb.documents[index];
      }
    }
    return null;
  }

  /**
   * 搜索知识库文档
   */
  searchKnowledgeBase(kbId, query, limit = 5) {
    const kb = this.getKnowledgeBase(kbId);
    if (!kb || !kb.documents) {
      return [];
    }

    // 简单的文本匹配搜索（实际应该使用向量相似度搜索）
    const results = kb.documents
      .filter(doc =>
        doc.title.toLowerCase().includes(query.toLowerCase()) ||
        doc.content.toLowerCase().includes(query.toLowerCase())
      )
      .map(doc => ({
        ...doc,
        relevance: this.calculateRelevance(doc, query)
      }))
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, limit);

    return results;
  }

  /**
   * 计算文档相关度（简单实现）
   */
  calculateRelevance(document, query) {
    const queryLower = query.toLowerCase();
    const titleLower = document.title.toLowerCase();
    const contentLower = document.content.toLowerCase();

    let score = 0;

    // 标题匹配权重更高
    if (titleLower.includes(queryLower)) {
      score += 0.8;
    }

    // 内容匹配
    if (contentLower.includes(queryLower)) {
      score += 0.5;
    }

    // 计算关键词密度
    const words = queryLower.split(' ');
    words.forEach(word => {
      const titleMatches = (titleLower.match(new RegExp(word, 'g')) || []).length;
      const contentMatches = (contentLower.match(new RegExp(word, 'g')) || []).length;
      score += (titleMatches * 0.1) + (contentMatches * 0.05);
    });

    return Math.min(score, 1); // 限制在0-1之间
  }

  // ==================== 向量知识库管理 ====================

  /**
   * 添加文档到知识库（带向量化）
   */
  async addDocumentWithVectorization(knowledgeBaseId, document, onProgress = null) {
    try {
      // 使用向量管理器处理文档
      const result = await this.knowledgeBaseManager.addDocument(knowledgeBaseId, document, onProgress);

      // 更新知识库的文档列表（向量管理器已经处理了）
      const kb = this.getKnowledgeBase(knowledgeBaseId);
      if (kb) {
        kb.updatedAt = new Date().toISOString();
        this.debouncedSave();
      }

      return result;
    } catch (error) {
      console.error('❌ [DataManager] 向量化文档添加失败:', error);
      throw error;
    }
  }

  /**
   * 向量搜索知识库
   */
  async vectorSearchKnowledgeBase(knowledgeBaseId, query, options = {}) {
    try {
      return await this.knowledgeBaseManager.searchKnowledgeBase(knowledgeBaseId, query, options);
    } catch (error) {
      console.error('❌ [DataManager] 向量搜索失败:', error);
      throw error;
    }
  }

  /**
   * 删除知识库（包括向量数据）
   */
  async deleteKnowledgeBaseWithVectors(id) {
    try {
      // 先删除向量数据
      await this.knowledgeBaseManager.deleteKnowledgeBaseVectors(id);

      // 再删除知识库记录
      return this.deleteKnowledgeBase(id);
    } catch (error) {
      console.error('❌ [DataManager] 删除知识库失败:', error);
      // 即使向量删除失败，也尝试删除知识库记录
      return this.deleteKnowledgeBase(id);
    }
  }

  /**
   * 删除文档（包括向量数据）
   */
  async deleteDocumentWithVectors(kbId, docId) {
    try {
      // 先删除向量数据
      await this.knowledgeBaseManager.deleteDocumentVectors(kbId, docId);

      // 再删除文档记录
      return this.deleteDocumentFromKB(kbId, docId);
    } catch (error) {
      console.error('❌ [DataManager] 删除文档失败:', error);
      // 即使向量删除失败，也尝试删除文档记录
      return this.deleteDocumentFromKB(kbId, docId);
    }
  }

  /**
   * 获取可用的embedding模型
   */
  getAvailableEmbeddingModels() {
    return this.knowledgeBaseManager.getAvailableEmbeddingModels();
  }

  /**
   * 测试embedding模型
   */
  async testEmbeddingModel(modelId) {
    return await this.knowledgeBaseManager.testEmbeddingModel(modelId);
  }

  /**
   * 获取向量数据库统计信息
   */
  async getVectorDBStats() {
    return await this.knowledgeBaseManager.getVectorDBStats();
  }

  /**
   * 🚀 新增：删除特定向量
   */
  async deleteVector(kbId, vectorId) {
    try {
      return await this.knowledgeBaseManager.vectorDB.deleteVector(vectorId);
    } catch (error) {
      console.error('❌ [DataManager] 删除向量失败:', error);
      throw error;
    }
  }

  /**
   * 🚀 新增：生成文本向量（支持分层向量）
   */
  async generateEmbedding(text, model, options = {}) {
    try {
      const {
        chunkType = 'manual',
        level = 0,
        parentId = null,
        knowledgeBaseId = null,
        documentId = null
      } = options;

      const vector = await this.knowledgeBaseManager.embeddingService.textToVector(text, model);

      // 生成新的向量ID
      const vectorId = `${chunkType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 如果提供了知识库和文档信息，直接添加到向量数据库
      if (knowledgeBaseId && documentId) {
        await this.knowledgeBaseManager.vectorDB.addVector(vectorId, vector, {
          knowledgeBaseId,
          documentId,
          chunkText: text,
          chunkType,
          level,
          parentId,
          createdAt: new Date().toISOString()
        });
      }

      return {
        success: true,
        vectorId,
        vector,
        text,
        metadata: {
          chunkType,
          level,
          parentId,
          knowledgeBaseId,
          documentId
        }
      };
    } catch (error) {
      console.error('❌ [DataManager] 生成向量失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 清理向量缓存
   */
  async cleanupVectorCache() {
    await this.knowledgeBaseManager.cleanup();
  }

  /**
   * 获取所有知识库
   */
  getAllKnowledgeBases() {
    return this.knowledgeBases;
  }



  /**
   * 🚀 新增：更新知识库中的文档
   * @param {string} knowledgeBaseId - 知识库ID
   * @param {Object} document - 要更新的文档对象
   * @returns {boolean} 更新是否成功
   */
  updateDocumentInKB(knowledgeBaseId, document) {
    try {
      // 查找知识库
      const kbIndex = this.knowledgeBases.findIndex(kb => kb.id === knowledgeBaseId);
      if (kbIndex === -1) {
        console.error('❌ [DataManager] 知识库不存在:', knowledgeBaseId);
        return false;
      }

      // 确保知识库有documents数组
      if (!this.knowledgeBases[kbIndex].documents) {
        this.knowledgeBases[kbIndex].documents = [];
      }

      // 查找文档
      const docIndex = this.knowledgeBases[kbIndex].documents.findIndex(doc => doc.id === document.id);

      if (docIndex !== -1) {
        // 更新现有文档
        this.knowledgeBases[kbIndex].documents[docIndex] = {
          ...this.knowledgeBases[kbIndex].documents[docIndex],
          ...document,
          updatedAt: new Date().toISOString()
        };
        console.log('✅ [DataManager] 文档已更新:', document.id);
      } else {
        // 添加新文档
        const newDocument = {
          ...document,
          createdAt: document.createdAt || new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        this.knowledgeBases[kbIndex].documents.push(newDocument);
        console.log('✅ [DataManager] 文档已添加到知识库:', document.id);
      }

      // 更新知识库的修改时间
      this.knowledgeBases[kbIndex].updatedAt = new Date().toISOString();

      // 保存数据
      this.debouncedSave();
      return true;

    } catch (error) {
      console.error('❌ [DataManager] 更新文档失败:', error);
      return false;
    }
  }
}
