// 🎯 复制功能工具函数

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @param {string} successMessage - 成功提示消息
 * @returns {Promise<boolean>} - 复制是否成功
 */
export const copyToClipboard = async (text, successMessage = '已复制到剪贴板') => {
  try {
    // 优先使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      console.log('✅ [复制] 使用 Clipboard API 复制成功');

      // 显示成功提示
      if (window.antd && window.antd.message) {
        window.antd.message.success(successMessage);
      }

      return true;
    } else {
      // 降级到传统的 execCommand 方法
      return fallbackCopyToClipboard(text, successMessage);
    }
  } catch (error) {
    console.error('❌ [复制] Clipboard API 复制失败:', error);
    // 降级到传统方法
    return fallbackCopyToClipboard(text, successMessage);
  }
};

/**
 * 降级复制方法（兼容旧浏览器）
 * @param {string} text - 要复制的文本
 * @param {string} successMessage - 成功提示消息
 * @returns {boolean} - 复制是否成功
 */
const fallbackCopyToClipboard = (text, successMessage) => {
  try {
    // 创建临时文本区域
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 设置样式使其不可见
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.opacity = '0';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    // 执行复制命令
    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);
    
    if (successful) {
      console.log('✅ [复制] 使用 execCommand 复制成功');
      
      // 显示成功提示
      if (window.antd && window.antd.message) {
        window.antd.message.success(successMessage);
      }
      
      return true;
    } else {
      throw new Error('execCommand 复制失败');
    }
  } catch (error) {
    console.error('❌ [复制] 降级复制方法失败:', error);
    
    // 显示错误提示
    if (window.antd && window.antd.message) {
      window.antd.message.error('复制失败，请手动复制');
    }
    
    return false;
  }
};

/**
 * 从HTML元素中提取纯文本内容
 * @param {HTMLElement} element - HTML元素
 * @returns {string} - 纯文本内容
 */
export const extractTextFromElement = (element) => {
  if (!element) return '';

  // 克隆元素以避免修改原始DOM
  const clonedElement = element.cloneNode(true);

  // 移除不需要的元素（如复制按钮）
  const copyButtons = clonedElement.querySelectorAll('.copy-button, .copy-btn');
  copyButtons.forEach(btn => btn.remove());

  // 处理代码块，保持格式
  const codeBlocks = clonedElement.querySelectorAll('pre code');
  codeBlocks.forEach(code => {
    const text = code.textContent || code.innerText;
    code.textContent = text;
  });

  // 获取纯文本内容
  return clonedElement.textContent || clonedElement.innerText || '';
};

/**
 * 安全地提取React children中的文本内容（保持代码格式）
 * @param {any} children - React children
 * @returns {string} - 提取的文本内容
 */
export const extractCodeFromChildren = (children) => {
  if (!children) return '';

  // 如果是字符串，保持原始格式（不trim，保持换行和缩进）
  if (typeof children === 'string') {
    return children;
  }

  // 如果是数组，递归处理每个元素
  if (Array.isArray(children)) {
    return children.map(child => extractCodeFromChildren(child)).join('');
  }

  // 如果是React元素，提取其文本内容
  if (children && typeof children === 'object') {
    if (children.props && children.props.children) {
      return extractCodeFromChildren(children.props.children);
    }

    // 如果有toString方法，使用它
    if (typeof children.toString === 'function') {
      const str = children.toString();
      // 避免返回 [object Object]
      if (str !== '[object Object]') {
        return str;
      }
    }
  }

  // 最后的降级方案
  return String(children).replace(/\[object Object\]/g, '');
};

/**
 * 从Markdown内容中提取代码块
 * @param {string} content - Markdown内容
 * @param {number} blockIndex - 代码块索引
 * @returns {string} - 代码块内容
 */
export const extractCodeBlock = (content, blockIndex = 0) => {
  if (!content) return '';

  // 匹配代码块的正则表达式
  const codeBlockRegex = /```[\s\S]*?\n([\s\S]*?)```/g;
  const matches = [];
  let match;

  while ((match = codeBlockRegex.exec(content)) !== null) {
    matches.push(match[1].trim());
  }

  return matches[blockIndex] || '';
};

/**
 * 将Markdown内容转换为纯文本
 * @param {string} markdown - Markdown内容
 * @returns {string} - 纯文本内容
 */
export const markdownToPlainText = (markdown) => {
  if (!markdown) return '';

  let text = markdown;

  // 移除代码块（保留内容）
  text = text.replace(/```[\s\S]*?\n([\s\S]*?)```/g, '$1');

  // 移除内联代码标记
  text = text.replace(/`([^`]+)`/g, '$1');

  // 移除链接，保留文本
  text = text.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');

  // 移除图片
  text = text.replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1');

  // 移除标题标记
  text = text.replace(/^#{1,6}\s+/gm, '');

  // 移除粗体和斜体标记
  text = text.replace(/\*\*([^*]+)\*\*/g, '$1');
  text = text.replace(/\*([^*]+)\*/g, '$1');
  text = text.replace(/__([^_]+)__/g, '$1');
  text = text.replace(/_([^_]+)_/g, '$1');

  // 移除删除线
  text = text.replace(/~~([^~]+)~~/g, '$1');

  // 移除引用标记
  text = text.replace(/^>\s+/gm, '');

  // 移除列表标记
  text = text.replace(/^[\s]*[-*+]\s+/gm, '');
  text = text.replace(/^[\s]*\d+\.\s+/gm, '');

  // 移除水平线
  text = text.replace(/^[-*_]{3,}$/gm, '');

  // 移除表格标记
  text = text.replace(/\|/g, ' ');
  text = text.replace(/^[\s]*:?-+:?[\s]*$/gm, '');

  // 清理多余的空白
  text = text.replace(/\n\s*\n/g, '\n\n'); // 多个空行变成两个
  text = text.replace(/[ \t]+/g, ' '); // 多个空格变成一个
  text = text.trim();

  return text;
};

/**
 * 🚀 检查思考内容是否为无效内容（只包含"Thinking..."等）
 * @param {string} content - 思考内容
 * @returns {boolean} - 是否为无效内容
 */
const isInvalidThinkingContent = (content) => {
  if (!content || typeof content !== 'string') {
    return true;
  }

  // 清理内容：移除空白字符、换行符等，转为小写
  const cleanContent = content.trim().replace(/\s+/g, ' ').toLowerCase();

  // 如果清理后内容为空，则为无效
  if (!cleanContent) {
    return true;
  }

  // 定义无效内容模式
  const invalidPatterns = [
    /^thinking\.{0,}$/,                    // "thinking" 或 "thinking..."
    /^thinking\.{1,}$/,                    // "thinking." 或 "thinking..."
    /^\.{3,}$/,                            // 只有省略号 "..."
    /^thinking\s*\.{0,}\s*$/,              // "thinking" 加空格和可选的点
    /^思考中\.{0,}$/,                       // 中文"思考中"
    /^正在思考\.{0,}$/,                     // 中文"正在思考"
    /^(thinking\.{0,}\s*){2,}$/,           // 多个"thinking..."重复
    /^thinking\s+thinking\.{0,}$/,         // "thinking thinking..."
    /^thinking\.{1,}\s+thinking\.{0,}$/,   // "thinking... thinking..."
  ];

  // 检查是否匹配任何无效模式
  const isInvalid = invalidPatterns.some(pattern => pattern.test(cleanContent));

  if (isInvalid) {
    console.log('🗑️ [isInvalidThinkingContent] 检测到无效思考内容:', content.substring(0, 50));
  }

  return isInvalid;
};

/**
 * 🚀 解析消息内容中的<think>标签
 * @param {string} content - 包含think标签的消息内容
 * @returns {Object} - 解析结果 { content: string, thinkingSections: Array }
 */
export const parseThinkTags = (content) => {
  if (!content || typeof content !== 'string') {
    return { content: content || '', thinkingSections: [] };
  }

  const thinkingSections = [];
  let processedContent = content;
  let thinkIndex = 0;

  // 🎯 使用正则表达式匹配<think>标签（支持嵌套）
  const thinkRegex = /<think>([\s\S]*?)<\/think>/gi;
  let match;

  while ((match = thinkRegex.exec(content)) !== null) {
    const fullMatch = match[0];
    const thinkingContent = match[1].trim();

    // 🎯 检查思考内容是否有效
    if (thinkingContent && !isInvalidThinkingContent(thinkingContent)) {
      // 生成唯一ID
      const sectionId = `think_${Date.now()}_${thinkIndex++}`;

      // 添加到思考段落列表
      thinkingSections.push({
        id: sectionId,
        content: thinkingContent,
        startIndex: match.index,
        endIndex: match.index + fullMatch.length
      });

      // 从原内容中移除think标签，替换为占位符
      processedContent = processedContent.replace(fullMatch, `[THINKING_PLACEHOLDER_${sectionId}]`);
    } else {
      // 🎯 无效的思考内容，直接移除整个标签
      processedContent = processedContent.replace(fullMatch, '');
      console.log('🗑️ [parseThinkTags] 移除无效思考内容:', thinkingContent);
    }
  }

  // 🎯 清理多余的空行和空格
  processedContent = processedContent
    .replace(/\n\s*\n\s*\n/g, '\n\n')  // 多个连续空行合并为两个
    .replace(/^\s+|\s+$/g, '')          // 移除首尾空白
    .trim();

  return {
    content: processedContent,
    thinkingSections: thinkingSections
  };
};

/**
 * 🚀 重建包含思考标签的完整内容（用于复制等场景）
 * @param {string} content - 处理后的内容
 * @param {Array} thinkingSections - 思考段落列表
 * @returns {string} - 完整的原始内容
 */
export const reconstructContentWithThinking = (content, thinkingSections) => {
  if (!content || !thinkingSections || thinkingSections.length === 0) {
    return content || '';
  }

  let reconstructed = content;

  // 按ID顺序替换占位符
  thinkingSections.forEach(section => {
    const placeholder = `[THINKING_PLACEHOLDER_${section.id}]`;
    const thinkTag = `<think>${section.content}</think>`;
    reconstructed = reconstructed.replace(placeholder, thinkTag);
  });

  return reconstructed;
};

/**
 * 格式化复制的文本
 * @param {string} text - 原始文本
 * @param {string} type - 文本类型 ('message', 'code', 'thinking')
 * @returns {string} - 格式化后的文本
 */
export const formatCopyText = (text, type = 'message') => {
  if (!text) return '';

  switch (type) {
    case 'code':
      // 代码块保持原始格式
      return text.trim();
      
    case 'thinking':
      // 思考过程添加标识
      return `[思考过程]\n${text.trim()}`;
      
    case 'message':
    default:
      // 普通消息清理多余空白
      return text.trim().replace(/\n\s*\n/g, '\n\n');
  }
};

/**
 * 获取复制按钮的样式
 * @param {string} position - 位置 ('hover', 'corner')
 * @returns {object} - 样式对象
 */
export const getCopyButtonStyle = (position = 'hover') => {
  const baseStyle = {
    background: 'rgba(0, 212, 255, 0.1)',
    border: '1px solid rgba(0, 212, 255, 0.3)',
    borderRadius: '6px',
    color: '#00d4ff',
    cursor: 'pointer',
    fontSize: '12px',
    padding: '4px 8px',
    transition: 'all 0.3s ease',
    zIndex: 10,
    backdropFilter: 'blur(10px)',
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    fontWeight: '500'
  };
  
  switch (position) {
    case 'corner':
      return {
        ...baseStyle,
        position: 'absolute',
        top: '6px',
        right: '6px',
        fontSize: '10px',
        padding: '2px 5px',
        borderRadius: '4px'
      };
      
    case 'hover':
    default:
      return {
        ...baseStyle,
        position: 'absolute',
        top: '8px',
        right: '8px',
        opacity: 0,
        transform: 'translateY(-2px)'
      };
  }
};

/**
 * 获取复制按钮的悬浮样式
 * @returns {object} - 悬浮样式对象
 */
export const getCopyButtonHoverStyle = () => ({
  background: 'rgba(0, 212, 255, 0.2)',
  borderColor: '#00d4ff',
  boxShadow: '0 0 10px rgba(0, 212, 255, 0.4)',
  transform: 'translateY(-1px)'
});

// 设置全局message引用（用于显示提示）
export const setMessageInstance = (messageInstance) => {
  window.antd = window.antd || {};
  window.antd.message = messageInstance;
};
