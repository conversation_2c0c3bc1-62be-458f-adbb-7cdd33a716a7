/**
 * 🎯 快捷键管理器
 * 统一管理应用内的所有快捷键功能
 */
export class KeyboardShortcuts {
  constructor() {
    this.listeners = new Map();
    this.isEnabled = true;
    this.platform = this.detectPlatform();
    
    // 🎯 快捷键配置
    this.shortcuts = {
      // 模块切换
      'switch_to_chat': { key: '1', ctrlKey: true, description: '切换到智能对话' },
      'switch_to_multimodel': { key: '2', ctrl<PERSON>ey: true, description: '切换到模型对比' },
      'switch_to_knowledge': { key: '3', ctrlKey: true, description: '切换到知识库' },
      'switch_to_config': { key: '4', ctrlKey: true, description: '切换到模型管理' },
      'switch_to_partner': { key: '5', ctrlKey: true, description: '切换到协作配置' },
      
      // 功能操作
      'new_session': { key: 'n', ctrlKey: true, description: '新建会话' },
      'save_session': { key: 's', ctrlKey: true, description: '保存会话' },
      'open_session': { key: 'l', ctrlKey: true, description: '加载会话' },
      'toggle_clipboard': { key: 'k', ctrlKey: true, description: '切换智能剪贴板' },
      'show_shortcuts': { key: 'o', ctrlKey: true, description: '显示快捷键帮助' },
      'refresh': { key: 'r', ctrlKey: true, description: '刷新当前页面' },
      
      // 应用控制
      'toggle_fullscreen': { key: 'F11', description: '切换全屏模式' },
      'close_dialog': { key: 'Escape', description: '关闭弹窗/抽屉' },
      'quit_app': { key: 'q', ctrlKey: true, description: '退出应用' },
      
      // 输入相关
      'send_message': { key: 'Enter', ctrlKey: true, description: '发送消息' },
      'new_line': { key: 'Enter', shiftKey: true, description: '换行' },
      
      // 开发调试
      'toggle_devtools': { key: 'F12', description: '开发者工具' },
    };
  }

  /**
   * 检测操作系统平台
   */
  detectPlatform() {
    const userAgent = navigator.userAgent.toLowerCase();
    if (userAgent.indexOf('mac') !== -1) return 'mac';
    if (userAgent.indexOf('win') !== -1) return 'windows';
    return 'linux';
  }

  /**
   * 获取修饰键名称（根据平台）
   */
  getModifierKey() {
    return this.platform === 'mac' ? 'Cmd' : 'Ctrl';
  }

  /**
   * 格式化快捷键显示文本
   */
  formatShortcut(shortcut) {
    const parts = [];
    
    if (shortcut.ctrlKey) {
      parts.push(this.getModifierKey());
    }
    if (shortcut.shiftKey) {
      parts.push('Shift');
    }
    if (shortcut.altKey) {
      parts.push('Alt');
    }
    
    // 特殊键处理
    let keyName = shortcut.key;
    if (keyName === ' ') keyName = 'Space';
    if (keyName === 'Escape') keyName = 'Esc';
    
    parts.push(keyName.toUpperCase());
    
    return parts.join(' + ');
  }

  /**
   * 检查按键事件是否匹配快捷键
   */
  matchesShortcut(event, shortcut) {
    const eventCtrlKey = this.platform === 'mac' ? event.metaKey : event.ctrlKey;
    
    return (
      event.key.toLowerCase() === shortcut.key.toLowerCase() &&
      !!eventCtrlKey === !!shortcut.ctrlKey &&
      !!event.shiftKey === !!shortcut.shiftKey &&
      !!event.altKey === !!shortcut.altKey
    );
  }

  /**
   * 注册快捷键监听器
   */
  register(action, callback) {
    if (!this.shortcuts[action]) {
      console.warn(`未找到快捷键配置: ${action}`);
      return;
    }

    this.listeners.set(action, callback);
  }

  /**
   * 取消注册快捷键监听器
   */
  unregister(action) {
    this.listeners.delete(action);
  }

  /**
   * 处理键盘事件
   */
  handleKeyDown = (event) => {
    if (!this.isEnabled) return;

    // 如果焦点在输入框中，只处理特定快捷键
    const isInputFocused = ['INPUT', 'TEXTAREA'].includes(event.target.tagName) ||
                          event.target.contentEditable === 'true';

    for (const [action, shortcut] of Object.entries(this.shortcuts)) {
      if (this.matchesShortcut(event, shortcut)) {
        // 对于输入框，只允许特定快捷键
        if (isInputFocused && !this.isInputAllowed(action)) {
          continue;
        }

        const callback = this.listeners.get(action);
        if (callback) {
          event.preventDefault();
          event.stopPropagation();
          callback(event);
          break;
        }
      }
    }
  };

  /**
   * 检查在输入框中是否允许此快捷键
   */
  isInputAllowed(action) {
    const allowedInInput = [
      'send_message',
      'new_line',
      'show_shortcuts',
      'toggle_clipboard',
      'close_dialog',
      'quit_app',
      'toggle_fullscreen',
      'toggle_devtools'
    ];
    return allowedInInput.includes(action);
  }

  /**
   * 启用快捷键
   */
  enable() {
    this.isEnabled = true;
    document.addEventListener('keydown', this.handleKeyDown);
  }

  /**
   * 禁用快捷键
   */
  disable() {
    this.isEnabled = false;
    document.removeEventListener('keydown', this.handleKeyDown);
  }

  /**
   * 获取所有快捷键配置（用于帮助显示）
   */
  getAllShortcuts() {
    const categories = {
      navigation: {
        title: '导航切换',
        shortcuts: ['switch_to_chat', 'switch_to_multimodel', 'switch_to_knowledge', 'switch_to_config', 'switch_to_partner']
      },
      actions: {
        title: '功能操作',
        shortcuts: ['new_session', 'save_session', 'open_session', 'toggle_clipboard', 'refresh']
      },
      input: {
        title: '输入相关',
        shortcuts: ['send_message', 'new_line']
      },
      app: {
        title: '应用控制',
        shortcuts: ['show_shortcuts', 'toggle_fullscreen', 'close_dialog', 'quit_app']
      },
      dev: {
        title: '开发调试',
        shortcuts: ['toggle_devtools']
      }
    };

    // 构建分类的快捷键数据
    const result = {};
    for (const [categoryKey, category] of Object.entries(categories)) {
      result[categoryKey] = {
        title: category.title,
        items: category.shortcuts.map(action => ({
          action,
          shortcut: this.formatShortcut(this.shortcuts[action]),
          description: this.shortcuts[action].description
        }))
      };
    }

    return result;
  }

  /**
   * 清理资源
   */
  destroy() {
    this.disable();
    this.listeners.clear();
  }
}

// 创建全局实例
export const globalShortcuts = new KeyboardShortcuts(); 