/**
 * 性能监控工具
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.isMonitoring = false;
    this.startTime = null;
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    this.isMonitoring = true;
    this.startTime = performance.now();
    this.metrics.clear();
    
    // 监控内存使用
    this.monitorMemory();
    
    // 监控渲染性能
    this.monitorRenderPerformance();
    
    console.log('🔍 性能监控已启动');
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.isMonitoring = false;
    const totalTime = performance.now() - this.startTime;
    
    console.log('⏹️ 性能监控已停止');
    console.log(`总监控时间: ${totalTime.toFixed(2)}ms`);
    
    return this.generateReport();
  }

  /**
   * 记录性能指标
   */
  recordMetric(name, value, unit = 'ms') {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    this.metrics.get(name).push({
      value,
      unit,
      timestamp: performance.now()
    });
  }

  /**
   * 测量函数执行时间
   */
  async measureFunction(name, fn) {
    const startTime = performance.now();
    
    try {
      const result = await fn();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.recordMetric(`function_${name}`, duration);
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.recordMetric(`function_${name}_error`, duration);
      throw error;
    }
  }

  /**
   * 监控内存使用
   */
  monitorMemory() {
    if (!this.isMonitoring) return;
    
    if (performance.memory) {
      const memory = performance.memory;
      
      this.recordMetric('memory_used', memory.usedJSHeapSize / 1024 / 1024, 'MB');
      this.recordMetric('memory_total', memory.totalJSHeapSize / 1024 / 1024, 'MB');
      this.recordMetric('memory_limit', memory.jsHeapSizeLimit / 1024 / 1024, 'MB');
    }
    
    // 每5秒记录一次内存使用
    setTimeout(() => this.monitorMemory(), 5000);
  }

  /**
   * 监控渲染性能
   */
  monitorRenderPerformance() {
    if (!this.isMonitoring) return;
    
    // 监控FPS
    let lastTime = performance.now();
    let frameCount = 0;
    
    const measureFPS = () => {
      if (!this.isMonitoring) return;
      
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        const fps = frameCount;
        this.recordMetric('fps', fps, 'fps');
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measureFPS);
    };
    
    requestAnimationFrame(measureFPS);
  }

  /**
   * 监控API调用性能
   */
  monitorAPICall(modelName, duration, success) {
    this.recordMetric(`api_${modelName}_duration`, duration);
    this.recordMetric(`api_${modelName}_${success ? 'success' : 'error'}`, 1, 'count');
  }

  /**
   * 监控组件渲染时间
   */
  monitorComponentRender(componentName, duration) {
    this.recordMetric(`render_${componentName}`, duration);
  }

  /**
   * 监控用户交互响应时间
   */
  monitorUserInteraction(actionName, duration) {
    this.recordMetric(`interaction_${actionName}`, duration);
  }

  /**
   * 获取指标统计
   */
  getMetricStats(metricName) {
    const values = this.metrics.get(metricName);
    if (!values || values.length === 0) {
      return null;
    }
    
    const numericValues = values.map(v => v.value);
    const sum = numericValues.reduce((a, b) => a + b, 0);
    const avg = sum / numericValues.length;
    const min = Math.min(...numericValues);
    const max = Math.max(...numericValues);
    
    // 计算中位数
    const sorted = [...numericValues].sort((a, b) => a - b);
    const median = sorted.length % 2 === 0
      ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
      : sorted[Math.floor(sorted.length / 2)];
    
    // 计算95百分位
    const p95Index = Math.floor(sorted.length * 0.95);
    const p95 = sorted[p95Index] || max;
    
    return {
      count: values.length,
      sum,
      avg,
      min,
      max,
      median,
      p95,
      unit: values[0]?.unit || 'ms'
    };
  }

  /**
   * 生成性能报告
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      duration: performance.now() - this.startTime,
      metrics: {}
    };
    
    // 处理所有指标
    for (const [name, values] of this.metrics) {
      report.metrics[name] = this.getMetricStats(name);
    }
    
    // 生成控制台报告
    this.printConsoleReport(report);
    
    return report;
  }

  /**
   * 打印控制台报告
   */
  printConsoleReport(report) {
    console.log('\n📊 性能监控报告');
    console.log('='.repeat(60));
    console.log(`监控时长: ${(report.duration / 1000).toFixed(2)}秒`);
    console.log(`生成时间: ${new Date(report.timestamp).toLocaleString()}`);
    console.log('='.repeat(60));
    
    // 按类别分组显示指标
    const categories = {
      'API调用': [],
      '组件渲染': [],
      '用户交互': [],
      '系统性能': [],
      '函数执行': []
    };
    
    for (const [name, stats] of Object.entries(report.metrics)) {
      if (name.startsWith('api_')) {
        categories['API调用'].push({ name, stats });
      } else if (name.startsWith('render_')) {
        categories['组件渲染'].push({ name, stats });
      } else if (name.startsWith('interaction_')) {
        categories['用户交互'].push({ name, stats });
      } else if (name.startsWith('memory_') || name.startsWith('fps')) {
        categories['系统性能'].push({ name, stats });
      } else if (name.startsWith('function_')) {
        categories['函数执行'].push({ name, stats });
      }
    }
    
    // 显示各类别的指标
    for (const [category, metrics] of Object.entries(categories)) {
      if (metrics.length > 0) {
        console.log(`\n📈 ${category}:`);
        metrics.forEach(({ name, stats }) => {
          if (stats) {
            console.log(`  ${name}:`);
            console.log(`    平均值: ${stats.avg.toFixed(2)}${stats.unit}`);
            console.log(`    最小值: ${stats.min.toFixed(2)}${stats.unit}`);
            console.log(`    最大值: ${stats.max.toFixed(2)}${stats.unit}`);
            console.log(`    95%分位: ${stats.p95.toFixed(2)}${stats.unit}`);
            console.log(`    样本数: ${stats.count}`);
          }
        });
      }
    }
    
    // 性能建议
    this.generatePerformanceRecommendations(report);
  }

  /**
   * 生成性能建议
   */
  generatePerformanceRecommendations(report) {
    console.log('\n💡 性能建议:');
    
    const recommendations = [];
    
    // 检查内存使用
    const memoryUsed = report.metrics['memory_used'];
    if (memoryUsed && memoryUsed.max > 100) {
      recommendations.push('内存使用较高，建议优化大对象的使用和及时清理引用');
    }
    
    // 检查FPS
    const fps = report.metrics['fps'];
    if (fps && fps.avg < 30) {
      recommendations.push('帧率较低，建议优化渲染性能和减少重复渲染');
    }
    
    // 检查API调用时间
    for (const [name, stats] of Object.entries(report.metrics)) {
      if (name.includes('api_') && name.includes('_duration') && stats.avg > 5000) {
        recommendations.push(`${name} API调用时间较长，建议优化网络请求或增加超时处理`);
      }
    }
    
    // 检查组件渲染时间
    for (const [name, stats] of Object.entries(report.metrics)) {
      if (name.includes('render_') && stats.avg > 100) {
        recommendations.push(`${name} 组件渲染时间较长，建议使用React.memo或优化渲染逻辑`);
      }
    }
    
    if (recommendations.length === 0) {
      console.log('  ✅ 性能表现良好，无需特别优化');
    } else {
      recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });
    }
  }

  /**
   * 导出性能数据
   */
  exportData() {
    const data = {
      timestamp: new Date().toISOString(),
      metrics: Object.fromEntries(this.metrics),
      summary: this.generateReport()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log('📁 性能数据已导出');
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 导出便捷函数
export const startPerformanceMonitoring = () => performanceMonitor.startMonitoring();
export const stopPerformanceMonitoring = () => performanceMonitor.stopMonitoring();
export const measureFunction = (name, fn) => performanceMonitor.measureFunction(name, fn);
export const recordMetric = (name, value, unit) => performanceMonitor.recordMetric(name, value, unit);
