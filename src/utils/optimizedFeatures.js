/**
 * 优化功能使用示例和工具函数
 * 展示如何在渲染进程中使用主进程的优化服务
 */

// 导入前端文档处理器
import { documentProcessorManager } from './documentProcessors.js';

/**
 * 检查优化功能是否可用
 */
export function isOptimizedFeaturesAvailable() {
  return window.electronAPI && 
         window.electronAPI.vectorSearch && 
         window.electronAPI.cache &&
         window.electronAPI.document &&
         window.electronAPI.version &&
         window.electronAPI.knowledgeGraph;
}

/**
 * 🚀 优化的向量搜索包装器
 */
export class OptimizedVectorSearch {
  constructor() {
    this.isAvailable = isOptimizedFeaturesAvailable();
    this.fallbackSearch = null; // 备用搜索方法
  }

  /**
   * 执行向量搜索
   * @param {Array} queryVector - 查询向量
   * @param {Array} vectors - 向量数据
   * @param {Object} options - 搜索选项
   */
  async search(queryVector, vectors, options = {}) {
    if (this.isAvailable) {
      try {
        console.log('⚡ [OptimizedSearch] 使用主进程优化搜索');
        return await window.electronAPI.vectorSearch(queryVector, vectors, options);
      } catch (error) {
        console.warn('⚠️ [OptimizedSearch] 主进程搜索失败，回退到本地搜索:', error);
      }
    }

    // 回退到本地搜索
    if (this.fallbackSearch) {
      console.log('🔍 [OptimizedSearch] 使用本地搜索');
      return await this.fallbackSearch(queryVector, vectors, options);
    }

    throw new Error('向量搜索不可用');
  }

  /**
   * 设置备用搜索方法
   */
  setFallbackSearch(searchFunction) {
    this.fallbackSearch = searchFunction;
  }

  /**
   * 获取搜索统计
   */
  async getStats() {
    if (this.isAvailable) {
      try {
        return await window.electronAPI.vectorSearchStats();
      } catch (error) {
        console.warn('⚠️ [OptimizedSearch] 获取统计失败:', error);
      }
    }
    return null;
  }
}

/**
 * 🚀 智能缓存管理器
 */
export class SmartCacheManager {
  constructor() {
    this.isAvailable = isOptimizedFeaturesAvailable();
    this.localCache = new Map(); // 本地备用缓存
  }

  /**
   * 获取缓存
   */
  async get(key, category = 'vectors') {
    if (this.isAvailable) {
      try {
        return await window.electronAPI.cache.get(key, category);
      } catch (error) {
        console.warn('⚠️ [SmartCache] 主进程缓存获取失败:', error);
      }
    }

    // 回退到本地缓存
    return this.localCache.get(`${category}:${key}`);
  }

  /**
   * 设置缓存
   */
  async set(key, data, category = 'vectors', options = {}) {
    if (this.isAvailable) {
      try {
        await window.electronAPI.cache.set(key, data, category, options);
        return true;
      } catch (error) {
        console.warn('⚠️ [SmartCache] 主进程缓存设置失败:', error);
      }
    }

    // 回退到本地缓存
    this.localCache.set(`${category}:${key}`, data);
    
    // 限制本地缓存大小
    if (this.localCache.size > 100) {
      const firstKey = this.localCache.keys().next().value;
      this.localCache.delete(firstKey);
    }
    
    return true;
  }

  /**
   * 删除缓存
   */
  async delete(key, category = 'vectors') {
    if (this.isAvailable) {
      try {
        return await window.electronAPI.cache.delete(key, category);
      } catch (error) {
        console.warn('⚠️ [SmartCache] 主进程缓存删除失败:', error);
      }
    }

    // 回退到本地缓存
    return this.localCache.delete(`${category}:${key}`);
  }

  /**
   * 获取缓存统计
   */
  async getStats() {
    if (this.isAvailable) {
      try {
        return await window.electronAPI.cache.getStats();
      } catch (error) {
        console.warn('⚠️ [SmartCache] 获取统计失败:', error);
      }
    }

    return {
      memory: {
        items: this.localCache.size,
        usage: '本地缓存模式'
      }
    };
  }
}

/**
 * 🚀 文档处理助手
 */
export class DocumentProcessorHelper {
  constructor() {
    this.isAvailable = true; // 前端处理器始终可用
    this.manager = documentProcessorManager;
    this.progressListeners = [];
  }

  /**
   * 处理文档
   */
  async processDocument(file, options = {}) {
    try {
      console.log('📄 [DocumentProcessor] 开始处理文档:', file.name || file);

      // 如果传入的是文件路径字符串，抛出错误
      if (typeof file === 'string') {
        throw new Error('前端处理器需要File对象，请使用文件上传');
      }

      return await this.manager.processDocument(file, options);
    } catch (error) {
      console.error('❌ [DocumentProcessor] 文档处理失败:', error);
      throw error;
    }
  }

  /**
   * 批量处理文档
   */
  async processDocuments(files, options = {}) {
    try {
      console.log('📄 [DocumentProcessor] 开始批量处理文档:', files.length, '个文件');

      // 添加进度监听
      const enhancedOptions = {
        ...options,
        onProgress: (progress) => {
          this.notifyProgress(progress);
          if (options.onProgress) {
            options.onProgress(progress);
          }
        }
      };

      return await this.manager.processDocuments(files, enhancedOptions);
    } catch (error) {
      console.error('❌ [DocumentProcessor] 批量处理失败:', error);
      throw error;
    }
  }

  /**
   * 添加进度监听器
   */
  onProgress(listener) {
    this.progressListeners.push(listener);
  }

  /**
   * 移除进度监听器
   */
  removeProgressListener() {
    this.progressListeners = [];
  }

  /**
   * 通知进度更新
   */
  notifyProgress(progress) {
    this.progressListeners.forEach(listener => {
      try {
        listener(progress);
      } catch (error) {
        console.error('❌ [DocumentProcessor] 进度监听器错误:', error);
      }
    });
  }

  /**
   * 获取支持的文件格式
   */
  async getSupportedFormats() {
    try {
      const formats = this.manager.getSupportedFormats();
      return {
        ...formats,
        note: '前端处理器：支持多种文档和图片格式的智能处理。'
      };
    } catch (error) {
      console.warn('⚠️ [DocumentProcessor] 获取支持格式失败:', error);
      return {
        images: [],
        documents: ['.txt'],
        all: ['.txt'],
        note: '处理器异常：回退到基础模式，仅支持文本文件。'
      };
    }
  }

  /**
   * 监听处理进度
   */
  onProgress(callback) {
    if (this.isAvailable && window.electronAPI.document.onProgress) {
      window.electronAPI.document.onProgress(callback);
    }
  }

  /**
   * 移除进度监听
   */
  removeProgressListener() {
    if (this.isAvailable && window.electronAPI.document.removeProgressListener) {
      window.electronAPI.document.removeProgressListener();
    }
  }
}

/**
 * 🚀 版本管理助手
 */
export class VersionManagerHelper {
  constructor() {
    this.isAvailable = isOptimizedFeaturesAvailable();
  }

  /**
   * 保存文档版本
   */
  async saveVersion(documentId, content, metadata = {}) {
    if (!this.isAvailable) {
      console.warn('⚠️ [VersionManager] 版本管理功能不可用');
      return null;
    }

    try {
      return await window.electronAPI.version.save(documentId, content, metadata);
    } catch (error) {
      console.error('❌ [VersionManager] 保存版本失败:', error);
      throw error;
    }
  }

  /**
   * 获取版本列表
   */
  async getVersions(documentId) {
    if (!this.isAvailable) {
      return [];
    }

    try {
      return await window.electronAPI.version.getList(documentId);
    } catch (error) {
      console.error('❌ [VersionManager] 获取版本列表失败:', error);
      return [];
    }
  }

  /**
   * 比较版本
   */
  async compareVersions(documentId, version1Id, version2Id) {
    if (!this.isAvailable) {
      throw new Error('版本比较功能不可用（需要主进程服务）');
    }

    try {
      return await window.electronAPI.version.compare(documentId, version1Id, version2Id);
    } catch (error) {
      console.error('❌ [VersionManager] 版本比较失败:', error);
      throw error;
    }
  }

  /**
   * 恢复版本
   */
  async restoreVersion(documentId, versionId) {
    if (!this.isAvailable) {
      throw new Error('版本恢复功能不可用（需要主进程服务）');
    }

    try {
      return await window.electronAPI.version.restore(documentId, versionId);
    } catch (error) {
      console.error('❌ [VersionManager] 版本恢复失败:', error);
      throw error;
    }
  }
}

/**
 * 🚀 性能监控工具
 */
export class PerformanceMonitor {
  constructor() {
    this.isAvailable = isOptimizedFeaturesAvailable();
    this.metrics = {
      searchTimes: [],
      cacheHits: 0,
      cacheMisses: 0
    };
  }

  /**
   * 记录搜索性能
   */
  recordSearchTime(time, resultCount, useMainProcess = false) {
    this.metrics.searchTimes.push({
      time,
      resultCount,
      useMainProcess,
      timestamp: Date.now()
    });

    // 保留最近100次记录
    if (this.metrics.searchTimes.length > 100) {
      this.metrics.searchTimes.shift();
    }

    console.log(`📊 [Performance] 搜索耗时: ${time}ms, 结果: ${resultCount}, 主进程: ${useMainProcess}`);
  }

  /**
   * 记录缓存命中
   */
  recordCacheHit() {
    this.metrics.cacheHits++;
  }

  /**
   * 记录缓存未命中
   */
  recordCacheMiss() {
    this.metrics.cacheMisses++;
  }

  /**
   * 获取性能统计
   */
  async getStats() {
    const localStats = {
      avgSearchTime: this.metrics.searchTimes.length > 0 
        ? this.metrics.searchTimes.reduce((sum, m) => sum + m.time, 0) / this.metrics.searchTimes.length 
        : 0,
      totalSearches: this.metrics.searchTimes.length,
      cacheHitRate: this.metrics.cacheHits + this.metrics.cacheMisses > 0 
        ? (this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) * 100).toFixed(2) + '%'
        : '0%',
      mainProcessUsage: this.metrics.searchTimes.filter(m => m.useMainProcess).length
    };

    if (this.isAvailable) {
      try {
        const serviceStats = await window.electronAPI.service.getStatus();
        return {
          local: localStats,
          service: serviceStats
        };
      } catch (error) {
        console.warn('⚠️ [Performance] 获取服务统计失败:', error);
      }
    }

    return { local: localStats };
  }

  /**
   * 重置统计
   */
  reset() {
    this.metrics = {
      searchTimes: [],
      cacheHits: 0,
      cacheMisses: 0
    };
  }
}

// 🚀 创建全局实例
export const optimizedVectorSearch = new OptimizedVectorSearch();
export const smartCache = new SmartCacheManager();
export const documentProcessor = new DocumentProcessorHelper();
export const versionManager = new VersionManagerHelper();
export const performanceMonitor = new PerformanceMonitor();

// 🚀 便捷函数
export function getOptimizationStatus() {
  return {
    available: isOptimizedFeaturesAvailable(),
    features: {
      vectorSearch: !!window.electronAPI?.vectorSearch,
      cache: !!window.electronAPI?.cache,
      documentProcessor: !!window.electronAPI?.document,
      versionManager: !!window.electronAPI?.version,
      knowledgeGraph: !!window.electronAPI?.knowledgeGraph
    }
  };
}

console.log('🚀 [OptimizedFeatures] 优化功能模块已加载', getOptimizationStatus());
