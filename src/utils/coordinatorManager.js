import { APIManager } from './apiManager';

/**
 * 中控模型调度管理器
 */
export class CoordinatorManager {
  constructor() {
    this.apiManager = new APIManager();
    // 跟踪模型发言统计
    this.modelStats = new Map(); // { modelId: { count: number, lastSpoke: timestamp } }
    // 会话级别的模型发言统计
    this.sessionStats = new Map(); // { sessionId: Map<modelId, { count: number, lastSpoke: timestamp }> }
    // 当前讨论轮次
    this.currentRound = 0;
    // 发言顺序队列
    this.speakingQueue = [];
  }

  /**
   * 生成调度提示词
   */
  generateCoordinatorPrompt(invitedModels, conversationContext, lastMessages) {
    const modelList = invitedModels.map(model => 
      `- ${model.name}: ${this.getModelDescription(model)}`
    ).join('\n');

    const recentContext = lastMessages.slice(-6).map(msg => 
      `${msg.sender}: ${msg.content.substring(0, 200)}${msg.content.length > 200 ? '...' : ''}`
    ).join('\n');

    return `你是一个AI协作调度员，负责分析对话上下文并选择最适合回答的模型。

当前可用模型：
${modelList}

最近对话内容：
${recentContext}

请分析当前对话的主题、复杂度和需求，然后选择最适合回答的模型。考虑因素包括：
1. 模型的专长领域和能力
2. 问题的类型和复杂度
3. 是否需要特殊功能（如thinking、vision）
4. 对话的连贯性和多样性

请按以下JSON格式回复：
{
  "selectedModel": "模型名称",
  "reasoning": "选择理由（简短说明为什么选择这个模型）",
  "confidence": 0.8
}

只返回JSON，不要其他内容。`;
  }

  /**
   * 获取模型描述
   */
  getModelDescription(model) {
    const features = [];
    if (model.supportThinking) features.push('支持思考链');
    if (model.supportVision) features.push('支持视觉分析');
    if (model.supportWebSearch) features.push('支持联网搜索');

    const featureText = features.length > 0 ? `（${features.join('、')}）` : '';
    return `通用AI模型${featureText}`;
  }

  /**
   * 检测消息是否包含Vision内容（图片）
   */
  hasVisionContent(content) {
    console.log('🔍 [CoordinatorManager] hasVisionContent 检测开始');
    console.log('🔍 [CoordinatorManager] content类型:', typeof content);
    console.log('🔍 [CoordinatorManager] content是否为数组:', Array.isArray(content));
    console.log('🔍 [CoordinatorManager] content内容:', content);

    if (!Array.isArray(content)) {
      console.log('🔍 [CoordinatorManager] content不是数组，返回false');
      return false;
    }

    const hasVision = content.some(item => {
      console.log('🔍 [CoordinatorManager] 检查item:', item);
      console.log('🔍 [CoordinatorManager] item类型:', typeof item);

      if (!item || typeof item !== 'object') {
        console.log('🔍 [CoordinatorManager] item不是对象');
        return false;
      }

      console.log('🔍 [CoordinatorManager] item.type:', item.type);

      if (item.type !== 'image_url') {
        console.log('🔍 [CoordinatorManager] item.type不是image_url');
        return false;
      }

      console.log('🔍 [CoordinatorManager] item.image_url:', item.image_url);

      if (!item.image_url || typeof item.image_url.url !== 'string') {
        console.log('🔍 [CoordinatorManager] image_url.url不是字符串');
        return false;
      }

      console.log('🔍 [CoordinatorManager] image_url.url前缀:', item.image_url.url.substring(0, 20));

      const isDataImage = item.image_url.url.startsWith('data:image/');
      console.log('🔍 [CoordinatorManager] 是否为data:image/格式:', isDataImage);

      return isDataImage;
    });

    console.log('🔍 [CoordinatorManager] hasVisionContent 最终结果:', hasVision);
    return hasVision;
  }

  /**
   * 检测消息中是否包含图片内容
   */
  detectVisionContentInMessages(messages) {
    console.log('🔍 [CoordinatorManager] detectVisionContentInMessages 开始');
    console.log('🔍 [CoordinatorManager] messages数量:', messages ? messages.length : 0);
    console.log('🔍 [CoordinatorManager] messages内容:', messages);

    if (!messages || messages.length === 0) {
      console.log('🔍 [CoordinatorManager] 没有消息，返回false');
      return false;
    }

    // 检查最后一条用户消息是否包含图片
    const lastUserMessage = this.getLastUserMessage(messages);
    console.log('🔍 [CoordinatorManager] 最后一条用户消息:', lastUserMessage);

    if (!lastUserMessage) {
      console.log('🔍 [CoordinatorManager] 没有找到用户消息，返回false');
      return false;
    }

    console.log('🔍 [CoordinatorManager] 最后用户消息内容:', lastUserMessage.content);
    console.log('🔍 [CoordinatorManager] 最后用户消息files:', lastUserMessage.files);

    const hasVision = this.hasVisionContent(lastUserMessage.content);
    console.log('🔍 [CoordinatorManager] detectVisionContentInMessages 最终结果:', hasVision);

    return hasVision;
  }

  /**
   * 执行模型调度
   */
  async scheduleNextModel(coordinatorModel, invitedModels, conversationContext, options = {}) {
    console.log('🎯 [CoordinatorManager] 开始智能调度');
    console.log('🎯 [CoordinatorManager] 中控模型:', coordinatorModel.name);
    console.log('🎯 [CoordinatorManager] 所有邀请模型:', invitedModels.map(m => m.name));

    // 🔥 修复：检查是否已被中断
    const { abortController } = options;
    if (abortController && abortController.signal.aborted) {
      console.log('🛑 [CoordinatorManager] 检测到中断信号，取消调度');
      return null;
    }

    try {
      const messages = conversationContext.messages || [];
      const lastUserMessage = this.getLastUserMessage(messages);
      const allowCoordinatorToSpeak = options.allowCoordinatorToSpeak !== false;

      // 1. 检查用户是否指定了特定模型
      const userSpecifiedModel = this.detectUserSpecifiedModel(lastUserMessage, invitedModels);
      if (userSpecifiedModel) {
        console.log('👤 [CoordinatorManager] 用户指定模型:', userSpecifiedModel.name);
        this.recordModelSpeaking(userSpecifiedModel.id);
        return {
          success: true,
          selectedModel: userSpecifiedModel,
          reasoning: `用户明确指定了 ${userSpecifiedModel.name} 回答`,
          confidence: 1.0,
          isUserSpecified: true
        };
      }

      // 2. 更新模型统计信息
      this.updateModelStats(invitedModels);

      // 3. 🔥 新增：检测是否包含Vision内容
      console.log('🔍 [CoordinatorManager] ===== Vision检测开始 =====');
      console.log('🔍 [CoordinatorManager] 传入的messages:', messages);
      console.log('🔍 [CoordinatorManager] messages数量:', messages.length);

      const hasVisionContent = this.detectVisionContentInMessages(messages);
      console.log('🖼️ [CoordinatorManager] 消息包含图片:', hasVisionContent ? '是' : '否');
      console.log('🔍 [CoordinatorManager] ===== Vision检测结束 =====');

      // 4. 确定候选模型（排除中控模型，除非明确允许）
      let candidateModels = allowCoordinatorToSpeak
        ? invitedModels
        : invitedModels.filter(model =>
            model.id !== coordinatorModel.id && model.name !== coordinatorModel.name
          );

      // 5. 🔥 修复：不在这里过滤模型，只记录Vision检测结果用于调度决策
      if (hasVisionContent) {
        console.log('🖼️ [CoordinatorManager] 消息包含图片，调度时会优先考虑Vision支持模型');
        console.log('🖼️ [CoordinatorManager] 候选模型Vision支持情况:', candidateModels.map(m => `${m.name}(Vision:${m.supportVision ? '✓' : '✗'})`));
      } else {
        console.log('🔍 [CoordinatorManager] 消息不包含图片，正常调度');
      }

      // 6. 🔥 新增：智能讨论完成度分析
      const discussionRound = options.discussionRound || 1;
      const maxRounds = options.maxRounds || 20;
      const isCollaborativeDiscussion = options.isCollaborativeDiscussion;
      const enableIntelligentEnd = options.enableIntelligentEnd !== false; // 默认启用
      const discussionQualityThreshold = options.discussionQualityThreshold || 0.75;

      // 🔥 新增：联网搜索状态
      const enableWebSearch = options.enableWebSearch || false;
      const webSearchSupported = options.webSearchSupported || false;

      // 🔥 修复：在这里定义taskType，避免作用域问题
      let taskType = '复杂讨论';
      if (lastUserMessage) {
        const userContent = lastUserMessage.content.toLowerCase();

        // 检测报数/计数任务
        if (userContent.includes('报数') || userContent.includes('数数') ||
            userContent.includes('1人报1') || userContent.includes('一人报一') ||
            /\d+.*\d+.*\d+/.test(userContent)) {
          taskType = '报数任务';
        }
        // 检测简单问候
        else if (userContent.includes('你好') || userContent.includes('hello') ||
                 userContent.includes('hi') || userContent.includes('问候')) {
          taskType = '简单问候';
        }
        // 🔥 修复：检测单一问答（包括数学计算）
        else if (userContent.includes('什么是') || userContent.includes('如何') ||
                 userContent.includes('为什么') || userContent.includes('?') || userContent.includes('？') ||
                 /\d+[\+\-\*\/]\d+/.test(userContent) || // 数学计算
                 userContent.includes('等于') || userContent.includes('=') ||
                 userContent.includes('几') && userContent.length < 20) { // 简短的"几"字问题
          taskType = '单一问答';
        }
      }

      let completenessAnalysis = null;
      // 🔥 修复：简单问题在第1轮后就可以检测，复杂问题在第2轮后检测
      const shouldAnalyze = isCollaborativeDiscussion && enableIntelligentEnd && (
        (discussionRound >= 1 && (taskType === '单一问答' || taskType === '简单问候' || taskType === '报数任务')) ||
        discussionRound > 2
      );

      if (shouldAnalyze) {
        completenessAnalysis = this.analyzeDiscussionCompleteness(
          messages,
          lastUserMessage,
          discussionRound,
          maxRounds
        );

        console.log('🧠 [CoordinatorManager] 智能完成度分析结果:', completenessAnalysis);
        console.log('🧠 [CoordinatorManager] 任务类型:', taskType);
        console.log('🧠 [CoordinatorManager] 质量阈值:', discussionQualityThreshold);

        // 🔥 修复：对于简单任务，降低质量阈值要求
        let effectiveThreshold = discussionQualityThreshold;
        if (taskType === '单一问答' || taskType === '简单问候' || taskType === '报数任务') {
          effectiveThreshold = Math.min(discussionQualityThreshold, 0.6); // 简单任务最高60%阈值
          console.log('🎯 [CoordinatorManager] 简单任务，调整阈值为:', effectiveThreshold);
        }

        if (completenessAnalysis.shouldEnd && completenessAnalysis.confidence >= effectiveThreshold) {
          console.log('✅ [CoordinatorManager] 智能分析建议结束讨论:', completenessAnalysis.reason);
          console.log('✅ [CoordinatorManager] 置信度:', completenessAnalysis.confidence, '有效阈值:', effectiveThreshold);
          return {
            success: true,
            shouldEndDiscussion: true,
            endReason: completenessAnalysis.reason,
            confidence: completenessAnalysis.confidence,
            analysisScores: completenessAnalysis.scores,
            isIntelligentEnd: true,
            qualityThreshold: effectiveThreshold,
            taskType: taskType
          };
        }
      } else if (!enableIntelligentEnd) {
        console.log('🔍 [CoordinatorManager] 智能结束检测已禁用');
      }

      console.log('🎯 [CoordinatorManager] 最终候选模型:', candidateModels.map(m => m.name));
      console.log('🎯 [CoordinatorManager] 中控模型可参与:', allowCoordinatorToSpeak);

      if (candidateModels.length === 0) {
        console.warn('⚠️ [CoordinatorManager] 没有可调度的候选模型');
        throw new Error('没有可调度的候选模型');
      }

      // 4. 使用智能调度模式
      return this.executeIntelligentScheduling(coordinatorModel, candidateModels, messages, options);

    } catch (error) {
      console.error('❌ [CoordinatorManager] 模型调度失败:', error);

      // 调度失败时的公平降级策略
      const allowCoordinatorToSpeak = options.allowCoordinatorToSpeak !== false;
      const candidateModels = allowCoordinatorToSpeak
        ? invitedModels
        : invitedModels.filter(model =>
            model.id !== coordinatorModel.id && model.name !== coordinatorModel.name
          );

      const fallbackModel = this.selectFairFallbackModel(candidateModels);

      if (fallbackModel) {
        this.recordModelSpeaking(fallbackModel.id);
      }

      console.log('🔄 [CoordinatorManager] 使用公平降级策略，选择模型:', fallbackModel?.name);

      return {
        success: false,
        error: error.message,
        selectedModel: fallbackModel,
        reasoning: '调度失败，使用公平轮流选择',
        confidence: 0.5,
        isFallback: true
      };
    }
  }



  /**
   * 解析调度响应
   */
  parseSchedulingResponse(responseContent, invitedModels) {
    try {
      // 尝试提取JSON
      const jsonMatch = responseContent.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('未找到有效的JSON响应');
      }

      const result = JSON.parse(jsonMatch[0]);
      
      // 验证选择的模型是否在邀请列表中
      const selectedModel = invitedModels.find(
        model => model.name === result.selectedModel || model.id === result.selectedModel
      );

      if (!selectedModel) {
        throw new Error(`选择的模型 "${result.selectedModel}" 不在邀请列表中`);
      }

      return {
        selectedModel,
        reasoning: result.reasoning || '未提供理由',
        confidence: Math.max(0, Math.min(1, result.confidence || 0.5))
      };

    } catch (error) {
      console.warn('解析调度响应失败:', error.message);
      
      // 解析失败时使用降级策略
      return {
        selectedModel: this.selectFallbackModel(invitedModels),
        reasoning: '解析失败，使用随机选择',
        confidence: 0.3
      };
    }
  }

  /**
   * 降级模型选择策略
   */
  selectFallbackModel(invitedModels, conversationContext = null) {
    if (!invitedModels || invitedModels.length === 0) {
      return null;
    }

    // 如果只有一个模型，直接返回
    if (invitedModels.length === 1) {
      return invitedModels[0];
    }

    // 简单的轮询策略：避免连续选择同一个模型
    if (conversationContext && conversationContext.messages) {
      const lastMessage = conversationContext.messages
        .slice()
        .reverse()
        .find(msg => msg.sender !== 'user' && msg.sender !== 'system');

      if (lastMessage) {
        const lastSender = lastMessage.sender;
        const otherModels = invitedModels.filter(model => 
          model.name !== lastSender && model.id !== lastSender
        );
        
        if (otherModels.length > 0) {
          return otherModels[Math.floor(Math.random() * otherModels.length)];
        }
      }
    }

    // 随机选择
    return invitedModels[Math.floor(Math.random() * invitedModels.length)];
  }

  /**
   * 生成调度决策的可视化信息
   */
  formatSchedulingDecision(schedulingResult) {
    const { selectedModel, reasoning, confidence, thinking, isFallback } = schedulingResult;
    
    let decisionText = `🎯 调度决策：选择 ${selectedModel?.name || '未知模型'}`;
    
    if (confidence !== undefined) {
      const confidencePercent = Math.round(confidence * 100);
      decisionText += ` (置信度: ${confidencePercent}%)`;
    }
    
    if (reasoning) {
      decisionText += `\n💭 选择理由：${reasoning}`;
    }
    
    if (isFallback) {
      decisionText += '\n⚠️ 注意：这是降级选择结果';
    }
    
    return {
      text: decisionText,
      thinking: thinking,
      confidence: confidence,
      isFallback: isFallback || false
    };
  }

  /**
   * 验证中控模型是否可用
   */
  async validateCoordinator(coordinatorModel) {
    try {
      const testResult = await this.apiManager.testModelConnection(coordinatorModel);
      return testResult.success;
    } catch (error) {
      console.error('中控模型验证失败:', error);
      return false;
    }
  }

  /**
   * 获取调度统计信息
   */
  getSchedulingStats(conversationHistory) {
    const stats = {
      totalScheduling: 0,
      successfulScheduling: 0,
      fallbackCount: 0,
      modelUsage: new Map()
    };

    conversationHistory.forEach(msg => {
      if (msg.schedulingInfo) {
        stats.totalScheduling++;
        
        if (!msg.schedulingInfo.isFallback) {
          stats.successfulScheduling++;
        } else {
          stats.fallbackCount++;
        }
        
        const modelName = msg.sender;
        stats.modelUsage.set(modelName, (stats.modelUsage.get(modelName) || 0) + 1);
      }
    });

    return {
      ...stats,
      successRate: stats.totalScheduling > 0 ? stats.successfulScheduling / stats.totalScheduling : 0,
      modelUsage: Object.fromEntries(stats.modelUsage)
    };
  }

  /**
   * 获取最后一条用户消息
   */
  getLastUserMessage(messages) {
    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i].sender === 'user') {
        return messages[i];
      }
    }
    return null;
  }

  /**
   * 检测用户是否指定了特定模型
   */
  detectUserSpecifiedModel(userMessage, invitedModels) {
    if (!userMessage || !userMessage.content) return null;

    const content = userMessage.content.toLowerCase();
    console.log('👤 [CoordinatorManager] 检测用户指定:', content);

    // 检测各种指定模式
    for (const model of invitedModels) {
      const modelName = model.name.toLowerCase();

      // 模式1: "模型A，你怎么看？"
      if (content.includes(`${modelName}，`) || content.includes(`${modelName},`)) {
        console.log('👤 [CoordinatorManager] 检测到指定模式1:', model.name);
        return model;
      }

      // 模式2: "@模型B 请回答"
      if (content.includes(`@${modelName}`) || content.includes(`@ ${modelName}`)) {
        console.log('👤 [CoordinatorManager] 检测到指定模式2:', model.name);
        return model;
      }

      // 模式3: "让模型C来分析"
      if (content.includes(`让${modelName}`) || content.includes(`请${modelName}`)) {
        console.log('👤 [CoordinatorManager] 检测到指定模式3:', model.name);
        return model;
      }

      // 模式4: "模型A你觉得"
      if (content.includes(`${modelName}你`) || content.includes(`${modelName} 你`)) {
        console.log('👤 [CoordinatorManager] 检测到指定模式4:', model.name);
        return model;
      }
    }

    return null;
  }

  /**
   * 更新模型统计信息
   */
  updateModelStats(invitedModels) {
    invitedModels.forEach(model => {
      if (!this.modelStats.has(model.id)) {
        this.modelStats.set(model.id, {
          count: 0,
          lastSpoke: 0,
          failures: 0,
          name: model.name
        });
      }
    });
  }



  /**
   * 记录模型发言
   */
  recordModelSpeaking(modelId) {
    const stats = this.modelStats.get(modelId);
    if (stats) {
      stats.count++;
      stats.lastSpoke = Date.now();
      this.modelStats.set(modelId, stats);
    }
  }

  /**
   * 记录模型在特定会话中的发言
   */
  recordSessionModelSpeaking(sessionId, modelId) {
    if (!this.sessionStats.has(sessionId)) {
      this.sessionStats.set(sessionId, new Map());
    }

    const sessionModelStats = this.sessionStats.get(sessionId);
    const stats = sessionModelStats.get(modelId) || { count: 0, lastSpoke: 0 };
    stats.count++;
    stats.lastSpoke = Date.now();
    sessionModelStats.set(modelId, stats);

    console.log(`📊 [CoordinatorManager] 记录会话模型发言: ${sessionId}/${modelId}, 会话内: ${stats.count}次`);
  }

  /**
   * 获取会话内的模型统计
   */
  getSessionModelStats(sessionId) {
    return this.sessionStats.get(sessionId) || new Map();
  }

  /**
   * 重置会话统计
   */
  resetSessionStats(sessionId) {
    this.sessionStats.delete(sessionId);
    console.log(`🔄 [CoordinatorManager] 重置会话统计: ${sessionId}`);
  }

  /**
   * 记录模型在特定会话中的发言
   */
  recordSessionModelSpeaking(sessionId, modelId) {
    if (!this.sessionStats.has(sessionId)) {
      this.sessionStats.set(sessionId, new Map());
    }

    const sessionModelStats = this.sessionStats.get(sessionId);
    const stats = sessionModelStats.get(modelId) || { count: 0, lastSpoke: 0 };
    stats.count++;
    stats.lastSpoke = Date.now();
    sessionModelStats.set(modelId, stats);

    console.log(`📊 [CoordinatorManager] 记录会话模型发言: ${sessionId}/${modelId}, 会话内: ${stats.count}次`);
  }

  /**
   * 获取会话内的模型统计
   */
  getSessionModelStats(sessionId) {
    return this.sessionStats.get(sessionId) || new Map();
  }

  /**
   * 重置会话统计
   */
  resetSessionStats(sessionId) {
    this.sessionStats.delete(sessionId);
    console.log(`🔄 [CoordinatorManager] 重置会话统计: ${sessionId}`);
  }



  /**
   * 执行智能调度
   */
  async executeIntelligentScheduling(coordinatorModel, candidateModels, messages, options) {
    console.log('🧠 [CoordinatorManager] 执行智能调度');

    // 🔥 修复：提取abortController
    const { abortController } = options;

    try {
      // 生成智能调度提示词
      const prompt = this.generateIntelligentCoordinatorPrompt(
        coordinatorModel,
        candidateModels,
        messages,
        this.modelStats,
        options
      );

      // 构建调度请求消息
      const coordinatorMessages = [
        {
          sender: 'user',
          content: prompt,
          timestamp: new Date().toISOString()
        }
      ];

      console.log('🎯 [CoordinatorManager] 调用中控模型进行调度决策...');

      // 调用中控模型进行调度决策
      const response = await this.apiManager.sendChatRequest(
        coordinatorModel,
        coordinatorMessages,
        {
          maxTokens: 800,
          temperature: 0.3,
          enableThinking: options.showThinking || false,
          abortController: abortController // 🔥 修复：传递AbortController
        }
      );

      if (!response.success) {
        throw new Error(response.error);
      }

      console.log('🎯 [CoordinatorManager] 中控模型响应成功，解析调度结果...');

      // 🔥 增加重试机制：解析调度结果
      let schedulingResult = this.parseIntelligentSchedulingResponse(
        response.data.content,
        candidateModels
      );

      // 如果首次解析失败且是降级结果，尝试重试一次
      if (schedulingResult.schedulingType === 'fallback' && options.allowRetry !== false) {
        console.log('🔄 [CoordinatorManager] 首次解析失败，尝试重试...');

        try {
          // 生成更严格的重试提示词
          const retryPrompt = this.generateStrictRetryPrompt(candidateModels);

          const retryMessages = [
            {
              sender: 'user',
              content: retryPrompt,
              timestamp: new Date().toISOString()
            }
          ];

          const retryResponse = await this.apiManager.sendChatRequest(
            coordinatorModel,
            retryMessages,
            {
              maxTokens: 500,
              temperature: 0.1, // 更低的温度，更严格的输出
              enableThinking: false,
              abortController: abortController // 🔥 修复：重试时也传递AbortController
            }
          );

          if (retryResponse.success) {
            console.log('🔄 [CoordinatorManager] 重试响应:', retryResponse.data.content);
            const retryResult = this.parseIntelligentSchedulingResponse(
              retryResponse.data.content,
              candidateModels
            );

            // 如果重试成功，使用重试结果
            if (retryResult.schedulingType !== 'fallback') {
              console.log('✅ [CoordinatorManager] 重试解析成功');
              schedulingResult = retryResult;
            }
          }
        } catch (retryError) {
          console.warn('⚠️ [CoordinatorManager] 重试失败:', retryError.message);
        }
      }

      // 更新发言统计
      if (schedulingResult.selectedModel) {
        this.recordModelSpeaking(schedulingResult.selectedModel.id);
      }

      console.log('✅ [CoordinatorManager] 智能调度完成，选择模型:', schedulingResult.selectedModel?.name);
      console.log('✅ [CoordinatorManager] 调度理由:', schedulingResult.reasoning);

      // 🔥 新功能：生成智能互动提示词
      let interactionPrompt = '';
      if (schedulingResult.selectedModel) {
        // 只有选择了模型时才生成互动提示词
        interactionPrompt = await this.generateInteractionPrompt(
          coordinatorModel,
          schedulingResult.selectedModel,
          messages,
          schedulingResult.reasoning,
          options
        );
      } else {
        console.log('🔍 [CoordinatorManager] 未选择模型，跳过互动提示词生成');
      }

      return {
        success: true,
        selectedModel: schedulingResult.selectedModel,
        reasoning: schedulingResult.reasoning,
        confidence: schedulingResult.confidence,
        thinking: response.data.thinking,
        rawResponse: response.data.content,
        schedulingType: schedulingResult.schedulingType || 'intelligent',
        interactionPrompt: interactionPrompt // 添加互动提示词
      };

    } catch (error) {
      console.error('❌ [CoordinatorManager] 智能调度失败:', error);

      // 降级到公平选择
      const fallbackModel = this.selectFairFallbackModel(candidateModels);

      return {
        success: false,
        error: error.message,
        selectedModel: fallbackModel,
        reasoning: '智能调度失败，使用公平轮流选择',
        confidence: 0.5,
        isFallback: true
      };
    }
  }

  /**
   * 生成智能调度提示词
   */
  generateIntelligentCoordinatorPrompt(coordinatorModel, invitedModels, messages, modelStats, options = {}) {
    const {
      isCollaborativeDiscussion = false,
      discussionRound = 1,
      maxRounds = 20, // 🔥 增加默认轮次，支持更深入的对话
      sessionId = null
    } = options;

    // 使用会话级别的统计，如果没有则使用全局统计
    const sessionStats = sessionId ? this.getSessionModelStats(sessionId) : new Map();

    const modelList = invitedModels.map(model => {
      const sessionStat = sessionStats.get(model.id) || { count: 0, lastSpoke: 0 };
      const globalStat = modelStats.get(model.id) || { count: 0, lastSpoke: 0 };
      return `- ${model.name}: ${this.getModelDescription(model)} (本会话已发言${sessionStat.count}次)`;
    }).join('\n');

    // 获取完整对话历史，特别关注最近的对话
    const fullContext = messages.map(msg => {
      const content = msg.content.length > 300 ? msg.content.substring(0, 300) + '...' : msg.content;
      return `${msg.sender}: ${content}`;
    }).join('\n');

    // 🔥 智能任务分析：检测任务类型和完成度
    const userMessage = messages.find(msg => msg.sender === 'user');
    const aiResponses = messages.filter(msg => msg.sender !== 'user' && msg.sender !== 'system');

    // 🔥 新增：检测Vision内容
    const hasVisionContent = userMessage ? this.hasVisionContent(userMessage.content) : false;
    const visionCapableModels = invitedModels.filter(model => model.supportVision === true);

    // 任务类型检测
    let taskType = '复杂讨论';
    let completionAnalysis = '';

    if (userMessage) {
      const userContent = userMessage.content.toLowerCase();

      // 检测报数/计数任务
      if (userContent.includes('报数') || userContent.includes('数数') ||
          userContent.includes('1人报1') || userContent.includes('一人报一') ||
          /\d+.*\d+.*\d+/.test(userContent)) {
        taskType = '报数任务';
        completionAnalysis = `已有${aiResponses.length}个模型报数，总共${invitedModels.length}个模型`;
        if (aiResponses.length >= invitedModels.length) {
          completionAnalysis += ' - 【任务已完成，应该结束】';
        }
      }
      // 检测简单问候
      else if (userContent.includes('你好') || userContent.includes('hello') ||
               userContent.includes('hi') || userContent.includes('问候')) {
        taskType = '简单问候';
        completionAnalysis = aiResponses.length >= 1 ? '【问候已完成，应该结束】' : '需要回应问候';
      }
      // 🔥 修复：检测单一问答（包括数学计算）
      else if (userContent.includes('什么是') || userContent.includes('如何') ||
               userContent.includes('为什么') || userContent.includes('?') || userContent.includes('？') ||
               /\d+[\+\-\*\/]\d+/.test(userContent) || // 数学计算
               userContent.includes('等于') || userContent.includes('=') ||
               userContent.includes('几') && userContent.length < 20) { // 简短的"几"字问题
        taskType = '单一问答';
        completionAnalysis = aiResponses.length >= 1 ? '【问题已有回答，应该结束】' : '需要回答问题';
      }
    }

    // 检测内容重复性
    let repetitionAnalysis = '';
    if (aiResponses.length >= 2) {
      const lastTwo = aiResponses.slice(-2);
      const similarity = this.calculateContentSimilarity(lastTwo[0].content, lastTwo[1].content);
      if (similarity > 0.7) {
        repetitionAnalysis = ' - 【检测到内容重复，建议结束】';
      }
    }

    // 🔥 新增：智能完成度分析
    let intelligentAnalysis = '';
    if (isCollaborativeDiscussion && discussionRound > 2) {
      const completenessAnalysis = this.analyzeDiscussionCompleteness(
        messages,
        userMessage,
        discussionRound,
        maxRounds
      );

      intelligentAnalysis = `\n\n🧠 智能完成度分析：`;
      intelligentAnalysis += `\n- 内容重复度：${(completenessAnalysis.scores.repetition * 100).toFixed(1)}%`;
      intelligentAnalysis += `\n- 问题覆盖度：${(completenessAnalysis.scores.coverage * 100).toFixed(1)}%`;
      intelligentAnalysis += `\n- 共识达成度：${(completenessAnalysis.scores.consensus * 100).toFixed(1)}%`;
      intelligentAnalysis += `\n- 信息增量度：${(completenessAnalysis.scores.incremental * 100).toFixed(1)}%`;
      intelligentAnalysis += `\n- 整体完成度：${(completenessAnalysis.scores.overall * 100).toFixed(1)}%`;

      if (completenessAnalysis.shouldEnd) {
        intelligentAnalysis += `\n- 🎯 智能建议：${completenessAnalysis.reason}`;
        intelligentAnalysis += `\n- ⚠️ 建议结束讨论（置信度：${(completenessAnalysis.confidence * 100).toFixed(1)}%）`;
      }
    }

    // 构建智能分析上下文
    let taskAnalysis = `\n\n【智能任务分析】`;
    taskAnalysis += `\n- 任务类型：${taskType}`;
    taskAnalysis += `\n- 用户请求：${userMessage ? userMessage.content.substring(0, 100) : '无'}`;
    taskAnalysis += `\n- 完成度分析：${completionAnalysis}`;
    taskAnalysis += `\n- 参与情况：${aiResponses.length}/${invitedModels.length} 个模型已参与`;
    taskAnalysis += `\n- 当前轮次：第 ${discussionRound}/${maxRounds} 轮`;

    // 🔥 新增：Vision相关信息
    if (hasVisionContent) {
      taskAnalysis += `\n- 🖼️ 图片内容：用户消息包含图片，已自动过滤为支持Vision的模型`;
      taskAnalysis += `\n- 🖼️ Vision模型：${visionCapableModels.map(m => m.name).join('、')} (${visionCapableModels.length}个)`;
    }

    taskAnalysis += repetitionAnalysis;
    taskAnalysis += intelligentAnalysis;

    if (aiResponses.length > 0) {
      const lastResponse = aiResponses[aiResponses.length - 1];
      taskAnalysis += `\n- 最新回复：${lastResponse.sender} - ${lastResponse.content.substring(0, 60)}...`;
    }

    taskAnalysis += `\n\n【结束判断提示】：根据任务类型和完成度，严格判断是否应该结束讨论！`;

    // 计算发言公平性（使用会话级别的统计）
    const sessionSpeakingCounts = Array.from(sessionStats.values()).map(s => s.count);
    const maxCount = sessionSpeakingCounts.length > 0 ? Math.max(...sessionSpeakingCounts, 0) : 0;
    const minCount = sessionSpeakingCounts.length > 0 ? Math.min(...sessionSpeakingCounts, 0) : 0;
    const fairnessGap = maxCount - minCount;

    if (isCollaborativeDiscussion) {
      // 🔥 优化：更自然的AI协作讨论提示词
      return `你是一个AI协作调度员，正在主持一场高质量的AI模型智能对话。你的目标是创造一个自然、流畅、有价值的多AI协作体验。

🎯 当前讨论状态：
- 讨论轮次：第 ${discussionRound}/${maxRounds} 轮
- 发言公平性差距：${fairnessGap}次
- 参与模型数量：${invitedModels.length}个

🤖 参与对话的AI模型：
${modelList}

📝 完整对话历史：
${fullContext}${taskAnalysis}

🎭 高质量协作对话规则：
1. **创造自然对话流**：选择能够自然承接前面话题的模型，让对话像真实的团队讨论
2. **鼓励深度互动**：让模型直接回应彼此的观点，形成真正的"对话"而非独立回答
3. **智能角色分配**：根据话题和模型特长，动态分配角色（提问者、分析者、质疑者、总结者）
4. **保持讨论活力**：避免重复和无意义的客套话，追求有价值的内容交流
5. **适时结束讨论**：当话题充分探讨或达成共识时，智能判断结束时机

🧠 智能调度策略（优先级排序）：
- **内容相关性**：选择最适合当前话题的专业模型
- **对话连贯性**：让模型能够自然回应前面的观点和问题
- **角色互补性**：如果前面是技术分析，可以选择创意模型提供不同视角
- **互动深度**：鼓励模型之间的直接对话和辩论
- **专业匹配度**：根据讨论内容的专业领域选择最合适的模型
- **发言公平性**：在质量优先的前提下，适当考虑发言机会均衡
- 🖼️ **Vision优先**：如果涉及图片内容，优先选择支持Vision的模型
- 🌐 **联网优先**：如果问题需要实时信息或最新数据，优先选择支持联网搜索的模型

💬 期望的互动风格：
- 让模型直接称呼彼此，如"我同意Claude的观点，但想补充..."
- 鼓励友好的观点碰撞和建设性争论
- 支持模型表达个性化的观点和风格
- 创造轻松但专业的讨论氛围

【关键】智能结束判断标准（必须严格执行！）：

**立即结束的情况（shouldEndDiscussion: true）：**
1. **🔢 数学计算问题**：如"1+1=几"、"2*3等于多少"等，一旦有模型给出正确答案，立即结束
2. **📝 简单事实问答**：如"什么是AI"、"北京是哪个国家的首都"等，一旦有充分回答，立即结束
3. **👋 简单问候**：如"你好"、"hello"等，一旦有模型回应，立即结束
4. **🔢 计数/报数任务**：如果用户要求报数，且每个模型都已报过数字，立即结束
5. **✅ 任务完成**：如果用户的具体任务已经完成，立即结束
6. **🔄 内容重复**：如果最近2轮出现明显重复内容，立即结束
7. **💯 所有模型参与**：如果所有模型都已发言且没有新观点，立即结束

**🚨 特别注意**：对于明显的简单问题（如数学计算、基础事实查询），第一个模型回答后就应该结束，不要为了"公平性"而让其他模型重复回答！

**继续讨论的情况（shouldEndDiscussion: false）：**
1. **复杂讨论**：需要深入分析的话题
2. **观点分歧**：模型间存在不同观点需要辩论
3. **未完成任务**：用户的请求还没有得到完整回答

**判断优先级**：任务完成度 > 内容重复性 > 参与公平性

**特别注意**：
- 报数游戏：3个模型报完1、2、3后必须结束
- 简单问候：1-2轮回复后必须结束
- 单一问答：得到答案后必须结束
- 不要因为"公平性"而延长已完成的任务

【重要】你必须明确回答三个核心问题，并严格按照以下JSON格式回复：

🎯 **核心决策问题**：
1. **是否需要调度新模型**？（true=继续讨论，false=结束讨论）
2. **如果调度，选择哪个模型**？（从候选模型中选择）
3. **决策理由是什么**？（简明扼要说明原因）

📋 **JSON格式要求**：
{
  "shouldContinue": true,
  "selectedModel": "模型名称",
  "reasoning": "决策理由",
  "confidence": 0.8,
  "endReason": "如果结束讨论的原因"
}

🔧 **字段说明**：
- **shouldContinue**: true=调度新模型继续，false=结束讨论
- **selectedModel**: 如果shouldContinue=true，必须选择：${invitedModels.map(m => m.name).join('、')} 中的一个；如果shouldContinue=false，可以为null
- **reasoning**: 决策理由，不超过30字
- **confidence**: 决策置信度，0.1到1.0
- **endReason**: 如果shouldContinue=false，说明结束原因；否则为null

⚠️ **严格要求**：
1. 只返回JSON，不要代码块包装
2. 不要任何解释文字
3. 确保JSON语法正确
4. 对于简单问题（如1+1=几），第一个模型回答后shouldContinue必须为false

📝 **示例回复**：
继续讨论：{"shouldContinue": true, "selectedModel": "${invitedModels[0]?.name || 'gpt-4'}", "reasoning": "需要补充技术细节", "confidence": 0.8, "endReason": null}
结束讨论：{"shouldContinue": false, "selectedModel": null, "reasoning": "问题已充分回答", "confidence": 0.9, "endReason": "简单数学题已完整回答"}`;
    } else {
      // 单次回复模式的提示词
      return `你是一个AI协作调度员，负责公平且智能地选择下一个发言的模型。

重要规则：
1. 你作为中控模型，只负责调度决策，不参与实际回答
2. 必须从以下候选模型中选择一个来回答用户问题
3. 考虑发言公平性：当前发言差距为${fairnessGap}次，优先选择发言较少的模型
4. 考虑话题相关性和模型特长
5. 支持模型间的互动和辩论

当前可用模型：
${modelList}

对话历史：
${fullContext}

调度策略：
- 如果某个模型发言明显较少，优先考虑
- 如果是技术问题，优先考虑有相关能力的模型
- 如果需要不同观点，选择之前没有参与讨论的模型
- 支持模型对前面回答的评论和补充
- 🖼️ 如果用户消息包含图片，只能选择支持Vision功能的模型

【重要】必须严格按照以下JSON格式回复：

{
  "shouldContinue": true,
  "selectedModel": "模型名称",
  "reasoning": "选择理由",
  "confidence": 0.8,
  "endReason": null
}

🔧 **字段说明**：
- **shouldContinue**: 对于单次回复模式，通常为true
- **selectedModel**: 必须选择：${invitedModels.map(m => m.name).join('、')} 中的一个
- **reasoning**: 选择理由，不超过30字
- **confidence**: 决策置信度，0.1到1.0
- **endReason**: 单次回复模式通常为null

⚠️ **严格要求**：
1. 只返回JSON，不要代码块包装
2. 确保JSON语法正确

📝 **示例回复**：
{"shouldContinue": true, "selectedModel": "${invitedModels[0]?.name || 'gpt-4'}", "reasoning": "公平轮流", "confidence": 0.8, "endReason": null}`;
    }
  }

  /**
   * 计算内容相似度（简单的词汇重叠度）
   */
  calculateContentSimilarity(content1, content2) {
    if (!content1 || !content2) return 0;

    // 简单的词汇分割和去重
    const words1 = new Set(content1.toLowerCase().split(/\s+/).filter(w => w.length > 2));
    const words2 = new Set(content2.toLowerCase().split(/\s+/).filter(w => w.length > 2));

    // 计算交集
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);

    // 返回Jaccard相似度
    return union.size > 0 ? intersection.size / union.size : 0;
  }

  /**
   * 生成严格的重试提示词
   */
  generateStrictRetryPrompt(invitedModels) {
    const modelNames = invitedModels.map(m => m.name).join('、');

    return `你必须从以下模型中选择一个：${modelNames}

请严格按照以下JSON格式回复，不要添加任何其他内容：

{
  "selectedModel": "模型名称",
  "reasoning": "选择理由",
  "confidence": 0.8,
  "schedulingType": "fair_rotation"
}

重要：
1. selectedModel必须是：${modelNames} 中的一个
2. 只返回JSON，不要任何解释
3. 确保JSON格式正确`;
  }

  /**
   * 解析智能调度响应
   */
  parseIntelligentSchedulingResponse(responseContent, invitedModels) {
    try {
      console.log('🔍 [CoordinatorManager] 原始调度响应:', responseContent);
      console.log('🔍 [CoordinatorManager] 响应长度:', responseContent.length);
      console.log('🔍 [CoordinatorManager] 响应类型:', typeof responseContent);

      // 🔥 增强的响应预处理和清理
      let cleanedContent = responseContent.trim();

      console.log('🔧 [CoordinatorManager] 开始清理响应内容');

      // 移除常见的代码块包装
      cleanedContent = cleanedContent.replace(/^```json\s*/i, '');
      cleanedContent = cleanedContent.replace(/^```javascript\s*/i, '');
      cleanedContent = cleanedContent.replace(/^```\s*/i, '');
      cleanedContent = cleanedContent.replace(/\s*```$/i, '');
      cleanedContent = cleanedContent.replace(/^json\s*/i, '');

      // 移除可能的解释文字（更精确的匹配）
      cleanedContent = cleanedContent.replace(/^[^{]*?(?=\{)/, ''); // 移除JSON前的文字，保留{
      cleanedContent = cleanedContent.replace(/\}[^}]*$/, '}'); // 移除JSON后的文字，保留}

      // 移除多余的空白字符
      cleanedContent = cleanedContent.replace(/\s+/g, ' ').trim();

      console.log('🔧 [CoordinatorManager] 清理后的响应:', cleanedContent);

      // 尝试提取JSON
      const jsonMatch = cleanedContent.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        console.error('❌ [CoordinatorManager] 未找到有效的JSON响应');
        console.error('❌ [CoordinatorManager] 清理后的内容:', cleanedContent);
        throw new Error('未找到有效的JSON响应');
      }

      let jsonString = jsonMatch[0];
      console.log('🔧 [CoordinatorManager] 提取的JSON字符串:', jsonString);

      // 🔥 修复：增强JSON解析错误处理
      let result;
      try {
        result = JSON.parse(jsonString);
        console.log('✅ [CoordinatorManager] JSON解析成功:', result);
      } catch (parseError) {
        console.error('❌ [CoordinatorManager] JSON解析失败:', parseError.message);
        console.error('❌ [CoordinatorManager] 无法解析的JSON:', jsonString);
        throw new Error(`JSON解析失败: ${parseError.message}`);
      }

      // 🔥 修复：验证新JSON格式的结构完整性
      console.log('🔧 [CoordinatorManager] 解析得到的结果:', result);
      console.log('🔧 [CoordinatorManager] 结果字段检查:', {
        shouldContinue: result.shouldContinue,
        shouldContinueType: typeof result.shouldContinue,
        selectedModel: result.selectedModel,
        selectedModelType: typeof result.selectedModel,
        reasoning: result.reasoning,
        confidence: result.confidence,
        endReason: result.endReason
      });

      // 🔥 新增：验证shouldContinue字段
      if (typeof result.shouldContinue !== 'boolean') {
        console.warn('⚠️ [CoordinatorManager] shouldContinue字段缺失或类型错误，尝试兼容旧格式');
        console.warn('⚠️ [CoordinatorManager] shouldContinue值:', result.shouldContinue, '类型:', typeof result.shouldContinue);
        // 兼容旧格式：如果有shouldEndDiscussion字段，转换为shouldContinue
        if (typeof result.shouldEndDiscussion === 'boolean') {
          result.shouldContinue = !result.shouldEndDiscussion;
          console.log('🔄 [CoordinatorManager] 从shouldEndDiscussion转换:', result.shouldContinue);
        } else {
          result.shouldContinue = true; // 默认继续
          console.log('🔄 [CoordinatorManager] 使用默认值shouldContinue=true');
        }
      }

      // 🔥 修复：验证selectedModel字段（只在需要继续时验证）
      if (result.shouldContinue) {
        if (!result.selectedModel || result.selectedModel === null) {
          throw new Error('当shouldContinue=true时，selectedModel不能为空或null');
        }
        if (typeof result.selectedModel !== 'string') {
          throw new Error('当shouldContinue=true时，selectedModel必须是字符串');
        }
      } else {
        // 当shouldContinue=false时，selectedModel可以为null
        console.log('🔍 [CoordinatorManager] shouldContinue=false，selectedModel可以为null');
      }

      if (result.confidence !== undefined && (typeof result.confidence !== 'number' || result.confidence < 0 || result.confidence > 1)) {
        console.warn('⚠️ [CoordinatorManager] confidence值异常，使用默认值');
        result.confidence = 0.5;
      }

      if (result.shouldEndDiscussion !== undefined && typeof result.shouldEndDiscussion !== 'boolean') {
        console.warn('⚠️ [CoordinatorManager] shouldEndDiscussion值异常，使用默认值');
        result.shouldEndDiscussion = false;
      }

      // 🔥 修复：根据shouldContinue决定是否验证模型
      let selectedModel = null;
      if (result.shouldContinue) {
        // 只在需要继续时验证模型
        selectedModel = invitedModels.find(
          model => model.name === result.selectedModel || model.id === result.selectedModel
        );

        if (!selectedModel) {
          console.error('❌ [CoordinatorManager] 选择的模型不在候选列表中:', result.selectedModel);
          console.error('❌ [CoordinatorManager] 可用模型列表:', invitedModels.map(m => m.name));
          throw new Error(`选择的模型 "${result.selectedModel}" 不在候选列表中`);
        }
      }

      console.log('✅ [CoordinatorManager] 调度解析成功:', {
        shouldContinue: result.shouldContinue,
        selectedModel: selectedModel?.name || null,
        reasoning: result.reasoning,
        confidence: result.confidence,
        endReason: result.endReason
      });

      // 🔥 修复：返回新的结果格式
      return {
        selectedModel,
        reasoning: result.reasoning || '未提供理由',
        confidence: Math.max(0, Math.min(1, result.confidence || 0.5)),
        shouldEndDiscussion: !result.shouldContinue, // 转换为旧格式兼容
        shouldContinue: result.shouldContinue,
        endReason: result.endReason || '',
        schedulingType: result.schedulingType || 'intelligent',
        discussionDirection: result.discussionDirection || ''
      };

    } catch (error) {
      console.error('❌ [CoordinatorManager] 解析调度响应失败:', {
        error: error.message,
        originalContent: responseContent,
        invitedModels: invitedModels.map(m => m.name)
      });

      // 解析失败时使用公平降级策略
      const fallbackModel = this.selectFairFallbackModel(invitedModels);
      console.warn('⚠️ [CoordinatorManager] 使用降级策略，选择模型:', fallbackModel?.name);

      return {
        selectedModel: fallbackModel,
        reasoning: `解析失败（${error.message}），使用公平轮流选择`,
        confidence: 0.3,
        schedulingType: 'fallback'
      };
    }
  }

  /**
   * 生成智能互动提示词
   */
  async generateInteractionPrompt(coordinatorModel, selectedModel, messages, reasoning, options = {}) {
    console.log('🎭 [CoordinatorManager] 生成智能互动提示词');

    // 🔥 修复：检查selectedModel是否为null
    if (!selectedModel) {
      console.warn('⚠️ [CoordinatorManager] selectedModel为null，无法生成互动提示词');
      return '';
    }

    try {
      // 获取最近的对话历史
      const recentMessages = messages.slice(-5); // 最近5条消息
      const conversationSummary = recentMessages.map(msg =>
        `${msg.sender}: ${msg.content.substring(0, 150)}${msg.content.length > 150 ? '...' : ''}`
      ).join('\n');

      // 获取其他AI模型的最近回复
      const aiMessages = messages.filter(msg =>
        msg.sender !== 'user' && msg.sender !== 'system' && msg.sender !== selectedModel.name
      );
      const lastAIMessage = aiMessages[aiMessages.length - 1];

      // 🔥 优化：构建更自然的互动提示词生成请求
      const interactionPrompt = `你是一个AI对话引导专家，专门创造自然流畅的AI团队协作体验。

🎯 当前情况：
- 选中模型：${selectedModel.name}
- 选择理由：${reasoning}
- 讨论轮次：第${options.discussionRound || 1}轮

📝 最近对话历史：
${conversationSummary}

🎭 任务：为 ${selectedModel.name} 生成一个自然的互动引导，让它能够：

**如果是第一个发言者**：
- 自然地开始话题讨论，展现专业特长
- 为后续讨论奠定基础

**如果有前面的发言**：
- 直接回应前面模型的具体观点
- 可以表示赞同、补充、质疑或提出不同角度
- 像真实的团队成员一样自然对话
- 可以直接称呼其他模型的名字

🎨 对话风格要求：
- 自然、友好、专业但不拘束
- 鼓励真实的观点交流和友好争论
- 体现模型的个性和专业特长
- 避免客套话，直奔主题

📏 格式要求：
- 简洁明了（80字以内）
- 直接返回引导内容，无需额外说明
- 使用对话式语言，不要过于正式

💡 示例风格：
"我觉得GPT-4的分析很有道理，但从技术实现角度，我想补充几个关键点..."
"Claude提到的用户体验问题确实重要，让我从产品设计角度来看看..."

请直接返回引导内容：`;

      const interactionMessages = [
        {
          sender: 'user',
          content: interactionPrompt,
          timestamp: new Date().toISOString()
        }
      ];

      // 调用中控模型生成互动提示词
      const response = await this.apiManager.sendChatRequest(
        coordinatorModel,
        interactionMessages,
        {
          maxTokens: 500,
          temperature: 0.7,
          enableThinking: false
        }
      );

      if (response.success && response.data.content) {
        const generatedPrompt = response.data.content.trim();
        console.log('✅ [CoordinatorManager] 互动提示词生成成功:', generatedPrompt.substring(0, 100) + '...');
        return generatedPrompt;
      } else {
        console.warn('⚠️ [CoordinatorManager] 互动提示词生成失败，使用默认提示');
        return this.getDefaultInteractionPrompt(selectedModel, lastAIMessage);
      }

    } catch (error) {
      console.error('❌ [CoordinatorManager] 生成互动提示词出错:', error);
      return this.getDefaultInteractionPrompt(selectedModel, messages);
    }
  }

  /**
   * 🔥 优化：获取更自然的默认互动提示词
   */
  getDefaultInteractionPrompt(selectedModel, lastAIMessage) {
    // 🔥 修复：检查selectedModel是否为null
    if (!selectedModel) {
      console.warn('⚠️ [CoordinatorManager] selectedModel为null，返回默认提示词');
      return '请参与这次讨论，分享你的观点和见解。';
    }

    if (lastAIMessage) {
      // 根据模型特点生成个性化的互动提示
      const modelPersonality = this.getModelPersonality(selectedModel.name);
      return `${lastAIMessage.sender}刚才的观点很有意思！请从你${modelPersonality}的角度回应一下，可以赞同、补充或提出不同看法。`;
    } else {
      const modelPersonality = this.getModelPersonality(selectedModel.name);
      return `请从你${modelPersonality}的专业角度来分析这个问题，为大家的讨论开个好头！`;
    }
  }

  /**
   * 🔥 新增：获取模型个性化描述
   */
  getModelPersonality(modelName) {
    const personalities = {
      'gpt-4o': '全面而平衡',
      'gpt-4o-mini': '简洁而高效',
      'claude-3.5-sonnet': '深度思考和技术专长',
      'claude-3-haiku': '快速而创意',
      'claude-3-opus': '详细分析和推理',
      'o1-preview': '逻辑推理和问题解决',
      'o1-mini': '快速推理',
      'gemini-pro': '多角度分析',
      'default': '独特的专业'
    };

    return personalities[modelName] || personalities['default'];
  }

  /**
   * 🔥 新增：智能讨论完成度分析
   */
  analyzeDiscussionCompleteness(messages, userMessage, discussionRound, maxRounds) {
    console.log('🧠 [CoordinatorManager] 开始智能讨论完成度分析');

    const aiResponses = messages.filter(msg =>
      msg.sender !== 'user' && msg.sender !== 'system'
    );

    if (aiResponses.length < 2) {
      return {
        shouldEnd: false,
        reason: '讨论刚开始，需要更多回复',
        confidence: 0.1
      };
    }

    // 1. 内容重复度分析
    const repetitionScore = this.analyzeContentRepetition(aiResponses);

    // 2. 问题覆盖度分析
    const coverageScore = this.analyzeQuestionCoverage(userMessage, aiResponses);

    // 3. 共识达成度分析
    const consensusScore = this.analyzeConsensusLevel(aiResponses);

    // 4. 信息增量分析
    const incrementalScore = this.analyzeInformationIncrement(aiResponses);

    // 5. 轮次进度分析
    const progressScore = Math.min(discussionRound / maxRounds, 1.0);

    // 综合评分计算
    const completenessScore = (
      repetitionScore * 0.25 +      // 重复度权重25%
      coverageScore * 0.30 +        // 覆盖度权重30%
      consensusScore * 0.20 +       // 共识度权重20%
      incrementalScore * 0.15 +     // 增量度权重15%
      progressScore * 0.10          // 进度权重10%
    );

    console.log('🧠 [CoordinatorManager] 完成度分析结果:', {
      repetitionScore: repetitionScore.toFixed(2),
      coverageScore: coverageScore.toFixed(2),
      consensusScore: consensusScore.toFixed(2),
      incrementalScore: incrementalScore.toFixed(2),
      progressScore: progressScore.toFixed(2),
      completenessScore: completenessScore.toFixed(2)
    });

    // 判断是否应该结束
    const shouldEnd = completenessScore >= 0.75; // 75%阈值
    const confidence = Math.min(completenessScore, 0.95);

    let reason = '';
    if (shouldEnd) {
      if (repetitionScore > 0.8) {
        reason = '检测到明显的内容重复，讨论已充分展开';
      } else if (coverageScore > 0.9) {
        reason = '问题已得到全面回答和深入探讨';
      } else if (consensusScore > 0.8) {
        reason = 'AI模型们已达成共识，形成了一致的结论';
      } else {
        reason = '讨论已达到高质量完成状态';
      }
    }

    return {
      shouldEnd,
      reason,
      confidence,
      scores: {
        repetition: repetitionScore,
        coverage: coverageScore,
        consensus: consensusScore,
        incremental: incrementalScore,
        progress: progressScore,
        overall: completenessScore
      }
    };
  }

  /**
   * 🔥 新增：分析内容重复度
   */
  analyzeContentRepetition(aiResponses) {
    if (aiResponses.length < 3) return 0;

    const recentResponses = aiResponses.slice(-3); // 最近3条回复
    let totalSimilarity = 0;
    let comparisons = 0;

    for (let i = 0; i < recentResponses.length - 1; i++) {
      for (let j = i + 1; j < recentResponses.length; j++) {
        const similarity = this.calculateContentSimilarity(
          recentResponses[i].content,
          recentResponses[j].content
        );
        totalSimilarity += similarity;
        comparisons++;
      }
    }

    const avgSimilarity = comparisons > 0 ? totalSimilarity / comparisons : 0;
    console.log('🔄 [CoordinatorManager] 内容重复度分析:', avgSimilarity.toFixed(2));

    return avgSimilarity;
  }

  /**
   * 🔥 修复：分析问题覆盖度（对简单问题更敏感）
   */
  analyzeQuestionCoverage(userMessage, aiResponses) {
    if (!userMessage || aiResponses.length === 0) return 0;

    const userContent = userMessage.content.toLowerCase();
    const combinedAIContent = aiResponses.map(msg => msg.content.toLowerCase()).join(' ');

    // 🔥 新增：检测简单数学问题
    const isMathQuestion = /\d+[\+\-\*\/]\d+/.test(userContent) ||
                          userContent.includes('等于') ||
                          userContent.includes('=');

    if (isMathQuestion) {
      // 对于数学问题，检查是否包含数字答案
      const hasNumberAnswer = /\d+/.test(combinedAIContent);
      const hasCorrectAnswer = combinedAIContent.includes('2') && userContent.includes('1+1'); // 特殊处理1+1

      console.log('🔢 [CoordinatorManager] 数学问题覆盖度分析:', {
        hasNumber: hasNumberAnswer,
        hasCorrect: hasCorrectAnswer,
        score: hasNumberAnswer ? 0.9 : 0.1
      });

      return hasNumberAnswer ? 0.9 : 0.1; // 数学问题有数字答案就算高覆盖度
    }

    // 提取用户问题中的关键词
    const questionKeywords = this.extractKeywords(userContent);

    // 检查AI回复中覆盖了多少关键词
    let coveredKeywords = 0;
    questionKeywords.forEach(keyword => {
      if (combinedAIContent.includes(keyword)) {
        coveredKeywords++;
      }
    });

    const coverageRatio = questionKeywords.length > 0 ? coveredKeywords / questionKeywords.length : 0;

    // 🔥 修复：对于简单问题，降低内容长度要求
    const isSimpleQuestion = userContent.length < 20 || questionKeywords.length <= 3;
    const contentLengthThreshold = isSimpleQuestion ? 100 : 1000; // 简单问题只需100字符
    const contentRichness = Math.min(combinedAIContent.length / contentLengthThreshold, 1.0);

    // 🔥 修复：简单问题更重视覆盖率，复杂问题更重视内容丰富度
    const coverageWeight = isSimpleQuestion ? 0.8 : 0.7;
    const richnessWeight = isSimpleQuestion ? 0.2 : 0.3;
    const finalScore = Math.min(coverageRatio * coverageWeight + contentRichness * richnessWeight, 1.0);

    console.log('📊 [CoordinatorManager] 问题覆盖度分析:', {
      keywords: questionKeywords.length,
      covered: coveredKeywords,
      ratio: coverageRatio.toFixed(2),
      richness: contentRichness.toFixed(2),
      isSimple: isSimpleQuestion,
      final: finalScore.toFixed(2)
    });

    return finalScore;
  }

  /**
   * 🔥 新增：分析共识达成度
   */
  analyzeConsensusLevel(aiResponses) {
    if (aiResponses.length < 2) return 0;

    // 检查是否有明显的分歧表达
    const disagreementKeywords = ['但是', '然而', '不过', '相反', '不同意', '反对', '错误'];
    const agreementKeywords = ['同意', '赞同', '正确', '很好', '确实', '没错', '支持'];

    let disagreementCount = 0;
    let agreementCount = 0;

    aiResponses.forEach(response => {
      const content = response.content.toLowerCase();

      disagreementKeywords.forEach(keyword => {
        if (content.includes(keyword)) disagreementCount++;
      });

      agreementKeywords.forEach(keyword => {
        if (content.includes(keyword)) agreementCount++;
      });
    });

    // 计算共识度：更多同意，更少分歧 = 更高共识
    const totalSignals = disagreementCount + agreementCount;
    const consensusScore = totalSignals > 0 ? agreementCount / totalSignals : 0.5;

    console.log('🤝 [CoordinatorManager] 共识达成度分析:', {
      agreement: agreementCount,
      disagreement: disagreementCount,
      consensus: consensusScore.toFixed(2)
    });

    return consensusScore;
  }

  /**
   * 🔥 新增：分析信息增量
   */
  analyzeInformationIncrement(aiResponses) {
    if (aiResponses.length < 2) return 0;

    const recentResponses = aiResponses.slice(-2); // 最近2条回复
    if (recentResponses.length < 2) return 0;

    const lastResponse = recentResponses[1].content;
    const previousResponse = recentResponses[0].content;

    // 检查新信息的关键指标
    const newInfoKeywords = ['另外', '此外', '补充', '还有', '进一步', '深入', '具体'];
    const conclusionKeywords = ['总结', '综上', '最终', '结论', '总的来说', '因此'];

    let newInfoCount = 0;
    let conclusionCount = 0;

    newInfoKeywords.forEach(keyword => {
      if (lastResponse.includes(keyword)) newInfoCount++;
    });

    conclusionKeywords.forEach(keyword => {
      if (lastResponse.includes(keyword)) conclusionCount++;
    });

    // 如果最近的回复主要是总结性的，说明信息增量较低
    const isMainlyConclusion = conclusionCount > newInfoCount;
    const incrementScore = isMainlyConclusion ? 0.2 : Math.min(newInfoCount / 3, 1.0);

    console.log('📈 [CoordinatorManager] 信息增量分析:', {
      newInfo: newInfoCount,
      conclusion: conclusionCount,
      isConclusion: isMainlyConclusion,
      increment: incrementScore.toFixed(2)
    });

    return 1.0 - incrementScore; // 反转：增量越少，完成度越高
  }

  /**
   * 🔥 新增：提取关键词
   */
  extractKeywords(text) {
    // 简单的关键词提取：去除停用词，提取有意义的词汇
    const stopWords = ['的', '是', '在', '有', '和', '与', '或', '但', '如果', '因为', '所以', '这', '那', '什么', '怎么', '为什么', '如何'];
    const words = text.match(/[\u4e00-\u9fa5]+|[a-zA-Z]+/g) || [];

    return words
      .filter(word => word.length > 1 && !stopWords.includes(word))
      .slice(0, 10); // 最多10个关键词
  }

  /**
   * 🔥 新增：计算内容相似度
   */
  calculateContentSimilarity(content1, content2) {
    if (!content1 || !content2) return 0;

    // 提取两个内容的关键词
    const keywords1 = this.extractKeywords(content1.toLowerCase());
    const keywords2 = this.extractKeywords(content2.toLowerCase());

    if (keywords1.length === 0 || keywords2.length === 0) return 0;

    // 计算交集
    const intersection = keywords1.filter(word => keywords2.includes(word));
    const union = [...new Set([...keywords1, ...keywords2])];

    // Jaccard相似度
    const jaccardSimilarity = intersection.length / union.length;

    // 长度相似度
    const lengthSimilarity = 1 - Math.abs(content1.length - content2.length) / Math.max(content1.length, content2.length);

    // 综合相似度
    const overallSimilarity = jaccardSimilarity * 0.7 + lengthSimilarity * 0.3;

    return Math.min(overallSimilarity, 1.0);
  }

  /**
   * 公平降级模型选择策略
   */
  selectFairFallbackModel(invitedModels) {
    if (!invitedModels || invitedModels.length === 0) {
      return null;
    }

    // 如果只有一个模型，直接返回
    if (invitedModels.length === 1) {
      return invitedModels[0];
    }

    // 选择发言次数最少的模型
    let selectedModel = invitedModels[0];
    let minCount = this.modelStats.get(selectedModel.id)?.count || 0;

    for (const model of invitedModels) {
      const count = this.modelStats.get(model.id)?.count || 0;
      if (count < minCount) {
        minCount = count;
        selectedModel = model;
      }
    }

    console.log('🎲 [CoordinatorManager] 公平降级选择:', selectedModel.name, '发言次数:', minCount);
    return selectedModel;
  }



  /**
   * 计算公平性分数
   */
  calculateFairnessScore() {
    const counts = Array.from(this.modelStats.values()).map(s => s.count);
    if (counts.length === 0) return 1.0;

    const max = Math.max(...counts);
    const min = Math.min(...counts);
    const gap = max - min;

    // 公平性分数：差距越小分数越高
    return Math.max(0, 1 - gap / 10);
  }

  /**
   * 重置调度状态
   */
  resetSchedulingState() {
    this.modelStats.clear();
    this.currentRound = 0;
    this.speakingQueue = [];
    console.log('🔄 [CoordinatorManager] 调度状态已重置');
  }

  /**
   * 清理资源
   */
  cleanup() {
    if (this.apiManager) {
      this.apiManager.cleanup();
    }
    this.resetSchedulingState();
  }
}
