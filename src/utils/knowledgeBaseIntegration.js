/**
 * 知识库集成工具 - 负责在聊天过程中查询和使用知识库
 */
export class KnowledgeBaseIntegration {
  constructor(dataManager) {
    this.dataManager = dataManager;
  }

  /**
   * 根据用户消息查询相关知识库内容
   * @param {string} userMessage - 用户消息
   * @param {Array} knowledgeBaseIds - 关联的知识库ID列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 相关的知识库内容
   */
  async queryKnowledgeBases(userMessage, knowledgeBaseIds, options = {}) {
    if (!knowledgeBaseIds || knowledgeBaseIds.length === 0) {
      return [];
    }

    const {
      maxResults = 5,
      threshold = 0.7,
      includeContext = true
    } = options;

    try {
      console.log('🔍 [KnowledgeBase] 开始查询知识库:', knowledgeBaseIds);
      console.log('🔍 [KnowledgeBase] 查询内容:', userMessage.substring(0, 100));

      const results = [];

      // 遍历每个知识库进行查询
      for (const kbId of knowledgeBaseIds) {
        try {
          const kb = this.dataManager.getKnowledgeBase(kbId);
          if (!kb) {
            console.warn(`⚠️ [KnowledgeBase] 知识库不存在: ${kbId}`);
            continue;
          }

          console.log(`🔍 [KnowledgeBase] 查询知识库: ${kb.name}`);

          // 使用知识库管理器进行向量搜索
          const searchResults = await this.dataManager.knowledgeBaseManager.searchVectors(
            kbId,
            userMessage,
            {
              topK: maxResults,
              threshold,
              includeContext,
              searchLevel: 'all'
            }
          );

          if (searchResults && searchResults.length > 0) {
            console.log(`✅ [KnowledgeBase] 在 ${kb.name} 中找到 ${searchResults.length} 个相关结果`);
            
            // 为每个结果添加知识库信息
            const enrichedResults = searchResults.map(result => ({
              ...result,
              knowledgeBaseName: kb.name,
              knowledgeBaseId: kbId,
              embeddingModel: kb.embeddingModel
            }));

            results.push(...enrichedResults);
          } else {
            console.log(`ℹ️ [KnowledgeBase] 在 ${kb.name} 中未找到相关内容`);
          }
        } catch (error) {
          console.error(`❌ [KnowledgeBase] 查询知识库 ${kbId} 时出错:`, error);
        }
      }

      // 按相似度排序并限制结果数量
      results.sort((a, b) => (b.similarity || 0) - (a.similarity || 0));
      const topResults = results.slice(0, maxResults);

      console.log(`🎯 [KnowledgeBase] 总共找到 ${topResults.length} 个相关结果`);
      return topResults;

    } catch (error) {
      console.error('❌ [KnowledgeBase] 查询知识库时出错:', error);
      return [];
    }
  }

  /**
   * 将知识库查询结果格式化为AI模型可用的上下文
   * @param {Array} searchResults - 搜索结果
   * @param {string} userMessage - 用户消息
   * @returns {string} 格式化的上下文
   */
  formatKnowledgeContext(searchResults, userMessage) {
    if (!searchResults || searchResults.length === 0) {
      return '';
    }

    const contextParts = [
      '# 相关知识库内容',
      '',
      '以下是从知识库中检索到的相关信息，请结合这些信息回答用户的问题：',
      ''
    ];

    searchResults.forEach((result, index) => {
      const similarity = result.similarity ? `(相似度: ${(result.similarity * 100).toFixed(1)}%)` : '';
      
      contextParts.push(`## 参考资料 ${index + 1} ${similarity}`);
      contextParts.push(`**来源**: ${result.knowledgeBaseName}`);
      
      if (result.documentTitle) {
        contextParts.push(`**文档**: ${result.documentTitle}`);
      }
      
      if (result.chunkType) {
        contextParts.push(`**类型**: ${result.chunkType === 'parent' ? '父分块' : '子分块'}`);
      }
      
      contextParts.push('');
      contextParts.push('**内容**:');
      contextParts.push(result.text || result.content || '');
      contextParts.push('');
      
      // 如果有上下文信息，也包含进来
      if (result.context && result.context !== result.text) {
        contextParts.push('**上下文**:');
        contextParts.push(result.context);
        contextParts.push('');
      }
      
      contextParts.push('---');
      contextParts.push('');
    });

    contextParts.push('# 用户问题');
    contextParts.push('');
    contextParts.push(userMessage);
    contextParts.push('');
    contextParts.push('请基于上述知识库内容回答用户的问题。如果知识库中没有相关信息，请明确说明并提供你的一般性建议。');

    return contextParts.join('\n');
  }

  /**
   * 检查会话是否启用了知识库
   * @param {Object} session - 会话对象
   * @returns {boolean} 是否启用知识库
   */
  isKnowledgeBaseEnabled(session) {
    return session && session.knowledgeBases && session.knowledgeBases.length > 0;
  }

  /**
   * 获取会话关联的知识库列表
   * @param {Object} session - 会话对象
   * @returns {Array} 知识库ID列表
   */
  getSessionKnowledgeBases(session) {
    if (!this.isKnowledgeBaseEnabled(session)) {
      return [];
    }
    return session.knowledgeBases || [];
  }

  /**
   * 为AI模型消息添加知识库工具
   * @param {Array} messages - 消息列表
   * @param {Array} knowledgeBaseIds - 知识库ID列表
   * @returns {Object} 包含增强消息和引用信息的对象
   */
  async enhanceMessagesWithKnowledge(messages, knowledgeBaseIds) {
    if (!knowledgeBaseIds || knowledgeBaseIds.length === 0) {
      return {
        messages: messages,
        knowledgeReferences: []
      };
    }

    // 获取最后一条用户消息
    const lastUserMessage = messages.slice().reverse().find(msg => msg.sender === 'user');
    if (!lastUserMessage) {
      return {
        messages: messages,
        knowledgeReferences: []
      };
    }

    // 🚀 新增：预处理用户查询，提取关键信息
    const processedQuery = this.preprocessQuery(lastUserMessage.content);
    console.log('🔍 [KnowledgeBase] 原始查询:', lastUserMessage.content.substring(0, 100));
    console.log('🔍 [KnowledgeBase] 处理后查询:', processedQuery.substring(0, 100));

    // 🚀 Enhanced: Content-type aware multi-tier search strategy
    const queryType = this.detectQueryType(lastUserMessage.content);
    const searchConfig = this.getSearchConfigForQueryType(queryType);

    let searchResults = await this.queryKnowledgeBases(
      processedQuery,
      knowledgeBaseIds,
      {
        maxResults: searchConfig.maxResults,
        threshold: searchConfig.primaryThreshold,
        includeContext: true
      }
    );

    // If results are insufficient, try more relaxed search
    if (searchResults.length < searchConfig.minResults) {
      console.log(`🔍 [KnowledgeBase] 首次搜索结果较少 (${searchResults.length}/${searchConfig.minResults})，尝试更宽松的搜索...`);
      searchResults = await this.queryKnowledgeBases(
        processedQuery,
        knowledgeBaseIds,
        {
          maxResults: searchConfig.maxResults * 1.5,
          threshold: searchConfig.secondaryThreshold,
          includeContext: true
        }
      );
    }

    // If processed query still yields no results, try original query
    if (searchResults.length === 0 && processedQuery !== lastUserMessage.content) {
      console.log('🔍 [KnowledgeBase] 处理后查询无结果，尝试原始查询...');
      searchResults = await this.queryKnowledgeBases(
        lastUserMessage.content,
        knowledgeBaseIds,
        {
          maxResults: searchConfig.maxResults,
          threshold: searchConfig.fallbackThreshold,
          includeContext: true
        }
      );
    }

    if (searchResults.length === 0) {
      return {
        messages: messages,
        knowledgeReferences: []
      };
    }

    // 创建增强的消息列表
    const enhancedMessages = [...messages];

    // 将知识库上下文添加到最后一条用户消息中
    const knowledgeContext = this.formatKnowledgeContext(searchResults, lastUserMessage.content);

    // 替换最后一条用户消息
    const lastMessageIndex = enhancedMessages.length - 1;
    enhancedMessages[lastMessageIndex] = {
      ...lastUserMessage,
      content: knowledgeContext,
      originalContent: lastUserMessage.content, // 保存原始内容
      hasKnowledgeContext: true
    };

    // 🚀 新增：格式化知识库引用信息
    const knowledgeReferences = this.formatKnowledgeReferences(searchResults);

    console.log('🧠 [KnowledgeBase] 已为消息添加知识库上下文');
    return {
      messages: enhancedMessages,
      knowledgeReferences: knowledgeReferences
    };
  }

  /**
   * 🚀 新增：格式化知识库引用信息
   * @param {Array} searchResults - 搜索结果
   * @returns {Array} 格式化的引用信息
   */
  formatKnowledgeReferences(searchResults) {
    if (!searchResults || searchResults.length === 0) {
      return [];
    }

    return searchResults.map((result, index) => ({
      id: `ref_${index}_${Date.now()}`,
      knowledgeBaseName: result.knowledgeBaseName,
      knowledgeBaseId: result.knowledgeBaseId,
      documentTitle: result.documentTitle || '未知文档',
      documentId: result.documentId,
      chunkType: result.chunkType,
      chunkIndex: result.chunkIndex || 0,
      similarity: result.similarity,
      text: result.text,
      parentContext: result.parentContext,
      embeddingModel: result.embeddingModel,
      timestamp: new Date().toISOString()
    }));
  }

  /**
   * 🚀 Enhanced: Multi-content type query preprocessing
   * @param {string} query - 原始查询
   * @returns {string} 处理后的查询
   */
  preprocessQuery(query) {
    let processedQuery = query.trim();
    const queryType = this.detectQueryType(query);

    console.log(`🔍 [KnowledgeBase] 查询类型检测: ${queryType}`);

    // Apply content-specific preprocessing
    switch (queryType) {
      case 'sql':
        processedQuery = this.preprocessSQLQuery(processedQuery);
        break;
      case 'code':
        processedQuery = this.preprocessCodeQuery(processedQuery);
        break;
      case 'api':
        processedQuery = this.preprocessAPIQuery(processedQuery);
        break;
      case 'technical':
        processedQuery = this.preprocessTechnicalQuery(processedQuery);
        break;
      case 'business':
        processedQuery = this.preprocessBusinessQuery(processedQuery);
        break;
      case 'config':
        processedQuery = this.preprocessConfigQuery(processedQuery);
        break;
      default:
        processedQuery = this.preprocessGenericQuery(processedQuery);
    }

    // Standard normalization
    processedQuery = this.normalizeQuery(processedQuery);

    return processedQuery;
  }

  /**
   * 🚀 New: Detect query type based on content
   */
  detectQueryType(query) {
    const lowerQuery = query.toLowerCase();

    // SQL-related queries
    if (this.isSQLRelatedQuery(query)) return 'sql';

    // Code-related queries
    if (this.isCodeRelatedQuery(query)) return 'code';

    // API-related queries
    if (this.isAPIRelatedQuery(query)) return 'api';

    // Technical documentation queries
    if (this.isTechnicalQuery(query)) return 'technical';

    // Business process queries
    if (this.isBusinessQuery(query)) return 'business';

    // Configuration queries
    if (this.isConfigQuery(query)) return 'config';

    return 'generic';
  }

  /**
   * 🚀 Enhanced: SQL query preprocessing
   */
  preprocessSQLQuery(query) {
    const tableNames = this.extractTableNames(query);
    let enhanced = query;

    if (tableNames.length > 0) {
      enhanced += ` CREATE TABLE ${tableNames.join(' ')} 建表语句 表结构 数据库 schema`;
    }

    // Add SQL-specific keywords
    enhanced += ' SQL 数据库 字段 表 database table field column';

    return enhanced;
  }

  /**
   * 🚀 Enhanced: Code query preprocessing
   */
  preprocessCodeQuery(query) {
    const functionNames = this.extractFunctionNames(query);
    let enhanced = query;

    if (functionNames.length > 0) {
      enhanced += ` function ${functionNames.join(' ')} 函数 方法`;
    }

    // Add code-specific keywords
    enhanced += ' 代码 函数 方法 实现 算法 code function method implementation';

    return enhanced;
  }

  /**
   * 🚀 New: API query preprocessing
   */
  preprocessAPIQuery(query) {
    const endpoints = this.extractEndpoints(query);
    let enhanced = query;

    if (endpoints.length > 0) {
      enhanced += ` ${endpoints.join(' ')} endpoint API`;
    }

    enhanced += ' API 接口 端点 请求 响应 endpoint request response';

    return enhanced;
  }

  /**
   * 🚀 New: Technical documentation query preprocessing
   */
  preprocessTechnicalQuery(query) {
    let enhanced = query;
    enhanced += ' 技术 文档 架构 系统 组件 technical documentation architecture system component';

    return enhanced;
  }

  /**
   * 🚀 New: Business query preprocessing
   */
  preprocessBusinessQuery(query) {
    let enhanced = query;
    enhanced += ' 业务 流程 需求 规则 business process requirement rule workflow';

    return enhanced;
  }

  /**
   * 🚀 New: Configuration query preprocessing
   */
  preprocessConfigQuery(query) {
    let enhanced = query;
    enhanced += ' 配置 设置 参数 环境 config configuration setting parameter environment';

    return enhanced;
  }

  /**
   * 🚀 New: Generic query preprocessing
   */
  preprocessGenericQuery(query) {
    // Add general search enhancement
    return query + ' 文档 内容 信息 document content information';
  }

  /**
   * 🚀 New: Normalize query format
   */
  normalizeQuery(query) {
    return query
      .replace(/\s+/g, ' ') // Multiple spaces to single space
      .replace(/[，。！？；：]/g, ' ') // Chinese punctuation to space
      .replace(/[,\.!?;:]/g, ' ') // English punctuation to space
      .trim();
  }

  /**
   * 🚀 新增：检测是否为SQL相关查询
   */
  isSQLRelatedQuery(query) {
    const sqlKeywords = [
      '建表', '表结构', '创建表', 'create table', 'table',
      '字段', '列', 'column', 'field',
      '数据库', 'database', 'db',
      'sql', 'mysql', 'postgresql'
    ];

    const lowerQuery = query.toLowerCase();
    return sqlKeywords.some(keyword => lowerQuery.includes(keyword));
  }

  /**
   * 🚀 新增：从查询中提取表名
   */
  extractTableNames(query) {
    const tableNames = [];

    // 匹配常见的表名模式
    const patterns = [
      /(\w+)表/g, // 中文：用户表、订单表
      /table\s+(\w+)/gi, // table user
      /(\w+)\s*的?\s*(表|table)/gi, // 用户的表
      /`(\w+)`/g, // `table_name`
      /'(\w+)'/g, // 'table_name'
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(query)) !== null) {
        if (match[1] && match[1].length > 2) {
          tableNames.push(match[1]);
        }
      }
    });

    return [...new Set(tableNames)]; // 去重
  }

  /**
   * 🚀 Enhanced: Code-related query detection
   */
  isCodeRelatedQuery(query) {
    const codeKeywords = [
      '函数', '方法', 'function', 'method',
      '类', 'class', '对象', 'object',
      '代码', 'code', '实现', 'implementation',
      '算法', 'algorithm', '脚本', 'script',
      '模块', 'module', '组件', 'component'
    ];

    const lowerQuery = query.toLowerCase();
    return codeKeywords.some(keyword => lowerQuery.includes(keyword));
  }

  /**
   * 🚀 New: API-related query detection
   */
  isAPIRelatedQuery(query) {
    const apiKeywords = [
      'api', '接口', '端点', 'endpoint',
      'rest', 'http', 'get', 'post', 'put', 'delete',
      '请求', 'request', '响应', 'response',
      '参数', 'parameter', '头部', 'header'
    ];

    const lowerQuery = query.toLowerCase();
    return apiKeywords.some(keyword => lowerQuery.includes(keyword));
  }

  /**
   * 🚀 New: Technical documentation query detection
   */
  isTechnicalQuery(query) {
    const techKeywords = [
      '架构', 'architecture', '系统', 'system',
      '技术', 'technical', '设计', 'design',
      '部署', 'deployment', '基础设施', 'infrastructure',
      '协议', 'protocol', '规范', 'specification'
    ];

    const lowerQuery = query.toLowerCase();
    return techKeywords.some(keyword => lowerQuery.includes(keyword));
  }

  /**
   * 🚀 New: Business process query detection
   */
  isBusinessQuery(query) {
    const businessKeywords = [
      '业务', 'business', '流程', 'process',
      '需求', 'requirement', '规则', 'rule',
      '政策', 'policy', '程序', 'procedure',
      '工作流', 'workflow', '用户', 'user',
      '客户', 'customer', '利益相关者', 'stakeholder'
    ];

    const lowerQuery = query.toLowerCase();
    return businessKeywords.some(keyword => lowerQuery.includes(keyword));
  }

  /**
   * 🚀 New: Configuration query detection
   */
  isConfigQuery(query) {
    const configKeywords = [
      '配置', 'config', 'configuration',
      '设置', 'setting', '参数', 'parameter',
      '环境', 'environment', 'env',
      '属性', 'property', '选项', 'option'
    ];

    const lowerQuery = query.toLowerCase();
    return configKeywords.some(keyword => lowerQuery.includes(keyword));
  }

  /**
   * 🚀 New: Extract function names from query
   */
  extractFunctionNames(query) {
    const functionNames = [];

    // Match function name patterns
    const patterns = [
      /(\w+)\s*函数/g, // 中文：getUserInfo函数
      /function\s+(\w+)/gi, // function getUserInfo
      /(\w+)\s*\(\s*\)/g, // getUserInfo()
      /(\w+)\s*方法/g, // getUserInfo方法
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(query)) !== null) {
        if (match[1] && match[1].length > 2) {
          functionNames.push(match[1]);
        }
      }
    });

    return [...new Set(functionNames)]; // Remove duplicates
  }

  /**
   * 🚀 New: Extract API endpoints from query
   */
  extractEndpoints(query) {
    const endpoints = [];

    // Match endpoint patterns
    const patterns = [
      /\/api\/\w+/gi, // /api/users
      /\/\w+\/\w+/g, // /users/profile
      /(GET|POST|PUT|DELETE|PATCH)\s+(\S+)/gi, // GET /api/users
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(query)) !== null) {
        if (match[0]) {
          endpoints.push(match[0]);
        }
      }
    });

    return [...new Set(endpoints)]; // Remove duplicates
  }

  /**
   * 🚀 New: Get search configuration based on query type
   */
  getSearchConfigForQueryType(queryType) {
    const configs = {
      sql: {
        primaryThreshold: 0.6,
        secondaryThreshold: 0.4,
        fallbackThreshold: 0.3,
        maxResults: 5,
        minResults: 2
      },
      code: {
        primaryThreshold: 0.65,
        secondaryThreshold: 0.45,
        fallbackThreshold: 0.35,
        maxResults: 6,
        minResults: 2
      },
      api: {
        primaryThreshold: 0.7,
        secondaryThreshold: 0.5,
        fallbackThreshold: 0.4,
        maxResults: 4,
        minResults: 1
      },
      technical: {
        primaryThreshold: 0.6,
        secondaryThreshold: 0.4,
        fallbackThreshold: 0.3,
        maxResults: 6,
        minResults: 2
      },
      business: {
        primaryThreshold: 0.55,
        secondaryThreshold: 0.35,
        fallbackThreshold: 0.25,
        maxResults: 7,
        minResults: 2
      },
      config: {
        primaryThreshold: 0.65,
        secondaryThreshold: 0.45,
        fallbackThreshold: 0.35,
        maxResults: 4,
        minResults: 1
      },
      generic: {
        primaryThreshold: 0.5,
        secondaryThreshold: 0.3,
        fallbackThreshold: 0.25,
        maxResults: 5,
        minResults: 2
      }
    };

    return configs[queryType] || configs.generic;
  }

  /**
   * 生成知识库使用统计
   * @param {Array} searchResults - 搜索结果
   * @returns {Object} 统计信息
   */
  generateUsageStats(searchResults) {
    if (!searchResults || searchResults.length === 0) {
      return null;
    }

    const stats = {
      totalResults: searchResults.length,
      knowledgeBases: {},
      avgSimilarity: 0
    };

    let totalSimilarity = 0;
    searchResults.forEach(result => {
      // 统计每个知识库的使用情况
      const kbName = result.knowledgeBaseName;
      if (!stats.knowledgeBases[kbName]) {
        stats.knowledgeBases[kbName] = {
          name: kbName,
          count: 0,
          maxSimilarity: 0
        };
      }
      stats.knowledgeBases[kbName].count++;
      stats.knowledgeBases[kbName].maxSimilarity = Math.max(
        stats.knowledgeBases[kbName].maxSimilarity,
        result.similarity || 0
      );

      totalSimilarity += result.similarity || 0;
    });

    stats.avgSimilarity = totalSimilarity / searchResults.length;
    return stats;
  }
}
