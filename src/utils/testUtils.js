/**
 * 测试工具类 - 用于验证应用功能
 */

import { DataManager } from './dataManager';
import { APIManager } from './apiManager';
import { LoopProtectionManager } from './loopProtection';
import { CoordinatorManager } from './coordinatorManager';

export class TestUtils {
  constructor() {
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始运行JDCAIChat功能测试...');
    
    this.testResults = [];
    
    // 数据管理测试
    await this.testDataManager();
    
    // 防死循环保护测试
    await this.testLoopProtection();
    
    // 中控调度测试
    await this.testCoordinatorManager();
    
    // API管理测试
    await this.testAPIManager();
    
    // 生成测试报告
    this.generateTestReport();
    
    return this.testResults;
  }

  /**
   * 测试数据管理器
   */
  async testDataManager() {
    console.log('📊 测试数据管理器...');
    
    try {
      const dataManager = new DataManager();
      await dataManager.initialize();
      
      // 测试配置管理
      const testConfig = {
        name: '测试配置组',
        baseUrl: 'http://localhost:8000/v1',
        apiKey: 'test-api-key'
      };
      
      const config = dataManager.addConfig(testConfig);
      this.addTestResult('数据管理器 - 添加配置', true, '成功添加配置组');
      
      // 测试模型管理
      const testModel = {
        name: 'test-model',
        supportThinking: true,
        supportVision: false,
        uploadTypes: ['file']
      };
      
      const model = dataManager.addModel(config.id, testModel);
      this.addTestResult('数据管理器 - 添加模型', true, '成功添加模型');
      
      // 测试搭档管理
      const testPartner = {
        name: '测试搭档',
        prompt: '你是一个测试助手'
      };
      
      const partner = dataManager.addPartner(testPartner);
      this.addTestResult('数据管理器 - 添加搭档', true, '成功添加搭档');
      
      // 测试会话管理
      const session = dataManager.createSession('chatroom', {
        invitedModels: [model.id],
        coordinator: model.id,
        partnerId: partner.id
      });
      
      this.addTestResult('数据管理器 - 创建会话', true, '成功创建会话');
      
      // 测试消息管理
      const message = dataManager.addMessage(session.id, {
        sender: 'user',
        content: '测试消息'
      });
      
      this.addTestResult('数据管理器 - 添加消息', true, '成功添加消息');
      
      // 清理测试数据
      dataManager.deleteConfig(config.id);
      dataManager.deletePartner(partner.id);
      dataManager.deleteSession(session.id);
      
    } catch (error) {
      this.addTestResult('数据管理器', false, `测试失败: ${error.message}`);
    }
  }

  /**
   * 测试防死循环保护机制
   */
  async testLoopProtection() {
    console.log('🔄 测试防死循环保护机制...');
    
    try {
      const loopProtection = new LoopProtectionManager({
        maxRounds: 5,
        similarityThreshold: 0.8
      });
      
      // 测试回合数限制
      for (let i = 0; i < 6; i++) {
        const result = loopProtection.checkProtection({
          sender: 'model-a',
          content: `测试消息 ${i}`
        });
        
        if (i === 5) {
          this.addTestResult(
            '防死循环 - 回合数限制', 
            result.shouldStop, 
            result.shouldStop ? '正确触发回合数限制' : '未触发回合数限制'
          );
        }
      }
      
      // 重置并测试内容重复检测
      loopProtection.reset();
      
      const repeatedContent = '这是重复的内容';
      for (let i = 0; i < 4; i++) {
        const result = loopProtection.checkProtection({
          sender: 'model-b',
          content: repeatedContent
        });
        
        if (i === 3) {
          this.addTestResult(
            '防死循环 - 内容重复检测', 
            result.shouldStop, 
            result.shouldStop ? '正确检测到内容重复' : '未检测到内容重复'
          );
        }
      }
      
      // 重置并测试循环模式检测
      loopProtection.reset();
      
      const pattern = ['model-a', 'model-b', 'model-a', 'model-b', 'model-a', 'model-b'];
      let lastResult = null;
      
      for (let i = 0; i < pattern.length; i++) {
        lastResult = loopProtection.checkProtection({
          sender: pattern[i],
          content: `消息 ${i}`
        });
      }
      
      this.addTestResult(
        '防死循环 - 循环模式检测', 
        lastResult?.shouldStop || false, 
        lastResult?.shouldStop ? '正确检测到循环模式' : '未检测到循环模式'
      );
      
    } catch (error) {
      this.addTestResult('防死循环保护', false, `测试失败: ${error.message}`);
    }
  }

  /**
   * 测试中控调度管理器
   */
  async testCoordinatorManager() {
    console.log('🎯 测试中控调度管理器...');
    
    try {
      const coordinatorManager = new CoordinatorManager();
      
      // 模拟模型数据
      const mockModels = [
        { id: '1', name: 'model-a', supportThinking: true },
        { id: '2', name: 'model-b', supportThinking: false },
        { id: '3', name: 'model-c', supportVision: true }
      ];
      
      // 测试降级选择策略
      const fallbackModel = coordinatorManager.selectFallbackModel(mockModels);
      this.addTestResult(
        '中控调度 - 降级选择', 
        fallbackModel !== null, 
        fallbackModel ? `成功选择降级模型: ${fallbackModel.name}` : '降级选择失败'
      );
      
      // 测试调度提示词生成
      const prompt = coordinatorManager.generateCoordinatorPrompt(
        mockModels,
        { messages: [] },
        []
      );
      
      this.addTestResult(
        '中控调度 - 提示词生成', 
        prompt.length > 0, 
        prompt.length > 0 ? '成功生成调度提示词' : '提示词生成失败'
      );
      
      // 测试调度响应解析
      const mockResponse = JSON.stringify({
        selectedModel: 'model-a',
        reasoning: '测试选择理由',
        confidence: 0.8
      });
      
      const parseResult = coordinatorManager.parseSchedulingResponse(mockResponse, mockModels);
      this.addTestResult(
        '中控调度 - 响应解析', 
        parseResult.selectedModel !== null, 
        parseResult.selectedModel ? '成功解析调度响应' : '响应解析失败'
      );
      
    } catch (error) {
      this.addTestResult('中控调度管理器', false, `测试失败: ${error.message}`);
    }
  }

  /**
   * 测试API管理器
   */
  async testAPIManager() {
    console.log('🌐 测试API管理器...');
    
    try {
      const apiManager = new APIManager();
      
      // 测试请求状态管理
      const initialRequests = apiManager.getActiveRequests();
      this.addTestResult(
        'API管理器 - 初始状态', 
        Array.isArray(initialRequests) && initialRequests.length === 0, 
        '初始状态正确，无活跃请求'
      );
      
      // 测试错误格式化
      const mockError = {
        response: {
          status: 401,
          data: { error: { message: '未授权' } }
        }
      };
      
      const formattedError = apiManager.formatError(mockError);
      this.addTestResult(
        'API管理器 - 错误格式化', 
        formattedError.includes('API密钥'), 
        '正确格式化401错误'
      );
      
      // 测试请求消息构建
      const mockMessages = [
        { sender: 'user', content: '测试问题' },
        { sender: 'assistant', content: '测试回答' }
      ];
      
      const mockModel = { supportThinking: true };
      const requestMessages = apiManager.buildRequestMessages(mockMessages, mockModel, {
        partnerPrompt: '你是测试助手'
      });
      
      this.addTestResult(
        'API管理器 - 消息构建', 
        Array.isArray(requestMessages) && requestMessages.length > 0, 
        `成功构建请求消息，共${requestMessages.length}条`
      );
      
      // 清理资源
      apiManager.cleanup();
      
    } catch (error) {
      this.addTestResult('API管理器', false, `测试失败: ${error.message}`);
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, passed, message) {
    this.testResults.push({
      name: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    });
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;
    
    console.log('\n📋 测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${successRate}%`);
    console.log('='.repeat(50));
    
    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`  - ${r.name}: ${r.message}`));
    }
    
    console.log('\n🎉 测试完成！');
    
    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: parseFloat(successRate),
      results: this.testResults
    };
  }
}

// 导出测试函数供控制台使用
export const runTests = async () => {
  const testUtils = new TestUtils();
  return await testUtils.runAllTests();
};
