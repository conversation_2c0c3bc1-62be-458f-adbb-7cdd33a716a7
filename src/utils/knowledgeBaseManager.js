import { OptimizedVectorDatabase } from './optimizedVectorDatabase.js';
import { EmbeddingService } from './embeddingService.js';
import { IntelligentChunker } from './intelligentChunker.js'; // 🚀 新增：智能分块服务

/**
 * 知识库向量管理器
 * 集成向量数据库和嵌入服务，提供完整的知识库功能
 */
export class KnowledgeBaseManager {
  constructor(dataManager) {
    this.dataManager = dataManager;
    this.vectorDB = new OptimizedVectorDatabase(); // 🚀 使用优化的向量数据库
    this.embeddingService = new EmbeddingService();
    this.intelligentChunker = null; // 🚀 新增：智能分块器（需要APIManager）
    this.isInitialized = false;
  }

  /**
   * 🚀 新增：设置API管理器（用于智能分块）
   */
  setAPIManager(apiManager) {
    this.intelligentChunker = new IntelligentChunker(apiManager);
    console.log('✅ [KnowledgeBase] 智能分块器已初始化');
  }

  /**
   * 初始化知识库管理器
   */
  async initialize() {
    try {
      await this.vectorDB.initialize();
      this.isInitialized = true;
      console.log('✅ [KnowledgeBase] 知识库管理器初始化完成');
    } catch (error) {
      console.error('❌ [KnowledgeBase] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 添加文档到知识库（支持分层分块和智能分块）
   */
  async addDocument(knowledgeBaseId, document, onProgress = null) {
    if (!this.isInitialized) await this.initialize();

    try {
      // 获取知识库配置
      const knowledgeBase = this.dataManager.getKnowledgeBase(knowledgeBaseId);
      if (!knowledgeBase) {
        throw new Error('知识库不存在');
      }

      // 获取embedding模型
      const embeddingModel = this.getEmbeddingModel(knowledgeBase.embeddingModel);
      if (!embeddingModel) {
        throw new Error('找不到指定的embedding模型');
      }

      console.log(`🔄 [KnowledgeBase] 开始处理文档: ${document.title}`);

      // 🚀 新增：检查是否使用智能分块
      const useIntelligentChunking = document.chunkingMethod === 'intelligent';

      if (useIntelligentChunking) {
        if (!this.intelligentChunker) {
          console.warn('⚠️ [KnowledgeBase] 智能分块器未初始化，降级到传统分块');
        } else if (!document.chatModel) {
          console.warn('⚠️ [KnowledgeBase] 未指定Chat模型，降级到传统分块');
        } else {
          // 获取Chat模型
          const chatModel = this.getChatModel(document.chatModel);
          if (chatModel) {
            return await this.addDocumentWithIntelligentChunks(
              knowledgeBaseId,
              document,
              embeddingModel,
              chatModel,
              onProgress
            );
          } else {
            console.warn('⚠️ [KnowledgeBase] 找不到指定的Chat模型，降级到传统分块');
          }
        }
      }

      // 检查是否使用分层分块
      const useHierarchical = document.hierarchical || false;

      if (useHierarchical) {
        return await this.addDocumentWithHierarchicalChunks(knowledgeBaseId, document, embeddingModel, onProgress);
      } else {
        return await this.addDocumentWithFlatChunks(knowledgeBaseId, document, embeddingModel, onProgress);
      }

    } catch (error) {
      console.error('❌ [KnowledgeBase] 文档添加失败:', error);
      throw error;
    }
  }

  /**
   * 🚀 新增：使用智能分块添加文档
   */
  async addDocumentWithIntelligentChunks(knowledgeBaseId, document, embeddingModel, chatModel, onProgress = null) {
    try {
      console.log('🧠 [KnowledgeBase] 开始智能分块处理');

      // 使用智能分块器分块
      const chunks = await this.intelligentChunker.intelligentChunk(
        document.content,
        chatModel,
        {
          maxChunkSize: document.chunkSize || 800,
          minChunkSize: 200,
          preserveStructure: true,
          hierarchical: document.hierarchical || false
        }
      );

      console.log(`🧠 [KnowledgeBase] 智能分块完成，共 ${chunks.length} 个分块`);

      if (onProgress) {
        onProgress({
          current: 0,
          total: chunks.length,
          message: '开始向量化智能分块...'
        });
      }

      const vectorsToAdd = [];
      const processedChunks = [];
      let successCount = 0;

      // 处理每个分块
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];

        try {
          // 生成向量
          const vector = await this.embeddingService.textToVector(chunk.text, embeddingModel);
          const vectorId = `intelligent_${Date.now()}_${i}`;

          vectorsToAdd.push({
            id: vectorId,
            vector: vector,
            metadata: {
              knowledgeBaseId,
              documentId: document.id,
              documentTitle: document.title,
              chunkText: chunk.text,
              chunkType: 'intelligent',
              chunkIndex: i,
              intelligentChunking: true,
              contentType: chunk.metadata?.contentType || 'text',
              language: chunk.metadata?.language,
              title: chunk.title,
              createdAt: new Date().toISOString()
            }
          });

          processedChunks.push({
            id: `chunk_${i}`,
            text: chunk.text,
            type: chunk.type,
            title: chunk.title,
            vectorId: vectorId,
            vectorized: true,
            metadata: chunk.metadata
          });

          successCount++;

          if (onProgress) {
            onProgress({
              current: i + 1,
              total: chunks.length,
              message: `已处理 ${i + 1}/${chunks.length} 个智能分块`
            });
          }

        } catch (error) {
          console.error(`❌ [KnowledgeBase] 处理分块 ${i} 失败:`, error);
          processedChunks.push({
            id: `chunk_${i}`,
            text: chunk.text,
            type: chunk.type,
            title: chunk.title,
            vectorId: null,
            vectorized: false,
            error: error.message,
            metadata: chunk.metadata
          });
        }
      }

      // 批量添加向量到数据库
      if (vectorsToAdd.length > 0) {
        await this.vectorDB.addVectors(vectorsToAdd);
        console.log(`✅ [KnowledgeBase] 已添加 ${vectorsToAdd.length} 个向量到数据库`);
      }

      // 更新文档记录
      const updatedDocument = {
        ...document,
        chunks: processedChunks,
        vectorized: true,
        chunkCount: chunks.length,
        vectorCount: successCount,
        chunkingMethod: 'intelligent',
        processedAt: new Date().toISOString()
      };

      // 更新知识库中的文档
      const updateSuccess = this.dataManager.updateDocumentInKB(knowledgeBaseId, updatedDocument);
      if (!updateSuccess) {
        console.warn('⚠️ [KnowledgeBase] 文档更新到知识库失败，但向量化已完成');
      }

      return {
        success: true,
        vectorCount: successCount,
        totalChunks: chunks.length,
        processedChunks: successCount,
        chunkCount: chunks.length,
        document: updatedDocument
      };

    } catch (error) {
      console.error('❌ [KnowledgeBase] 智能分块处理失败:', error);
      throw error;
    }
  }

  /**
   * 🚀 新增：使用分层分块添加文档
   */
  async addDocumentWithHierarchicalChunks(knowledgeBaseId, document, embeddingModel, onProgress = null) {
    // 分层分块
    const hierarchicalChunks = this.embeddingService.splitDocument(document.content, {
      hierarchical: true,
      parentChunkSize: document.parentChunkSize || 800,
      childChunkSize: document.childChunkSize || 200,
      overlap: document.chunkOverlap || 50,
      preserveParagraphs: true
    });

    console.log(`📄 [KnowledgeBase] 分层分块完成，共 ${hierarchicalChunks.length} 个父分块`);

    // 分层批量向量化
    const vectorResults = await this.embeddingService.batchHierarchicalTextToVector(
      hierarchicalChunks,
      embeddingModel,
      (progress) => {
        if (onProgress) {
          onProgress({
            stage: 'vectorizing',
            ...progress,
            message: `正在向量化 ${progress.stage === 'parent' ? '父' : '子'}分块 ${progress.current}/${progress.total}`
          });
        }
      }
    );

    // 准备向量数据
    const vectorsToAdd = [];
    const processedChunks = [];

    vectorResults.forEach((parentResult, parentIndex) => {
      // 处理父分块
      if (parentResult.vector) {
        const parentVectorId = `${knowledgeBaseId}_${document.id}_parent_${parentIndex}`;
        vectorsToAdd.push({
          id: parentVectorId,
          vector: parentResult.vector,
          metadata: {
            knowledgeBaseId,
            documentId: document.id,
            documentTitle: document.title,
            chunkIndex: parentIndex,
            chunkText: parentResult.text,
            chunkType: 'parent',
            level: 0,
            parentId: null,
            createdAt: new Date().toISOString()
          }
        });

        const processedParent = {
          id: parentResult.id,
          text: parentResult.text,
          level: 0,
          parentId: null,
          vectorId: parentVectorId,
          vectorized: true,
          children: []
        };

        // 处理子分块
        parentResult.children.forEach((childResult, childIndex) => {
          if (childResult.vector) {
            const childVectorId = `${knowledgeBaseId}_${document.id}_parent_${parentIndex}_child_${childIndex}`;
            vectorsToAdd.push({
              id: childVectorId,
              vector: childResult.vector,
              metadata: {
                knowledgeBaseId,
                documentId: document.id,
                documentTitle: document.title,
                chunkIndex: childIndex,
                chunkText: childResult.text,
                chunkType: 'child',
                level: 1,
                parentId: parentVectorId,
                createdAt: new Date().toISOString()
              }
            });

            processedParent.children.push({
              id: childResult.id,
              text: childResult.text,
              level: 1,
              parentId: parentResult.id,
              vectorId: childVectorId,
              vectorized: true
            });
          } else {
            processedParent.children.push({
              id: childResult.id,
              text: childResult.text,
              level: 1,
              parentId: parentResult.id,
              vectorId: null,
              vectorized: false,
              error: childResult.error
            });
          }
        });

        processedChunks.push(processedParent);
      }
    });

    // 批量添加向量到数据库
    if (vectorsToAdd.length > 0) {
      await this.vectorDB.addVectors(vectorsToAdd);

      if (onProgress) {
        onProgress({
          stage: 'storing',
          current: vectorsToAdd.length,
          total: vectorsToAdd.length,
          percentage: 100,
          message: `已存储 ${vectorsToAdd.length} 个向量`
        });
      }
    }

    // 更新文档信息
    const updatedDocument = {
      ...document,
      vectorized: true,
      hierarchical: true,
      chunks: processedChunks,
      vectorCount: vectorsToAdd.length,
      parentChunkCount: processedChunks.length,
      childChunkCount: processedChunks.reduce((total, parent) => total + parent.children.length, 0),
      processedAt: new Date().toISOString()
    };

    // 保存到数据管理器
    this.dataManager.addDocumentToKB(knowledgeBaseId, updatedDocument);

    console.log(`✅ [KnowledgeBase] 分层文档处理完成: ${vectorsToAdd.length} 个向量，${processedChunks.length} 个父分块`);

    return {
      success: true,
      document: updatedDocument,
      vectorCount: vectorsToAdd.length,
      parentChunkCount: processedChunks.length,
      childChunkCount: processedChunks.reduce((total, parent) => total + parent.children.length, 0),
      hierarchical: true
    };
  }

  /**
   * 🚀 新增：使用平面分块添加文档（原有逻辑）
   */
  async addDocumentWithFlatChunks(knowledgeBaseId, document, embeddingModel, onProgress = null) {
    // 文档分块
    const chunks = this.embeddingService.splitDocument(document.content, {
      chunkSize: document.chunkSize || 500,
      overlap: document.chunkOverlap || 50,
      preserveParagraphs: true
    });

    console.log(`📄 [KnowledgeBase] 文档分块完成，共 ${chunks.length} 个块`);

    // 批量向量化
    const vectorResults = await this.embeddingService.batchTextToVector(
      chunks,
      embeddingModel,
      (progress) => {
        if (onProgress) {
          onProgress({
            stage: 'vectorizing',
            ...progress,
            message: `正在向量化第 ${progress.current}/${progress.total} 个文本块`
          });
        }
      }
    );

    // 准备向量数据
    const vectorsToAdd = [];
    const successfulChunks = [];

    vectorResults.forEach((result, index) => {
      if (result.vector) {
        const vectorId = `${knowledgeBaseId}_${document.id}_chunk_${index}`;
        vectorsToAdd.push({
          id: vectorId,
          vector: result.vector,
          metadata: {
            knowledgeBaseId,
            documentId: document.id,
            documentTitle: document.title,
            chunkIndex: index,
            chunkText: result.text,
            chunkType: 'flat',
            level: 0,
            parentId: null,
            createdAt: new Date().toISOString()
          }
        });
        successfulChunks.push({
          index,
          text: result.text,
          vectorId,
          level: 0,
          parentId: null,
          children: []
        });
      }
    });

    // 批量添加向量到数据库
    if (vectorsToAdd.length > 0) {
      await this.vectorDB.addVectors(vectorsToAdd);

      if (onProgress) {
        onProgress({
          stage: 'storing',
          current: vectorsToAdd.length,
          total: vectorsToAdd.length,
          percentage: 100,
          message: `已存储 ${vectorsToAdd.length} 个向量`
        });
      }
    }

    // 更新文档信息
    const updatedDocument = {
      ...document,
      vectorized: true,
      hierarchical: false,
      chunks: successfulChunks,
      vectorCount: vectorsToAdd.length,
      failedChunks: chunks.length - vectorsToAdd.length,
      processedAt: new Date().toISOString()
    };

    // 保存到数据管理器
    this.dataManager.addDocumentToKB(knowledgeBaseId, updatedDocument);

    console.log(`✅ [KnowledgeBase] 平面文档处理完成: ${vectorsToAdd.length}/${chunks.length} 个块成功向量化`);

    return {
      success: true,
      document: updatedDocument,
      vectorCount: vectorsToAdd.length,
      totalChunks: chunks.length,
      failedChunks: chunks.length - vectorsToAdd.length,
      hierarchical: false
    };
  }

  /**
   * 搜索知识库（支持分层搜索）
   */
  async searchKnowledgeBase(knowledgeBaseId, query, options = {}) {
    if (!this.isInitialized) await this.initialize();

    try {
      const {
        limit = 5,
        threshold = 0.7,
        includeChunks = true,
        searchLevel = 'all', // 'parent', 'child', 'all'
        includeContext = true // 是否包含上下文
      } = options;

      // 获取知识库配置
      const knowledgeBase = this.dataManager.getKnowledgeBase(knowledgeBaseId);
      if (!knowledgeBase) {
        throw new Error('知识库不存在');
      }

      // 获取embedding模型
      const embeddingModel = this.getEmbeddingModel(knowledgeBase.embeddingModel);
      if (!embeddingModel) {
        throw new Error('找不到指定的embedding模型');
      }

      console.log(`🔍 [KnowledgeBase] 开始分层搜索: "${query}", 搜索级别: ${searchLevel}`);

      // 将查询转换为向量
      const queryVector = await this.embeddingService.textToVector(query, embeddingModel);

      // 在向量数据库中搜索
      const vectorResults = await this.vectorDB.search(queryVector, {
        limit: limit * 3, // 获取更多结果用于分层处理
        threshold,
        knowledgeBaseId
      });

      console.log(`🎯 [KnowledgeBase] 找到 ${vectorResults.length} 个相关向量`);

      // 根据搜索级别过滤结果
      const filteredResults = this.filterResultsByLevel(vectorResults, searchLevel);

      // 按文档分组并合并结果
      const documentResults = new Map();

      filteredResults.forEach(result => {
        const {
          documentId,
          documentTitle,
          chunkText,
          chunkIndex,
          chunkType = 'flat',
          level = 0,
          parentId = null
        } = result.metadata;
        
        if (!documentResults.has(documentId)) {
          documentResults.set(documentId, {
            documentId,
            documentTitle,
            maxSimilarity: result.similarity,
            avgSimilarity: result.similarity,
            chunks: [],
            relevantChunks: 1
          });
        }

        const docResult = documentResults.get(documentId);
        docResult.chunks.push({
          chunkIndex,
          text: chunkText,
          similarity: result.similarity,
          chunkType,
          level,
          parentId,
          vectorId: result.id
        });

        // 更新相似度统计
        docResult.maxSimilarity = Math.max(docResult.maxSimilarity, result.similarity);
        docResult.avgSimilarity = (docResult.avgSimilarity * docResult.relevantChunks + result.similarity) / (docResult.relevantChunks + 1);
        docResult.relevantChunks++;
      });

      // 增强文档结果（支持分层结构）
      let results = this.enhanceDocumentResults(documentResults, filteredResults, includeContext);

      // 按相似度排序并限制结果数量
      results = results
        .sort((a, b) => b.maxSimilarity - a.maxSimilarity)
        .slice(0, limit);

      // 如果不需要包含块信息，则移除
      if (!includeChunks) {
        results = results.map(result => ({
          documentId: result.documentId,
          documentTitle: result.documentTitle,
          maxSimilarity: result.maxSimilarity,
          avgSimilarity: result.avgSimilarity,
          relevantChunks: result.relevantChunks,
          hasHierarchy: result.hasHierarchy,
          parentChunkCount: result.parentChunks?.length || 0,
          childChunkCount: result.childChunks?.length || 0
        }));
      }

      console.log(`✅ [KnowledgeBase] 分层搜索完成，返回 ${results.length} 个文档结果`);

      return {
        query,
        results,
        totalVectorMatches: vectorResults.length,
        filteredMatches: filteredResults.length,
        searchLevel,
        includeContext,
        searchTime: Date.now(),
        hierarchicalResults: results.some(r => r.hasHierarchy)
      };

    } catch (error) {
      console.error('❌ [KnowledgeBase] 搜索失败:', error);
      throw error;
    }
  }

  /**
   * 删除知识库的所有向量数据
   */
  async deleteKnowledgeBaseVectors(knowledgeBaseId) {
    if (!this.isInitialized) await this.initialize();

    try {
      const deletedIds = await this.vectorDB.deleteVectorsByKnowledgeBase(knowledgeBaseId);
      console.log(`🗑️ [KnowledgeBase] 已删除知识库 ${knowledgeBaseId} 的 ${deletedIds.length} 个向量`);
      return deletedIds;
    } catch (error) {
      console.error('❌ [KnowledgeBase] 删除向量失败:', error);
      throw error;
    }
  }

  /**
   * 删除文档的向量数据
   */
  async deleteDocumentVectors(knowledgeBaseId, documentId) {
    if (!this.isInitialized) await this.initialize();

    try {
      // 查找并删除该文档的所有向量
      const vectorResults = await this.vectorDB.search([], {
        limit: 1000,
        threshold: 0,
        knowledgeBaseId,
        documentId
      });

      const deletePromises = vectorResults.map(result => 
        this.vectorDB.deleteVector(result.id)
      );

      await Promise.all(deletePromises);
      
      console.log(`🗑️ [KnowledgeBase] 已删除文档 ${documentId} 的 ${vectorResults.length} 个向量`);
      return vectorResults.length;
    } catch (error) {
      console.error('❌ [KnowledgeBase] 删除文档向量失败:', error);
      throw error;
    }
  }

  /**
   * 获取embedding模型
   */
  getEmbeddingModel(modelId) {
    const configs = this.dataManager.getConfigs();

    for (const config of configs) {
      if (config.models) {
        for (const model of config.models) {
          if (model.id === modelId && model.type === 'embedding') {
            return {
              ...model,
              baseUrl: config.baseUrl,
              apiKey: config.apiKey
            };
          }
        }
      }
    }

    return null;
  }

  /**
   * 🚀 新增：获取Chat模型（用于智能分块）
   */
  getChatModel(modelId) {
    const configs = this.dataManager.getConfigs();

    for (const config of configs) {
      if (config.models) {
        for (const model of config.models) {
          if (model.id === modelId && model.type !== 'embedding') {
            const chatModel = {
              ...model,
              baseUrl: config.baseUrl,
              apiKey: config.apiKey
            };

            // 🚀 修复：验证和清理maxTokens数据
            console.log(`🔍 [KnowledgeBase] 原始模型数据:`, {
              id: model.id,
              name: model.name,
              maxTokens: model.maxTokens,
              max_tokens: model.max_tokens,
              contextLength: model.contextLength
            });

            // 确保maxTokens是合理的数值
            if (chatModel.maxTokens && (typeof chatModel.maxTokens !== 'number' || chatModel.maxTokens > 1000000)) {
              console.warn(`⚠️ [KnowledgeBase] 模型 ${model.name} 的maxTokens值异常: ${chatModel.maxTokens}，重置为默认值`);
              chatModel.maxTokens = this.getDefaultMaxTokens(model.name);
            }

            return chatModel;
          }
        }
      }
    }

    return null;
  }

  /**
   * 🚀 新增：根据模型名称获取默认的maxTokens
   */
  getDefaultMaxTokens(modelName) {
    const name = modelName.toLowerCase();

    // 根据常见模型设置合理的默认值
    if (name.includes('gpt-4') || name.includes('claude')) {
      return 8000;
    } else if (name.includes('gpt-3.5')) {
      return 4000;
    } else if (name.includes('gemini')) {
      return 8000;
    } else if (name.includes('llama') || name.includes('qwen')) {
      return 4000;
    }

    return 4000; // 默认值
  }

  /**
   * 获取所有可用的embedding模型
   */
  getAvailableEmbeddingModels() {
    const configs = this.dataManager.getConfigs();
    const models = [];
    
    configs.forEach(config => {
      if (config.models) {
        config.models.forEach(model => {
          if (model.type === 'embedding') {
            models.push({
              ...model,
              configId: config.id,
              baseUrl: config.baseUrl,
              apiKey: config.apiKey
            });
          }
        });
      }
    });
    
    return models;
  }

  /**
   * 测试embedding模型
   */
  async testEmbeddingModel(modelId) {
    try {
      const model = this.getEmbeddingModel(modelId);
      if (!model) {
        throw new Error('模型不存在');
      }

      return await this.embeddingService.testModel(model);
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取向量数据库统计信息
   */
  async getVectorDBStats() {
    if (!this.isInitialized) await this.initialize();
    
    try {
      const stats = await this.vectorDB.getStats();
      const cacheStats = this.embeddingService.getCacheStats();
      
      return {
        vectorDatabase: stats,
        embeddingCache: cacheStats,
        knowledgeBases: this.dataManager.getKnowledgeBases().length
      };
    } catch (error) {
      console.error('❌ [KnowledgeBase] 获取统计信息失败:', error);
      return null;
    }
  }

  /**
   * 🚀 新增：根据搜索级别过滤结果
   */
  filterResultsByLevel(vectorResults, searchLevel) {
    if (searchLevel === 'all') {
      return vectorResults;
    }

    return vectorResults.filter(result => {
      const { chunkType = 'flat', level = 0 } = result.metadata;

      if (searchLevel === 'parent') {
        return chunkType === 'parent' || (chunkType === 'flat' && level === 0);
      } else if (searchLevel === 'child') {
        return chunkType === 'child' && level === 1;
      }

      return true;
    });
  }

  /**
   * 🚀 新增：增强文档结果处理（支持分层结构）
   */
  enhanceDocumentResults(documentResults, vectorResults, includeContext = true) {
    const enhancedResults = [];

    for (const [documentId, docResult] of documentResults) {
      const enhanced = { ...docResult };

      // 分离父分块和子分块
      const parentChunks = [];
      const childChunks = [];

      enhanced.chunks.forEach(chunk => {
        if (chunk.level === 0 || chunk.chunkType === 'parent' || chunk.chunkType === 'flat') {
          parentChunks.push(chunk);
        } else if (chunk.level === 1 || chunk.chunkType === 'child') {
          childChunks.push(chunk);
        }
      });

      // 如果包含上下文，为子分块添加父分块信息
      if (includeContext && childChunks.length > 0) {
        childChunks.forEach(childChunk => {
          if (childChunk.parentId) {
            // 查找对应的父分块
            const parentVector = vectorResults.find(v => v.id === childChunk.parentId);
            if (parentVector) {
              childChunk.parentContext = {
                text: parentVector.metadata.chunkText,
                similarity: parentVector.similarity
              };
            }
          }
        });
      }

      enhanced.parentChunks = parentChunks;
      enhanced.childChunks = childChunks;
      enhanced.hasHierarchy = childChunks.length > 0;

      enhancedResults.push(enhanced);
    }

    return enhancedResults;
  }

  /**
   * 🚀 新增：向量搜索方法
   * @param {string} knowledgeBaseId - 知识库ID
   * @param {string} query - 查询文本
   * @param {Object} options - 搜索选项
   * @returns {Promise<Array>} 搜索结果
   */
  async searchVectors(knowledgeBaseId, query, options = {}) {
    if (!this.isInitialized) await this.initialize();

    const {
      topK = 5,
      threshold = 0.7,
      includeContext = true,
      searchLevel = 'all' // 'all', 'parent', 'child'
    } = options;

    try {
      console.log(`🔍 [KnowledgeBase] 开始向量搜索: ${query.substring(0, 50)}...`);

      // 获取知识库配置
      const knowledgeBase = this.dataManager.getKnowledgeBase(knowledgeBaseId);
      if (!knowledgeBase) {
        throw new Error('知识库不存在');
      }

      // 获取embedding模型
      const embeddingModel = this.getEmbeddingModel(knowledgeBase.embeddingModel);
      if (!embeddingModel) {
        throw new Error('找不到指定的embedding模型');
      }

      // 生成查询向量
      console.log('🔍 [KnowledgeBase] 准备生成查询向量...');
      console.log('🔍 [KnowledgeBase] embeddingService:', this.embeddingService);
      console.log('🔍 [KnowledgeBase] embeddingModel:', embeddingModel);

      if (!this.embeddingService) {
        throw new Error('EmbeddingService 未初始化');
      }

      if (typeof this.embeddingService.generateEmbedding !== 'function') {
        console.error('❌ [KnowledgeBase] generateEmbedding 方法不存在');
        console.error('❌ [KnowledgeBase] embeddingService 方法列表:', Object.getOwnPropertyNames(this.embeddingService));
        throw new Error('generateEmbedding 方法不存在');
      }

      const queryVector = await this.embeddingService.generateEmbedding(query, embeddingModel);
      if (!queryVector || queryVector.length === 0) {
        console.warn('⚠️ [KnowledgeBase] 查询向量生成失败');
        return [];
      }

      // 执行向量搜索
      const searchResults = await this.vectorDB.search(queryVector, {
        limit: topK,
        threshold: threshold,
        knowledgeBaseId: knowledgeBaseId
      });

      if (!searchResults || searchResults.length === 0) {
        console.log('ℹ️ [KnowledgeBase] 未找到相关内容');
        return [];
      }

      // 根据搜索级别过滤结果
      let filteredResults = searchResults;
      if (searchLevel === 'parent') {
        filteredResults = searchResults.filter(result =>
          result.metadata.chunkType === 'parent' || result.metadata.chunkType === 'flat'
        );
      } else if (searchLevel === 'child') {
        filteredResults = searchResults.filter(result =>
          result.metadata.chunkType === 'child'
        );
      }

      // 格式化搜索结果
      const formattedResults = filteredResults.map(result => ({
        id: result.id,
        text: result.metadata.chunkText || result.metadata.text || '',
        similarity: result.similarity,
        documentId: result.metadata.documentId,
        documentTitle: result.metadata.documentTitle || '未知文档',
        chunkType: result.metadata.chunkType || 'unknown',
        chunkIndex: result.metadata.chunkIndex || 0,
        parentId: result.metadata.parentId,
        knowledgeBaseName: knowledgeBase.name,
        knowledgeBaseId: knowledgeBaseId,
        embeddingModel: knowledgeBase.embeddingModel,
        metadata: result.metadata
      }));

      // 如果需要包含上下文，为子分块添加父分块信息
      if (includeContext) {
        for (const result of formattedResults) {
          if (result.chunkType === 'child' && result.parentId) {
            try {
              // 查找父分块 - 从内存中搜索
              const parentVector = this.vectorDB.vectors.get(result.parentId);
              if (parentVector) {
                result.parentContext = {
                  text: parentVector.metadata.chunkText || parentVector.metadata.text || '',
                  similarity: parentVector.similarity || 0
                };
              }
            } catch (error) {
              console.warn('⚠️ [KnowledgeBase] 获取父分块上下文失败:', error);
            }
          }
        }
      }

      console.log(`✅ [KnowledgeBase] 搜索完成，找到 ${formattedResults.length} 个相关结果`);
      return formattedResults;

    } catch (error) {
      console.error('❌ [KnowledgeBase] 向量搜索失败:', error);
      throw error;
    }
  }

  /**
   * 清理缓存和临时数据
   */
  async cleanup() {
    this.embeddingService.clearCache();
    console.log('🧹 [KnowledgeBase] 缓存清理完成');
  }
}
