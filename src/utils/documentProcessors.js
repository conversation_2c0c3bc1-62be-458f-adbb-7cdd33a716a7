/**
 * 📄 前端文档处理器集合
 * 支持多种文档格式的文本提取和处理
 */

/**
 * 基础文档处理器类
 */
class BaseDocumentProcessor {
  constructor() {
    this.supportedFormats = [];
  }

  /**
   * 检查是否支持该文件格式
   */
  supports(fileName) {
    const ext = '.' + fileName.split('.').pop().toLowerCase();
    return this.supportedFormats.includes(ext);
  }

  /**
   * 处理文档（需要子类实现）
   */
  async process(file, options = {}) {
    throw new Error('子类必须实现 process 方法');
  }
}

/**
 * 📝 文本文件处理器
 */
class TextProcessor extends BaseDocumentProcessor {
  constructor() {
    super();
    this.supportedFormats = ['.txt', '.md', '.csv', '.json', '.xml', '.html', '.css', '.js', '.ts'];
  }

  async process(file, options = {}) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const text = e.target.result;
          resolve({
            success: true,
            text: text,
            metadata: {
              fileName: file.name,
              fileSize: file.size,
              fileType: file.type,
              lastModified: new Date(file.lastModified),
              encoding: 'UTF-8',
              processingTime: Date.now()
            }
          });
        } catch (error) {
          reject(new Error(`文本文件处理失败: ${error.message}`));
        }
      };

      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };

      reader.readAsText(file, 'UTF-8');
    });
  }
}

/**
 * 🖼️ 图片OCR处理器（使用Tesseract.js）
 */
class ImageOCRProcessor extends BaseDocumentProcessor {
  constructor() {
    super();
    this.supportedFormats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    this.tesseractLoaded = false;
  }

  async loadTesseract() {
    if (this.tesseractLoaded) return;

    try {
      // 动态导入Tesseract.js
      const { createWorker } = await import('tesseract.js');
      this.createWorker = createWorker;
      this.tesseractLoaded = true;
      console.log('✅ [ImageOCR] Tesseract.js 加载成功');
    } catch (error) {
      console.warn('⚠️ [ImageOCR] Tesseract.js 加载失败:', error);
      throw new Error('OCR引擎加载失败，请检查网络连接');
    }
  }

  async process(file, options = {}) {
    const { language = 'chi_sim+eng' } = options;

    try {
      await this.loadTesseract();

      const worker = await this.createWorker(language);
      
      console.log('🔍 [ImageOCR] 开始OCR识别:', file.name);
      const { data: { text } } = await worker.recognize(file);
      
      await worker.terminate();

      return {
        success: true,
        text: text.trim(),
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          ocrLanguage: language,
          processingTime: Date.now(),
          extractionMethod: 'OCR'
        }
      };
    } catch (error) {
      console.error('❌ [ImageOCR] OCR处理失败:', error);
      throw new Error(`图片OCR处理失败: ${error.message}`);
    }
  }
}

/**
 * 📄 PDF处理器（使用PDF.js）
 */
class PDFProcessor extends BaseDocumentProcessor {
  constructor() {
    super();
    this.supportedFormats = ['.pdf'];
    this.pdfJSLoaded = false;
  }

  async loadPDFJS() {
    if (this.pdfJSLoaded) return;

    try {
      // 动态导入PDF.js
      const pdfjsLib = await import('pdfjs-dist');

      // 设置worker路径 - 使用更稳定的CDN
      pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.js`;

      this.pdfjsLib = pdfjsLib;
      this.pdfJSLoaded = true;
      console.log('✅ [PDF] PDF.js 加载成功，版本:', pdfjsLib.version);
    } catch (error) {
      console.warn('⚠️ [PDF] PDF.js 加载失败:', error);
      throw new Error('PDF处理引擎加载失败');
    }
  }

  async process(file, options = {}) {
    try {
      await this.loadPDFJS();

      const arrayBuffer = await file.arrayBuffer();

      // 添加更详细的错误处理
      if (!arrayBuffer || arrayBuffer.byteLength === 0) {
        throw new Error('文件为空或无法读取');
      }

      console.log(`📄 [PDF] 开始解析PDF文件，大小: ${arrayBuffer.byteLength} 字节`);

      const loadingTask = this.pdfjsLib.getDocument({
        data: arrayBuffer,
        // 添加一些配置选项
        verbosity: 0, // 减少日志输出
        isEvalSupported: false,
        disableFontFace: true
      });

      const pdf = await loadingTask.promise;

      let fullText = '';
      const numPages = pdf.numPages;

      console.log(`📄 [PDF] PDF解析成功，共 ${numPages} 页`);

      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        try {
          const page = await pdf.getPage(pageNum);
          const textContent = await page.getTextContent();

          // 更安全的文本提取
          let pageText = '';
          if (textContent && textContent.items && Array.isArray(textContent.items)) {
            pageText = textContent.items
              .filter(item => item && typeof item.str === 'string')
              .map(item => item.str)
              .join(' ')
              .replace(/\s+/g, ' ')
              .trim();
          }

          if (pageText && pageText.length > 0) {
            fullText += `\n\n=== 第 ${pageNum} 页 ===\n${pageText}`;
          }

          console.log(`📄 [PDF] 第 ${pageNum} 页处理完成，提取文本: ${pageText.length} 字符`);
        } catch (pageError) {
          console.warn(`⚠️ [PDF] 第 ${pageNum} 页处理失败:`, pageError);
          fullText += `\n\n=== 第 ${pageNum} 页 ===\n[页面处理失败: ${pageError.message}]`;
        }
      }

      // 确保返回的文本不为空
      const finalText = fullText.trim();
      if (!finalText || finalText.length === 0) {
        throw new Error('PDF文档中未找到可提取的文本内容');
      }

      return {
        success: true,
        text: finalText,
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          totalPages: numPages,
          processingTime: Date.now(),
          extractionMethod: 'PDF.js',
          textLength: finalText.length
        }
      };
    } catch (error) {
      console.error('❌ [PDF] PDF处理失败:', error);

      // 提供更详细的错误信息
      let errorMessage = 'PDF处理失败';
      if (error.message.includes('Invalid PDF')) {
        errorMessage = 'PDF文件格式无效或已损坏';
      } else if (error.message.includes('Password')) {
        errorMessage = 'PDF文件受密码保护，暂不支持';
      } else if (error.message.includes('网络')) {
        errorMessage = 'PDF处理引擎加载失败，请检查网络连接';
      } else {
        errorMessage = `PDF处理失败: ${error.message}`;
      }

      throw new Error(errorMessage);
    }
  }
}

/**
 * 📊 Excel处理器（使用SheetJS）
 */
class ExcelProcessor extends BaseDocumentProcessor {
  constructor() {
    super();
    this.supportedFormats = ['.xlsx', '.xls', '.csv'];
    this.xlsxLoaded = false;
  }

  async loadXLSX() {
    if (this.xlsxLoaded) return;

    try {
      // 动态导入SheetJS
      const XLSX = await import('xlsx');
      this.XLSX = XLSX;
      this.xlsxLoaded = true;
      console.log('✅ [Excel] SheetJS 加载成功');
    } catch (error) {
      console.warn('⚠️ [Excel] SheetJS 加载失败:', error);
      throw new Error('Excel处理引擎加载失败');
    }
  }

  async process(file, options = {}) {
    try {
      await this.loadXLSX();

      const arrayBuffer = await file.arrayBuffer();
      const workbook = this.XLSX.read(arrayBuffer, { type: 'array' });
      
      let fullText = '';
      const sheetNames = workbook.SheetNames;

      console.log(`📊 [Excel] 开始处理Excel，共 ${sheetNames.length} 个工作表`);

      sheetNames.forEach((sheetName, index) => {
        const worksheet = workbook.Sheets[sheetName];
        const csvText = this.XLSX.utils.sheet_to_csv(worksheet);
        
        if (csvText.trim()) {
          fullText += `\n\n=== 工作表: ${sheetName} ===\n${csvText}`;
        }
      });

      return {
        success: true,
        text: fullText.trim(),
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          totalSheets: sheetNames.length,
          sheetNames: sheetNames,
          processingTime: Date.now(),
          extractionMethod: 'SheetJS'
        }
      };
    } catch (error) {
      console.error('❌ [Excel] Excel处理失败:', error);
      throw new Error(`Excel处理失败: ${error.message}`);
    }
  }
}

/**
 * 📝 Word文档处理器（使用mammoth.js）
 */
class WordProcessor extends BaseDocumentProcessor {
  constructor() {
    super();
    this.supportedFormats = ['.docx'];
    this.mammothLoaded = false;
  }

  async loadMammoth() {
    if (this.mammothLoaded) return;

    try {
      // 动态导入mammoth.js
      const mammoth = await import('mammoth');
      this.mammoth = mammoth;
      this.mammothLoaded = true;
      console.log('✅ [Word] Mammoth.js 加载成功');
    } catch (error) {
      console.warn('⚠️ [Word] Mammoth.js 加载失败:', error);
      throw new Error('Word处理引擎加载失败');
    }
  }

  async process(file, options = {}) {
    try {
      await this.loadMammoth();

      const arrayBuffer = await file.arrayBuffer();

      if (!arrayBuffer || arrayBuffer.byteLength === 0) {
        throw new Error('文件为空或无法读取');
      }

      console.log(`📝 [Word] 开始处理Word文档，大小: ${arrayBuffer.byteLength} 字节`);

      const result = await this.mammoth.extractRawText({ arrayBuffer });

      // 安全地处理提取的文本
      let extractedText = '';
      if (result && result.value && typeof result.value === 'string') {
        extractedText = result.value.trim();
      }

      if (!extractedText || extractedText.length === 0) {
        throw new Error('Word文档中未找到可提取的文本内容');
      }

      console.log(`📝 [Word] Word文档处理完成，提取文本: ${extractedText.length} 字符`);

      return {
        success: true,
        text: extractedText,
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          warnings: result.messages || [],
          processingTime: Date.now(),
          extractionMethod: 'Mammoth.js',
          textLength: extractedText.length
        }
      };
    } catch (error) {
      console.error('❌ [Word] Word处理失败:', error);

      let errorMessage = 'Word文档处理失败';
      if (error.message.includes('not a valid docx')) {
        errorMessage = 'Word文档格式无效，请确保是.docx格式';
      } else if (error.message.includes('corrupted')) {
        errorMessage = 'Word文档已损坏或格式不正确';
      } else {
        errorMessage = `Word文档处理失败: ${error.message}`;
      }

      throw new Error(errorMessage);
    }
  }
}

/**
 * 🔧 文档处理器管理器
 */
class DocumentProcessorManager {
  constructor() {
    this.processors = [
      new TextProcessor(),
      new ImageOCRProcessor(),
      new PDFProcessor(),
      new ExcelProcessor(),
      new WordProcessor()
    ];
  }

  /**
   * 获取支持的文件格式
   */
  getSupportedFormats() {
    const allFormats = this.processors.flatMap(p => p.supportedFormats);
    return {
      text: ['.txt', '.md', '.csv', '.json', '.xml', '.html', '.css', '.js', '.ts'],
      images: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
      pdf: ['.pdf'],
      excel: ['.xlsx', '.xls'],
      word: ['.docx'],
      all: [...new Set(allFormats)]
    };
  }

  /**
   * 查找合适的处理器
   */
  findProcessor(fileName) {
    return this.processors.find(processor => processor.supports(fileName));
  }

  /**
   * 处理单个文档
   */
  async processDocument(file, options = {}) {
    const processor = this.findProcessor(file.name);
    
    if (!processor) {
      throw new Error(`不支持的文件格式: ${file.name}`);
    }

    console.log(`📄 [DocumentProcessor] 使用 ${processor.constructor.name} 处理文件: ${file.name}`);
    
    const startTime = Date.now();
    try {
      const result = await processor.process(file, options);
      const processingTime = Date.now() - startTime;
      
      return {
        ...result,
        metadata: {
          ...result.metadata,
          processingTime,
          processor: processor.constructor.name
        }
      };
    } catch (error) {
      console.error(`❌ [DocumentProcessor] 处理失败: ${file.name}`, error);
      throw error;
    }
  }

  /**
   * 批量处理文档
   */
  async processDocuments(files, options = {}) {
    const results = [];
    const errors = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      try {
        const result = await this.processDocument(file, options);
        results.push({
          filePath: file.name,
          success: true,
          result
        });
      } catch (error) {
        errors.push({
          filePath: file.name,
          success: false,
          error: error.message
        });
      }

      // 触发进度回调
      if (options.onProgress) {
        options.onProgress({
          current: i + 1,
          total: files.length,
          currentFile: file.name
        });
      }
    }

    return {
      results,
      errors,
      totalProcessed: results.length,
      totalErrors: errors.length
    };
  }
}

// 创建全局实例
export const documentProcessorManager = new DocumentProcessorManager();

// 导出处理器类
export {
  BaseDocumentProcessor,
  TextProcessor,
  ImageOCRProcessor,
  PDFProcessor,
  ExcelProcessor,
  WordProcessor,
  DocumentProcessorManager
};
