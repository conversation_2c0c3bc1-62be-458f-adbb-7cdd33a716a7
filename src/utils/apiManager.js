import axios from 'axios';

/**
 * API管理器 - 负责所有AI模型的API调用
 */
export class APIManager {
  constructor() {
    this.activeRequests = new Map(); // 跟踪活跃的请求
    this.requestTimeout = 180000; // 🔥 优化：3分钟超时（180秒）
  }

  /**
   * 测试模型API连通性
   */
  async testModelConnection(model) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒测试超时

    try {
      const response = await axios.post(
        `${model.baseUrl}/chat/completions`,
        {
          model: model.name,
          messages: [{ role: 'user', content: 'Hello' }],
          max_tokens: 10,
          stream: false
        },
        {
          headers: {
            'Authorization': `Bearer ${model.apiKey}`,
            'Content-Type': 'application/json'
          },
          signal: controller.signal,
          timeout: 5000
        }
      );

      clearTimeout(timeoutId);
      return {
        success: true,
        message: '连接成功',
        latency: response.headers['x-response-time'] || 'N/A'
      };
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        return { success: false, message: '连接超时' };
      }
      
      if (error.response) {
        return {
          success: false,
          message: `API错误: ${error.response.status} - ${error.response.data?.error?.message || '未知错误'}`
        };
      }
      
      return {
        success: false,
        message: `网络错误: ${error.message}`
      };
    }
  }

  /**
   * 发送聊天请求
   */
  async sendChatRequest(model, messages, options = {}) {
    const requestId = `${model.id}_${Date.now()}`;
    // 🔥 修复：优先使用外部传入的AbortController
    const controller = options.abortController || new AbortController();

    console.log('🌐 [APIManager] sendChatRequest 开始');
    console.log('🌐 [APIManager] 请求ID:', requestId);
    console.log('🌐 [APIManager] 流式模式:', !!options.stream);
    console.log('🌐 [APIManager] 模型信息:', {
      name: model.name,
      baseUrl: model.baseUrl,
      hasApiKey: !!model.apiKey,
      supportThinking: model.supportThinking
    });



    // 记录活跃请求
    this.activeRequests.set(requestId, {
      controller,
      model: model.name,
      startTime: Date.now()
    });

    // 设置超时
    const timeoutId = setTimeout(() => {
      console.log('⏰ [APIManager] 请求超时，取消请求:', requestId);
      controller.abort();
      this.activeRequests.delete(requestId);
    }, this.requestTimeout);

    try {
      // 构建请求消息
      console.log('🔧 [APIManager] 构建请求消息...');
      const requestMessages = this.buildRequestMessages(messages, model, options);
      console.log('🔧 [APIManager] 请求消息数量:', requestMessages.length);

      // 构建请求体
      const requestBody = {
        model: model.name,
        messages: requestMessages,
        max_tokens: options.maxTokens || model.maxTokens || 8192, // 🎯 使用模型配置的maxTokens，默认8K
        temperature: options.temperature || 0.7,
        stream: options.stream || false
      };

      // 如果模型支持thinking，添加相关参数
      if (model.supportThinking && options.enableThinking) {
        requestBody.thinking = true;
        console.log('🧠 [APIManager] 启用thinking模式');
      }

      console.log('🔧 [APIManager] 请求体:', {
        model: requestBody.model,
        messageCount: requestBody.messages.length,
        maxTokens: requestBody.max_tokens,
        temperature: requestBody.temperature,
        thinking: requestBody.thinking,
        stream: requestBody.stream
      });

      const apiUrl = `${model.baseUrl}/chat/completions`;
      console.log('📡 [APIManager] 发送HTTP请求到:', apiUrl);

      if (options.stream) {
        // 流式响应处理
        return await this.handleStreamResponse(apiUrl, requestBody, controller, requestId, model, options);
      } else {
        // 非流式响应处理（原有逻辑）
        return await this.handleNormalResponse(apiUrl, requestBody, controller, requestId, model, timeoutId);
      }

    } catch (error) {
      console.error('❌ [APIManager] 请求失败:', error);
      console.error('❌ [APIManager] 错误详情:', {
        name: error.name,
        message: error.message,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText
      });

      clearTimeout(timeoutId);



      this.activeRequests.delete(requestId);

      if (error.name === 'AbortError') {
        console.log('🚫 [APIManager] 请求被取消');
        return {
          success: false,
          error: '请求已取消',
          requestId
        };
      }

      const formattedError = this.formatError(error);
      console.error('❌ [APIManager] 格式化错误:', formattedError);

      return {
        success: false,
        error: formattedError,
        requestId
      };
    }
  }

  /**
   * 处理流式响应
   */
  async handleStreamResponse(apiUrl, requestBody, controller, requestId, model, options) {
    console.log('🌊 [APIManager] 开始流式响应处理');

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${model.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody),
      signal: controller.signal
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    let fullContent = '';
    let fullThinking = '';
    let buffer = '';
    let finishReason = null; // 🔥 新增：记录结束原因
    let isStreamComplete = false; // 🔥 新增：标记流是否正常完成
    let actualUsage = null; // 🔥 新增：记录实际的token使用情况

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          console.log('🌊 [APIManager] 流式响应完成');
          isStreamComplete = true;
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留最后一行（可能不完整）

        for (const line of lines) {
          if (line.trim() === '') continue;
          if (line.trim() === 'data: [DONE]') {
            // 🔥 新增：检测到正常结束标志
            isStreamComplete = true;
            continue;
          }

          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.slice(6);
              const data = JSON.parse(jsonStr);

              // 🔥 新增：捕获实际的token使用情况
              if (data.usage) {
                actualUsage = data.usage;
                console.log('📊 [APIManager] 检测到实际token使用:', actualUsage);
              }

              if (data.choices && data.choices[0]) {
                const delta = data.choices[0].delta;
                const choice = data.choices[0];

                // 🔥 新增：记录结束原因
                if (choice.finish_reason) {
                  finishReason = choice.finish_reason;
                  console.log('🏁 [APIManager] 检测到结束原因:', finishReason);
                }

                if (delta.content) {
                  fullContent += delta.content;
                  // 调用流式回调
                  if (options.onStreamChunk) {
                    options.onStreamChunk({
                      content: delta.content,
                      fullContent: fullContent,
                      thinking: fullThinking,
                      isComplete: false
                    });
                  }
                }

                if (delta.thinking) {
                  fullThinking += delta.thinking;
                }
              }
            } catch (parseError) {
              console.warn('⚠️ [APIManager] 解析流式数据失败:', parseError);
            }
          }
        }
      }

      // 🔥 修复：更合理的成功判断逻辑
      // 只要有内容返回且流式响应完成，就算成功
      const hasContent = fullContent.length > 0;
      const isSuccess = isStreamComplete && hasContent;

      console.log('📊 [APIManager] 成功判断:', {
        isStreamComplete,
        hasContent,
        contentLength: fullContent.length,
        finishReason,
        isSuccess
      });

      this.activeRequests.delete(requestId);

      // 最终回调
      if (options.onStreamChunk) {
        options.onStreamChunk({
          content: '',
          fullContent: fullContent,
          thinking: fullThinking,
          isComplete: true
        });
      }

      // 🔥 修复：根据实际完成状态返回结果
      console.log('✅ [APIManager] 流式响应处理完成');
      console.log('📊 [APIManager] 成功状态:', isSuccess, '结束原因:', finishReason, '内容长度:', fullContent.length);

      if (isSuccess) {
        // 🔥 修复：使用与性能记录相同的token计算逻辑
        let finalUsage;

        if (actualUsage && actualUsage.total_tokens > 0) {
          finalUsage = actualUsage;
        } else {
          const estimatedResponseTokens = this.estimateTokensAccurate(fullContent);
          const estimatedRequestTokens = this.estimateRequestTokens(requestBody.messages || []);
          finalUsage = {
            prompt_tokens: estimatedRequestTokens,
            completion_tokens: estimatedResponseTokens,
            total_tokens: estimatedRequestTokens + estimatedResponseTokens
          };
        }

        return {
          success: true,
          data: {
            content: fullContent,
            thinking: fullThinking,
            usage: finalUsage,
            finishReason: finishReason || 'stop'
          },
          requestId
        };
      } else {
        // 失败：返回错误
        const errorMessage = !isStreamComplete ? '流式响应未完成' :
                           finishReason ? `响应异常结束: ${finishReason}` : '响应未正常结束';

        return {
          success: false,
          error: errorMessage,
          data: {
            content: fullContent, // 即使失败也返回已接收的内容
            thinking: fullThinking,
            finishReason: finishReason
          },
          requestId
        };
      }

    } catch (streamError) {
      console.error('❌ [APIManager] 流式响应处理异常:', streamError);
      this.activeRequests.delete(requestId);
      throw streamError; // 重新抛出异常
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * 处理普通响应
   */
  async handleNormalResponse(apiUrl, requestBody, controller, requestId, model, timeoutId) {
    const response = await axios.post(
      apiUrl,
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${model.apiKey}`,
          'Content-Type': 'application/json'
        },
        signal: controller.signal,
        timeout: this.requestTimeout
      }
    );

    console.log('✅ [APIManager] HTTP请求成功');
    console.log('✅ [APIManager] 响应状态:', response.status);
    console.log('✅ [APIManager] 响应数据结构:', {
      hasChoices: !!response.data.choices,
      choicesLength: response.data.choices?.length,
      hasUsage: !!response.data.usage
    });

    clearTimeout(timeoutId);
    this.activeRequests.delete(requestId);

    // 处理响应
    console.log('🔄 [APIManager] 处理响应数据...');
    const result = this.processResponse(response.data, model);
    console.log('✅ [APIManager] 响应处理完成，内容长度:', result.content?.length || 0);

    return {
      success: true,
      data: result,
      requestId
    };
  }

  /**
   * 估算请求token数量
   */
  estimateRequestTokens(messages) {
    // 🔥 修复：直接传递完整文本而不是字符数
    const fullText = messages.map(msg => msg.content || '').join(' ');
    const tokens = this.estimateTokensAccurate(fullText);
    console.log('📊 [APIManager] 请求Token估算:', {
      消息数: messages.length,
      总字符数: fullText.length,
      估算Token: tokens
    });
    return tokens;
  }

  /**
   * 🔥 新增：更准确的token估算方法
   * 考虑中文字符和英文字符的不同token消耗
   */
  estimateTokensAccurate(text) {
    if (typeof text === 'number') {
      // 如果传入的是字符数，使用简单估算
      return Math.ceil(text / 4);
    }

    if (!text || typeof text !== 'string') {
      return 0;
    }

    // 🔥 改进：区分中文和英文字符进行估算
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishChars = text.length - chineseChars;

    // 中文字符：约1.5个token per字符
    // 英文字符：约0.25个token per字符（4个字符1个token）
    const estimatedTokens = Math.ceil(chineseChars * 1.5 + englishChars * 0.25);

    console.log('📊 [APIManager] Token估算详情:', {
      总字符数: text.length,
      中文字符: chineseChars,
      英文字符: englishChars,
      估算Token: estimatedTokens
    });

    return estimatedTokens;
  }

  /**
   * 构建请求消息
   */
  buildRequestMessages(messages, model, options) {
    console.log('🔧 [APIManager] buildRequestMessages 开始');
    console.log('🔧 [APIManager] 输入消息数量:', messages.length);
    console.log('🔧 [APIManager] 当前模型:', model.name);
    console.log('🔧 [APIManager] 搭档提示词:', options.partnerPrompt ? '已设置' : '未设置');
    console.log('🔧 [APIManager] 互动提示词:', options.interactionPrompt ? '已设置' : '未设置');

    // 🔥 修复：添加安全的内容预览提取函数
    const getContentPreview = (content, maxLength = 30) => {
      if (typeof content === 'string') {
        return content.substring(0, maxLength);
      } else if (Array.isArray(content)) {
        // 对于Vision格式的消息，提取文本部分
        const textItem = content.find(item => item.type === 'text');
        const textContent = textItem?.text || '';
        const imageCount = content.filter(item => item.type === 'image_url').length;
        const preview = textContent.substring(0, maxLength);
        return imageCount > 0 ? `${preview} [+${imageCount}张图片]` : preview;
      } else {
        return '[非文本内容]';
      }
    };

    // 🔥 新增：精确的Vision消息检测函数
    const hasVisionContent = (content) => {
      if (!Array.isArray(content)) return false;

      return content.some(item =>
        item &&
        typeof item === 'object' &&
        item.type === 'image_url' &&
        item.image_url &&
        typeof item.image_url.url === 'string' &&
        item.image_url.url.startsWith('data:image/')
      );
    };

    const requestMessages = [];

    // 添加系统提示（搭档角色）
    if (options.partnerPrompt) {
      requestMessages.push({
        role: 'system',
        content: options.partnerPrompt
      });
      console.log('🔧 [APIManager] 添加系统提示词');
    }

    // 🔥 关键修复：重新设计消息格式，避免AI模型混淆
    // 将所有历史消息整合为一个清晰的对话历史，作为用户消息传递

    if (messages.length === 0) {
      console.log('⚠️ [APIManager] 没有历史消息');
      return requestMessages;
    }

    // 🔥 修复：按时间顺序处理所有消息，确保正确的对话顺序
    // 🌐 保留搜索结果等重要的消息
    const allRelevantMessages = messages.filter(msg => {
      // 排除一般的系统消息，但保留搜索相关的消息
      if (msg.sender === 'system') {
        return msg.type === 'search_result' ||
               msg.type === 'search_context' ||
               msg.isContextOnly === true;
      }
      // 包含用户消息和AI消息，以及搜索上下文消息
      return msg.sender === 'user' ||
             msg.sender !== 'system' ||
             msg.type === 'search_context';
    });

    console.log(`🔧 [APIManager] 处理消息总数: ${allRelevantMessages.length}条`);

    // 🌐 检查搜索相关消息
    const searchMessages = allRelevantMessages.filter(msg =>
      msg.type === 'search_result' || msg.type === 'search_context'
    );
    if (searchMessages.length > 0) {
      console.log(`🌐 [APIManager] 包含 ${searchMessages.length} 条搜索相关消息`);
      searchMessages.forEach((msg, index) => {
        console.log(`🌐 [APIManager] 搜索消息 ${index + 1}: ${msg.type}, 内容长度: ${msg.content?.length || 0}`);
      });
    } else {
      console.log(`⚠️ [APIManager] 未发现搜索相关消息`);
    }

    if (allRelevantMessages.length === 0) {
      console.log('⚠️ [APIManager] 没有相关消息');
      return requestMessages;
    }

    // 按时间戳排序所有消息，确保正确的时间顺序
    const sortedMessages = allRelevantMessages.sort((a, b) =>
      new Date(a.timestamp) - new Date(b.timestamp)
    );

    console.log('🔧 [APIManager] 消息时间顺序验证:', sortedMessages.map((msg, index) => ({
      index: index + 1,
      sender: msg.sender,
      timestamp: msg.timestamp,
      contentPreview: getContentPreview(msg.content, 30) + '...'
    })));

    // 构建完整的对话历史上下文
    let conversationHistory = '';

    // 🔥 修复：如果有多条消息，根据是否包含Vision消息选择处理方式
    if (sortedMessages.length > 1) {
      // 🔥 修复：使用精确的Vision消息检测
      const hasVisionMessages = sortedMessages.some(msg => hasVisionContent(msg.content));

      if (hasVisionMessages && model.supportVision) {
        // 🔥 新方案：对于包含Vision的对话，构建标准的messages数组
        console.log('🖼️ [APIManager] 检测到Vision消息，构建标准messages数组');

        // 添加系统消息（如果有互动提示词）
        if (options.interactionPrompt) {
          requestMessages.push({
            role: 'system',
            content: `你是 ${model.name}。${options.interactionPrompt}`
          });
        } else {
          requestMessages.push({
            role: 'system',
            content: `你是 ${model.name}，请基于对话历史继续参与这个对话。`
          });
        }

        // 🔥 修复：添加所有历史消息，保持Vision格式
        sortedMessages.forEach((msg) => {
          if (msg.sender === 'user') {
            requestMessages.push({
              role: 'user',
              content: msg.content // 直接传递原始content，无论是字符串还是Vision数组
            });
          } else {
            requestMessages.push({
              role: 'assistant',
              content: msg.content
            });
          }
        });

        console.log(`✅ [APIManager] 构建Vision兼容的messages数组，共${requestMessages.length}条消息`);

      } else {
        // 🔥 原方案：对于纯文本对话，使用文本合并方式
        console.log('📝 [APIManager] 纯文本对话，使用文本合并方式');

        conversationHistory = '\n\n=== 对话历史（按时间顺序）===\n';

        sortedMessages.forEach((msg) => {
          if (msg.sender === 'user') {
            if (hasVisionContent(msg.content)) {
              // 🔥 修复：对于不支持Vision的模型，提取文本部分
              const textContent = msg.content.find(item => item.type === 'text')?.text || '';
              conversationHistory += `用户: ${textContent}\n\n`;

              // 🔥 修复：不在API请求内容中添加图片描述文本，仅用于调试日志
              const imageCount = msg.content.filter(item => item.type === 'image_url').length;
              if (imageCount > 0) {
                console.log(`🖼️ [APIManager] 用户消息包含 ${imageCount} 张图片，但当前模型不支持Vision（仅调试信息）`);
              }
            } else {
              conversationHistory += `用户: ${msg.content}\n\n`;
            }
          } else {
            conversationHistory += `${msg.sender}: ${msg.content}\n\n`;
          }
        });

        conversationHistory += '=== 对话历史结束 ===\n\n';

        // 整合智能互动提示词
        if (options.interactionPrompt) {
          conversationHistory += `【互动引导】${options.interactionPrompt}\n\n`;
          console.log('🎭 [APIManager] 添加互动提示词:', options.interactionPrompt.substring(0, 50) + '...');
        }

        conversationHistory += `现在请你作为 ${model.name} 继续参与这个对话。请基于上述对话历史，给出你的回复。`;

        // 将完整的对话历史作为用户消息传递
        requestMessages.push({
          role: 'user',
          content: conversationHistory
        });

        const aiMessageCount = sortedMessages.filter(msg => msg.sender !== 'user').length;
        console.log(`✅ [APIManager] 构建多模型对话上下文，包含 ${aiMessageCount} 条AI回复`);
        console.log(`🔧 [APIManager] 对话历史预览:`, conversationHistory.substring(0, 200) + '...');
      }

    } else {
      // 🔥 修复：如果只有一条消息，支持Vision格式直接传递
      const singleMessage = sortedMessages[0];

      if (hasVisionContent(singleMessage.content) && model.supportVision) {
        // 🔥 修复：对于支持Vision的模型，直接传递结构化内容
        requestMessages.push({
          role: 'user',
          content: singleMessage.content
        });

        const textContent = singleMessage.content.find(item => item.type === 'text')?.text || '';
        const imageCount = singleMessage.content.filter(item => item.type === 'image_url').length;
        const safeTextPreview = textContent ? textContent.substring(0, 50) : '[无文本内容]';
        console.log(`✅ [APIManager] 添加Vision消息: ${safeTextPreview}... (包含${imageCount}张图片)`);
      } else if (hasVisionContent(singleMessage.content) && !model.supportVision) {
        // 🔥 修复：对于不支持Vision的模型，只提取文本部分
        const textContent = singleMessage.content.find(item => item.type === 'text')?.text || '';
        requestMessages.push({
          role: 'user',
          content: textContent
        });

        const imageCount = singleMessage.content.filter(item => item.type === 'image_url').length;
        console.log(`✅ [APIManager] 添加文本消息（忽略${imageCount}张图片）: ${textContent.substring(0, 50)}...`);
      } else {
        // 普通文本消息
        requestMessages.push({
          role: 'user',
          content: singleMessage.content
        });

        console.log(`✅ [APIManager] 添加单条消息: ${getContentPreview(singleMessage.content, 50)}...`);
      }
    }

    console.log('🔧 [APIManager] 构建完成，最终消息数量:', requestMessages.length);
    console.log('🔧 [APIManager] 最终消息结构:', requestMessages.map(m => ({
      role: m.role,
      contentLength: m.content?.length || 0
    })));

    return requestMessages;
  }

  /**
   * 处理API响应
   */
  processResponse(responseData, model) {
    console.log('🔄 [APIManager] processResponse 开始');
    console.log('🔄 [APIManager] 响应数据结构:', {
      hasChoices: !!responseData.choices,
      choicesLength: responseData.choices?.length,
      hasUsage: !!responseData.usage
    });

    const choice = responseData.choices?.[0];
    if (!choice) {
      console.error('❌ [APIManager] 无效的API响应: 没有choices');
      throw new Error('无效的API响应');
    }

    console.log('🔄 [APIManager] 第一个choice结构:', {
      hasMessage: !!choice.message,
      hasContent: !!choice.message?.content,
      hasThinking: !!choice.message?.thinking,
      finishReason: choice.finish_reason
    });

    const result = {
      content: choice.message?.content || '',
      thinking: null,
      usage: responseData.usage,
      finishReason: choice.finish_reason
    };

    // 处理thinking内容
    if (model.supportThinking && choice.message?.thinking) {
      result.thinking = choice.message.thinking;
      console.log('🧠 [APIManager] 提取thinking内容，长度:', result.thinking.length);
    }

    console.log('✅ [APIManager] 响应处理完成:', {
      contentLength: result.content.length,
      hasThinking: !!result.thinking,
      hasUsage: !!result.usage,
      finishReason: result.finishReason
    });

    return result;
  }

  /**
   * 格式化错误信息
   */
  formatError(error) {
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error?.message || '未知API错误';
      
      switch (status) {
        case 401:
          return 'API密钥无效或已过期';
        case 403:
          return '访问被拒绝，请检查API权限';
        case 429:
          return 'API调用频率超限，请稍后重试';
        case 500:
          return 'API服务器内部错误';
        case 503:
          return 'API服务暂时不可用';
        default:
          return `API错误 (${status}): ${message}`;
      }
    }

    if (error.code === 'ECONNABORTED') {
      return '请求超时，请检查网络连接';
    }

    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return '无法连接到API服务器，请检查网络和URL';
    }

    return `网络错误: ${error.message}`;
  }

  /**
   * 取消指定请求
   */
  cancelRequest(requestId) {
    const request = this.activeRequests.get(requestId);
    if (request) {
      request.controller.abort();
      this.activeRequests.delete(requestId);
      return true;
    }
    return false;
  }

  /**
   * 取消所有活跃请求
   */
  cancelAllRequests() {
    const canceledCount = this.activeRequests.size;
    
    this.activeRequests.forEach((request) => {
      request.controller.abort();
    });
    
    this.activeRequests.clear();
    return canceledCount;
  }

  /**
   * 获取活跃请求状态
   */
  getActiveRequests() {
    const requests = [];
    this.activeRequests.forEach((request, requestId) => {
      requests.push({
        id: requestId,
        model: request.model,
        duration: Date.now() - request.startTime
      });
    });
    return requests;
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.cancelAllRequests();
  }
}
