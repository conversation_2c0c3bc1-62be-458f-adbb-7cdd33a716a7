import { v4 as uuidv4 } from 'uuid';

/**
 * 🚀 性能数据管理器
 * 独立的性能数据收集和存储系统，与消息显示状态解耦
 */
export class PerformanceDataManager {
  constructor() {
    this.apiCallRecords = new Map(); // API调用记录
    this.modelMetrics = new Map(); // 模型性能指标
    this.isInitialized = false;
    
    // 存储键名
    this.STORAGE_KEYS = {
      API_RECORDS: 'jdc_api_call_records',
      MODEL_METRICS: 'jdc_model_metrics',
      DAILY_STATS: 'jdc_daily_stats'
    };
  }

  /**
   * 初始化性能数据管理器
   */
  async initialize() {
    try {
      await this.loadFromStorage();
      this.isInitialized = true;
      console.log('✅ [性能数据管理器] 初始化完成');
    } catch (error) {
      console.error('❌ [性能数据管理器] 初始化失败:', error);
      this.isInitialized = true; // 即使失败也标记为已初始化，使用默认数据
    }
  }

  /**
   * 从本地存储加载数据
   */
  async loadFromStorage() {
    try {
      // 加载API调用记录
      const apiRecordsData = localStorage.getItem(this.STORAGE_KEYS.API_RECORDS);
      if (apiRecordsData) {
        const records = JSON.parse(apiRecordsData);
        this.apiCallRecords = new Map(records);
      }

      // 加载模型性能指标
      const metricsData = localStorage.getItem(this.STORAGE_KEYS.MODEL_METRICS);
      if (metricsData) {
        const metrics = JSON.parse(metricsData);
        this.modelMetrics = new Map(metrics);
      }

      console.log('📊 [性能数据管理器] 数据加载完成');
      console.log('📊 API调用记录数量:', this.apiCallRecords.size);
      console.log('📊 模型指标数量:', this.modelMetrics.size);
    } catch (error) {
      console.error('❌ [性能数据管理器] 加载数据失败:', error);
    }
  }

  /**
   * 保存数据到本地存储
   */
  async saveToStorage() {
    try {
      // 保存API调用记录（只保留最近1000条）
      const apiRecordsArray = Array.from(this.apiCallRecords.entries());
      const recentRecords = apiRecordsArray.slice(-1000);
      localStorage.setItem(this.STORAGE_KEYS.API_RECORDS, JSON.stringify(recentRecords));

      // 保存模型性能指标
      const metricsArray = Array.from(this.modelMetrics.entries());
      localStorage.setItem(this.STORAGE_KEYS.MODEL_METRICS, JSON.stringify(metricsArray));

      console.log('💾 [性能数据管理器] 数据保存完成');
    } catch (error) {
      console.error('❌ [性能数据管理器] 保存数据失败:', error);
    }
  }

  /**
   * 记录API调用开始
   */
  recordAPICallStart(modelName, sessionId, requestData = {}) {
    const callId = uuidv4();
    const record = {
      id: callId,
      modelName,
      sessionId,
      startTime: Date.now(),
      endTime: null,
      duration: null,
      success: null,
      error: null,
      requestTokens: requestData.estimatedTokens || 0,
      responseTokens: 0,
      totalTokens: 0,
      requestData: {
        messageCount: requestData.messageCount || 0,
        maxTokens: requestData.maxTokens || 0,
        temperature: requestData.temperature || 0.7,
        stream: requestData.stream || false
      },
      responseData: null,
      timestamp: new Date().toISOString(),
      date: new Date().toDateString() // 用于按日期统计
    };

    this.apiCallRecords.set(callId, record);
    console.log('📝 [性能数据管理器] 记录API调用开始:', callId, modelName);
    return callId;
  }

  /**
   * 记录API调用完成
   */
  recordAPICallEnd(callId, success, responseData = {}, error = null) {
    const record = this.apiCallRecords.get(callId);
    if (!record) {
      console.warn('⚠️ [性能数据管理器] 未找到API调用记录:', callId);
      return;
    }

    const endTime = Date.now();
    record.endTime = endTime;
    record.duration = endTime - record.startTime;
    record.success = success;
    record.error = error;

    // 记录token使用情况
    if (responseData.usage) {
      record.requestTokens = responseData.usage.prompt_tokens || 0;
      record.responseTokens = responseData.usage.completion_tokens || 0;
      record.totalTokens = responseData.usage.total_tokens || 0;
    }

    record.responseData = {
      hasChoices: !!responseData.choices,
      choicesLength: responseData.choices?.length || 0,
      finishReason: responseData.choices?.[0]?.finish_reason,
      usage: responseData.usage
    };

    this.apiCallRecords.set(callId, record);
    
    // 更新模型性能指标
    this.updateModelMetrics(record);
    
    // 异步保存数据
    setTimeout(() => this.saveToStorage(), 100);

    console.log('✅ [性能数据管理器] 记录API调用完成:', callId, {
      duration: record.duration,
      success: record.success,
      tokens: record.totalTokens
    });
  }

  /**
   * 更新模型性能指标
   */
  updateModelMetrics(record) {
    const modelName = record.modelName;
    let metrics = this.modelMetrics.get(modelName);

    if (!metrics) {
      metrics = {
        modelName,
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        totalDuration: 0,
        totalTokens: 0,
        totalRequestTokens: 0,
        totalResponseTokens: 0,
        firstUsed: record.timestamp,
        lastUsed: record.timestamp,
        dailyStats: new Map() // 按日期统计
      };
    }

    // 更新总体统计
    metrics.totalRequests++;
    if (record.success) {
      metrics.successfulRequests++;
    } else {
      metrics.failedRequests++;
    }
    metrics.totalDuration += record.duration;
    metrics.totalTokens += record.totalTokens;
    metrics.totalRequestTokens += record.requestTokens;
    metrics.totalResponseTokens += record.responseTokens;
    metrics.lastUsed = record.timestamp;

    // 更新每日统计
    const dateKey = record.date;
    let dailyStat = metrics.dailyStats.get(dateKey);
    if (!dailyStat) {
      dailyStat = {
        date: dateKey,
        requests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        totalDuration: 0,
        totalTokens: 0,
        avgResponseTime: 0,
        successRate: 0
      };
    }

    dailyStat.requests++;
    if (record.success) {
      dailyStat.successfulRequests++;
    } else {
      dailyStat.failedRequests++;
    }
    dailyStat.totalDuration += record.duration;
    dailyStat.totalTokens += record.totalTokens;
    dailyStat.avgResponseTime = dailyStat.totalDuration / dailyStat.requests;
    dailyStat.successRate = (dailyStat.successfulRequests / dailyStat.requests) * 100;

    metrics.dailyStats.set(dateKey, dailyStat);
    this.modelMetrics.set(modelName, metrics);
  }

  /**
   * 获取模型性能统计
   */
  getModelPerformanceStats(modelName, timeRange = 'all') {
    const metrics = this.modelMetrics.get(modelName);
    if (!metrics) {
      return null;
    }

    const now = Date.now();
    let filteredRecords = Array.from(this.apiCallRecords.values())
      .filter(record => record.modelName === modelName);

    // 根据时间范围过滤
    if (timeRange !== 'all') {
      const timeRanges = {
        'today': 24 * 60 * 60 * 1000,
        'week': 7 * 24 * 60 * 60 * 1000,
        'month': 30 * 24 * 60 * 60 * 1000
      };

      const rangeMs = timeRanges[timeRange];
      if (rangeMs) {
        const cutoffTime = now - rangeMs;
        filteredRecords = filteredRecords.filter(record => record.startTime >= cutoffTime);
      }
    }

    if (filteredRecords.length === 0) {
      return {
        modelName,
        totalRequests: 0,
        successRate: 0,
        avgResponseTime: 0,
        totalTokens: 0,
        avgTokensPerRequest: 0,
        errorCount: 0,
        lastUsed: null,
        status: 'idle'
      };
    }

    const successfulRecords = filteredRecords.filter(r => r.success);
    const totalDuration = filteredRecords.reduce((sum, r) => sum + (r.duration || 0), 0);
    const totalTokens = filteredRecords.reduce((sum, r) => sum + (r.totalTokens || 0), 0);

    return {
      modelName,
      totalRequests: filteredRecords.length,
      successRate: (successfulRecords.length / filteredRecords.length) * 100,
      avgResponseTime: Math.round(totalDuration / filteredRecords.length),
      totalTokens,
      avgTokensPerRequest: Math.round(totalTokens / filteredRecords.length),
      requestTokens: filteredRecords.reduce((sum, r) => sum + (r.requestTokens || 0), 0),
      responseTokens: filteredRecords.reduce((sum, r) => sum + (r.responseTokens || 0), 0),
      errorCount: filteredRecords.length - successfulRecords.length,
      lastUsed: filteredRecords[filteredRecords.length - 1]?.timestamp,
      status: totalDuration / filteredRecords.length > 5000 ? 'slow' : 
              totalDuration / filteredRecords.length > 2000 ? 'normal' : 'fast'
    };
  }

  /**
   * 获取所有模型的性能统计
   */
  getAllModelStats(timeRange = 'all') {
    const modelNames = Array.from(this.modelMetrics.keys());
    const stats = {};

    modelNames.forEach(modelName => {
      stats[modelName] = this.getModelPerformanceStats(modelName, timeRange);
    });

    return stats;
  }

  /**
   * 获取全局统计信息
   */
  getGlobalStats(timeRange = 'all') {
    const now = Date.now();
    let allRecords = Array.from(this.apiCallRecords.values());

    // 根据时间范围过滤
    if (timeRange !== 'all') {
      const timeRanges = {
        'today': 24 * 60 * 60 * 1000,
        'week': 7 * 24 * 60 * 60 * 1000,
        'month': 30 * 24 * 60 * 60 * 1000
      };

      const rangeMs = timeRanges[timeRange];
      if (rangeMs) {
        const cutoffTime = now - rangeMs;
        allRecords = allRecords.filter(record => record.startTime >= cutoffTime);
      }
    }

    if (allRecords.length === 0) {
      return {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        successRate: 0,
        avgResponseTime: 0,
        totalTokens: 0,
        totalCost: 0,
        activeModels: 0
      };
    }

    const successfulRecords = allRecords.filter(r => r.success);
    const totalDuration = allRecords.reduce((sum, r) => sum + (r.duration || 0), 0);
    const totalTokens = allRecords.reduce((sum, r) => sum + (r.totalTokens || 0), 0);
    const activeModels = new Set(allRecords.map(r => r.modelName)).size;

    return {
      totalRequests: allRecords.length,
      successfulRequests: successfulRecords.length,
      failedRequests: allRecords.length - successfulRecords.length,
      successRate: (successfulRecords.length / allRecords.length) * 100,
      avgResponseTime: Math.round(totalDuration / allRecords.length),
      totalTokens,
      requestTokens: allRecords.reduce((sum, r) => sum + (r.requestTokens || 0), 0),
      responseTokens: allRecords.reduce((sum, r) => sum + (r.responseTokens || 0), 0),
      totalCost: this.estimateCost(totalTokens), // 估算成本
      activeModels
    };
  }

  /**
   * 估算token成本（简单估算）
   */
  estimateCost(totalTokens) {
    // 简单估算：每1K token约$0.002
    return (totalTokens / 1000) * 0.002;
  }

  /**
   * 清理过期数据
   */
  cleanupOldData(daysToKeep = 30) {
    const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
    let removedCount = 0;

    for (const [id, record] of this.apiCallRecords.entries()) {
      if (record.startTime < cutoffTime) {
        this.apiCallRecords.delete(id);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      console.log(`🧹 [性能数据管理器] 清理了 ${removedCount} 条过期记录`);
      this.saveToStorage();
    }
  }
}

// 创建全局实例
export const performanceDataManager = new PerformanceDataManager();
