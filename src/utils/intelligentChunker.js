/**
 * 智能分块服务
 * 使用Chat模型进行智能文档分块，保持内容结构完整性
 */
export class IntelligentChunker {
  constructor(apiManager) {
    this.apiManager = apiManager;
  }

  /**
   * 智能分块文档
   * @param {string} content - 文档内容
   * @param {Object} model - Chat模型配置
   * @param {Object} options - 分块选项
   * @returns {Promise<Array>} 分块结果
   */
  async intelligentChunk(content, model, options = {}) {
    const {
      maxChunkSize = 800,
      minChunkSize = 200,
      overlap = 50,
      preserveStructure = true,
      hierarchical = false
    } = options;

    // 🚀 新增：智能检测是否需要分层分块
    const shouldUseHierarchical = this.shouldUseHierarchicalChunking(content);
    const finalHierarchical = hierarchical || shouldUseHierarchical;

    console.log('🧠 [IntelligentChunker] 内容分析结果:', {
      contentType: this.detectContentType(content),
      shouldUseHierarchical: shouldUseHierarchical,
      finalHierarchical: finalHierarchical,
      contentLength: content.length
    });

    try {
      console.log('🧠 [IntelligentChunker] 开始智能分块');
      console.log('🧠 [IntelligentChunker] 内容长度:', content.length);

      // 如果内容较短，直接返回
      if (content.length <= maxChunkSize) {
        return [{
          text: content,
          index: 0,
          type: 'single',
          metadata: {
            originalLength: content.length,
            chunkLength: content.length
          }
        }];
      }

      // 构建智能分块提示词
      const chunkingPrompt = this.buildChunkingPrompt(content, {
        maxChunkSize,
        minChunkSize,
        preserveStructure,
        hierarchical: finalHierarchical
      });

      // 🚀 修复：智能处理模型的maxTokens配置
      let modelMaxTokens = this.getValidMaxTokens(model);

      // 为分块任务预留足够的tokens，使用合理的比例
      const chunkingMaxTokens = this.calculateChunkingTokens(modelMaxTokens, content.length);

      console.log(`🧠 [IntelligentChunker] Token配置:`, {
        modelName: model.name || 'Unknown',
        modelMaxTokens: modelMaxTokens,
        chunkingMaxTokens: chunkingMaxTokens,
        contentLength: content.length
      });

      // 调用Chat模型进行分块
      const response = await this.apiManager.sendChatRequest(
        model,
        [{
          sender: 'user',
          content: chunkingPrompt,
          timestamp: new Date().toISOString()
        }],
        {
          maxTokens: chunkingMaxTokens,
          temperature: 0.1, // 低温度确保一致性
          enableThinking: false
        }
      );

      if (!response.success || !response.data.content) {
        const errorMsg = response.error || '未知错误';
        console.error('❌ [IntelligentChunker] Chat模型请求失败:', errorMsg);
        throw new Error(`Chat模型分块失败: ${errorMsg}`);
      }

      console.log('✅ [IntelligentChunker] AI响应成功，开始解析分块结果...');

      // 解析分块结果
      const chunks = this.parseChunkingResult(response.data.content, content);

      console.log('✅ [IntelligentChunker] 智能分块完成，共', chunks.length, '个分块');

      // 🚀 增强：验证分块质量
      if (chunks.length === 0) {
        throw new Error('AI返回了空的分块结果');
      }

      if (chunks.length === 1 && content.length > maxChunkSize * 2) {
        console.warn('⚠️ [IntelligentChunker] 大文档只生成了1个分块，可能需要调整');
      }

      return chunks;

    } catch (error) {
      console.error('❌ [IntelligentChunker] 智能分块失败:', error.message);
      console.warn('🔄 [IntelligentChunker] 降级到传统分块方法');

      // 🚀 增强：降级到简单分块
      try {
        return this.fallbackChunking(content, options);
      } catch (fallbackError) {
        console.error('❌ [IntelligentChunker] 降级分块也失败:', fallbackError);
        // 最后的保险：返回单个分块
        return [{
          text: content,
          index: 0,
          type: 'fallback',
          title: '完整文档',
          metadata: {
            originalLength: content.length,
            chunkLength: content.length,
            contentType: 'text',
            intelligentChunking: false,
            fallback: true,
            error: error.message
          }
        }];
      }
    }
  }

  /**
   * 构建分块提示词
   */
  buildChunkingPrompt(content, options) {
    const { maxChunkSize, minChunkSize, preserveStructure, hierarchical } = options;

    return `你是一个专业的文档分块专家。请对以下文档内容进行智能分块，确保内容结构的完整性。

## 🎯 核心要求

### 1. **结构完整性（最重要）**：
- ❌ 绝对不要在句子中间分割
- ✅ 保持代码块、表格、列表的完整性
- ✅ 保持SQL语句、JSON对象等结构化内容的完整性
- ✅ 保留所有标点符号、换行符和格式
- ✅ 保持markdown格式完整

### 2. **分块规则**：
- 每个分块大小控制在 ${minChunkSize}-${maxChunkSize} 字符之间
- 优先在段落、章节、逻辑单元边界分割
- 对于代码、SQL、配置文件等结构化内容，按逻辑单元分割
- 保持上下文的连贯性
- 每个分块必须包含有意义的完整内容

### 3. **内容类型特定处理规则**：

**SQL数据库内容**：
- 每个表的完整定义作为一个分块，保持CREATE TABLE语句的完整性
- 父分块：完整的表定义（包括所有字段）
- 子分块：表的基本信息、字段组、索引等逻辑部分

**编程代码**：
- 保持函数、类、方法的完整性
- 父分块：完整的类或模块
- 子分块：单个函数或方法

**API文档**：
- 每个端点的完整文档作为一个分块
- 父分块：API概述和端点组
- 子分块：单个端点的详细信息

**技术文档**：
- 按章节和子章节进行分块
- 保持代码示例和图表的完整性

**业务文档**：
- 按流程步骤和业务规则分块
- 保持相关步骤的逻辑连贯性

**配置文件**：
- 按配置节和相关设置分块
- 保持配置项的完整性

**结构化数据（JSON/XML/YAML）**：
- 按对象和数组的逻辑结构分块
- 保持嵌套关系的完整性

**表格数据**：
- 保持表头和相关行的完整性
- 按逻辑分组进行分块

**混合内容**：
- 识别不同内容类型并分别处理
- 保持各部分的内在逻辑

**重要**：无论什么内容类型，都必须保持原始格式，包括换行符、缩进和特殊字符

## 📋 输出格式要求

**必须严格按照以下JSON格式输出，不要添加任何解释文字：**

\`\`\`json
{
  "chunks": [
    {
      "index": 0,
      "text": "第一个分块的完整内容（必须包含原始格式和标点）",
      "type": "paragraph|code|table|list|sql|mixed",
      "title": "分块的简短描述",
      "metadata": {
        "contentType": "text|sql|json|code|markdown",
        "language": "sql|javascript|python|markdown等",
        "hasStructure": true,
        "importance": "high|medium|low"
      }
    },
    {
      "index": 1,
      "text": "第二个分块的完整内容",
      "type": "paragraph|code|table|list|sql|mixed",
      "title": "分块的简短描述",
      "metadata": {
        "contentType": "text|sql|json|code|markdown",
        "language": "sql|javascript|python|markdown等",
        "hasStructure": false,
        "importance": "high|medium|low"
      }
    }
  ]
}
\`\`\`

## 📄 要分块的文档内容：

${content}

## 🚀 开始分块

请仔细分析上述内容，按照要求进行智能分块。确保每个分块都包含完整、有意义的内容，并严格按照JSON格式输出：`;
  }

  /**
   * 解析分块结果
   */
  parseChunkingResult(response, originalContent) {
    try {
      console.log('🔍 [IntelligentChunker] 开始解析AI响应...');
      console.log('🔍 [IntelligentChunker] 响应长度:', response.length);

      // 🚀 增强：更灵活的JSON提取
      let jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);

      if (!jsonMatch) {
        // 尝试其他可能的格式
        jsonMatch = response.match(/```\s*([\s\S]*?)\s*```/);
        if (!jsonMatch) {
          // 尝试直接解析整个响应
          try {
            const directParse = JSON.parse(response.trim());
            if (directParse.chunks) {
              jsonMatch = [null, response.trim()];
            }
          } catch (e) {
            console.error('❌ [IntelligentChunker] 无法找到有效的JSON格式');
            console.error('❌ [IntelligentChunker] AI响应内容:', response.substring(0, 500));
            throw new Error('未找到JSON格式的分块结果，AI可能返回了不完整的响应');
          }
        }
      }

      console.log('✅ [IntelligentChunker] 找到JSON内容，长度:', jsonMatch[1].length);

      const result = JSON.parse(jsonMatch[1]);

      if (!result.chunks || !Array.isArray(result.chunks)) {
        console.error('❌ [IntelligentChunker] 分块结果格式错误:', result);
        throw new Error('分块结果格式错误：缺少chunks数组');
      }

      console.log(`🔍 [IntelligentChunker] 解析到 ${result.chunks.length} 个分块`);

      // 🚀 增强：更严格的验证和清理
      const validChunks = result.chunks
        .filter((chunk, index) => {
          if (!chunk.text || chunk.text.trim().length === 0) {
            console.warn(`⚠️ [IntelligentChunker] 分块 ${index} 内容为空，已跳过`);
            return false;
          }
          if (chunk.text.trim().length < 10) {
            console.warn(`⚠️ [IntelligentChunker] 分块 ${index} 内容过短 (${chunk.text.length} 字符)，已跳过`);
            return false;
          }
          return true;
        })
        .map((chunk, index) => ({
          text: chunk.text.trim(),
          index: index,
          type: chunk.type || 'mixed',
          title: chunk.title || `分块 ${index + 1}`,
          metadata: {
            originalLength: originalContent.length,
            chunkLength: chunk.text.trim().length,
            contentType: chunk.metadata?.contentType || 'text',
            language: chunk.metadata?.language,
            hasStructure: chunk.metadata?.hasStructure || false,
            importance: chunk.metadata?.importance || 'medium',
            intelligentChunking: true
          }
        }));

      if (validChunks.length === 0) {
        throw new Error('所有分块都无效或为空，AI可能没有正确理解分块要求');
      }

      // 🚀 增强：验证分块完整性
      const totalChunkLength = validChunks.reduce((sum, chunk) => sum + chunk.text.length, 0);
      const coverageRatio = totalChunkLength / originalContent.length;

      console.log(`📊 [IntelligentChunker] 分块统计:`);
      console.log(`📊 [IntelligentChunker] - 有效分块数: ${validChunks.length}`);
      console.log(`📊 [IntelligentChunker] - 覆盖率: ${(coverageRatio * 100).toFixed(1)}%`);
      console.log(`📊 [IntelligentChunker] - 原始长度: ${originalContent.length}`);
      console.log(`📊 [IntelligentChunker] - 分块总长度: ${totalChunkLength}`);

      if (coverageRatio < 0.6) {
        console.warn('⚠️ [IntelligentChunker] 分块覆盖率过低，可能存在内容丢失');
        throw new Error(`分块覆盖率过低 (${(coverageRatio * 100).toFixed(1)}%)，建议使用传统分块`);
      }

      return validChunks;

    } catch (error) {
      console.error('❌ [IntelligentChunker] 解析分块结果失败:', error);
      console.error('❌ [IntelligentChunker] 原始响应:', response.substring(0, 1000));
      throw error;
    }
  }

  /**
   * 降级分块方法（当智能分块失败时使用）
   */
  fallbackChunking(content, options) {
    const { maxChunkSize = 800, overlap = 50 } = options;

    console.log('🔄 [IntelligentChunker] 使用降级分块方法');
    console.log('🔄 [IntelligentChunker] 内容长度:', content.length, '最大分块:', maxChunkSize);

    const chunks = [];
    const sentences = this.splitIntoSentences(content);

    console.log('🔄 [IntelligentChunker] 分割为', sentences.length, '个句子/段落');

    let currentChunk = '';
    let chunkIndex = 0;

    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length > maxChunkSize && currentChunk.length > 0) {
        // 保存当前分块
        const chunkText = currentChunk.trim();
        if (chunkText.length > 0) {
          chunks.push({
            text: chunkText,
            index: chunkIndex++,
            type: 'fallback',
            title: `降级分块 ${chunkIndex}`,
            metadata: {
              originalLength: content.length,
              chunkLength: chunkText.length,
              contentType: this.detectContentType(chunkText),
              intelligentChunking: false,
              fallback: true
            }
          });
        }

        // 开始新分块，保留重叠
        const overlapText = this.getOverlapText(currentChunk, overlap);
        currentChunk = overlapText + sentence;
      } else {
        currentChunk += sentence;
      }
    }

    // 添加最后一个分块
    const finalChunkText = currentChunk.trim();
    if (finalChunkText.length > 0) {
      chunks.push({
        text: finalChunkText,
        index: chunkIndex,
        type: 'fallback',
        title: `降级分块 ${chunkIndex + 1}`,
        metadata: {
          originalLength: content.length,
          chunkLength: finalChunkText.length,
          contentType: this.detectContentType(finalChunkText),
          intelligentChunking: false,
          fallback: true
        }
      });
    }

    console.log('✅ [IntelligentChunker] 降级分块完成，共', chunks.length, '个分块');
    return chunks;
  }

  /**
   * 将文本分割为句子
   */
  splitIntoSentences(text) {
    // 改进的句子分割，保持结构化内容的完整性
    const sentences = [];
    const lines = text.split('\n');
    
    let currentSentence = '';
    let inCodeBlock = false;
    let inTable = false;
    
    for (const line of lines) {
      // 检测代码块
      if (line.trim().startsWith('```')) {
        inCodeBlock = !inCodeBlock;
        currentSentence += line + '\n';
        continue;
      }
      
      // 检测表格
      if (line.includes('|') && !inCodeBlock) {
        inTable = true;
        currentSentence += line + '\n';
        continue;
      }
      
      if (inCodeBlock || inTable) {
        currentSentence += line + '\n';
        
        // 表格结束检测
        if (inTable && line.trim() === '') {
          inTable = false;
        }
      } else {
        // 普通文本处理
        if (line.trim() === '') {
          if (currentSentence.trim()) {
            sentences.push(currentSentence);
            currentSentence = '';
          }
        } else {
          currentSentence += line + '\n';
        }
      }
    }
    
    if (currentSentence.trim()) {
      sentences.push(currentSentence);
    }
    
    return sentences;
  }

  /**
   * 获取重叠文本
   */
  getOverlapText(text, overlapSize) {
    if (text.length <= overlapSize) {
      return text;
    }
    
    // 尝试在句子边界获取重叠
    const lastPart = text.slice(-overlapSize * 2);
    const sentences = lastPart.split(/[.!?。！？]/);
    
    if (sentences.length > 1) {
      return sentences[sentences.length - 2] + sentences[sentences.length - 1];
    }
    
    return text.slice(-overlapSize);
  }

  /**
   * 🚀 Enhanced: Intelligent hierarchical chunking detection for all content types
   */
  shouldUseHierarchicalChunking(content) {
    const contentType = this.detectContentType(content);
    const contentLength = content.length;
    const lines = content.split('\n');

    console.log(`🔍 [IntelligentChunker] 分析内容类型: ${contentType}, 长度: ${contentLength}`);

    // Content-specific hierarchical chunking rules
    switch (contentType) {
      case 'sql':
        return this.shouldUseSQLHierarchical(content);

      case 'code':
      case 'python':
      case 'javascript':
      case 'java':
      case 'cpp':
      case 'csharp':
        return this.shouldUseCodeHierarchical(content);

      case 'api':
        return this.shouldUseAPIHierarchical(content);

      case 'technical':
        return this.shouldUseTechnicalHierarchical(content);

      case 'business':
        return this.shouldUseBusinessHierarchical(content);

      case 'config':
        return this.shouldUseConfigHierarchical(content);

      case 'json':
      case 'xml':
      case 'yaml':
        return this.shouldUseStructuredHierarchical(content);

      case 'table':
        return this.shouldUseTableHierarchical(content);

      case 'mixed':
        return true; // Mixed content almost always benefits from hierarchical chunking

      default:
        return this.shouldUseGenericHierarchical(content, contentLength, lines);
    }
  }

  /**
   * 🚀 New: SQL-specific hierarchical chunking rules
   */
  shouldUseSQLHierarchical(content) {
    const tableCount = (content.match(/CREATE TABLE/gi) || []).length;
    const fieldCount = (content.match(/`\w+`.*COMMENT/gi) || []).length;

    if (tableCount > 1) {
      console.log('🔍 [IntelligentChunker] 多个表定义 → 分层分块');
      return true;
    }
    if (fieldCount > 8) {
      console.log('🔍 [IntelligentChunker] 复杂表结构 → 分层分块');
      return true;
    }
    return false;
  }

  /**
   * 🚀 New: Code-specific hierarchical chunking rules
   */
  shouldUseCodeHierarchical(content) {
    const functionCount = (content.match(/function\s+\w+|def\s+\w+|public\s+\w+\s+\w+\s*\(/gi) || []).length;
    const classCount = (content.match(/class\s+\w+|interface\s+\w+/gi) || []).length;

    if (functionCount > 3 || classCount > 1) {
      console.log('🔍 [IntelligentChunker] 多个函数/类 → 分层分块');
      return true;
    }
    return false;
  }

  /**
   * 🚀 New: API documentation hierarchical chunking rules
   */
  shouldUseAPIHierarchical(content) {
    const endpointCount = (content.match(/GET|POST|PUT|DELETE|PATCH/gi) || []).length;
    const sectionCount = (content.match(/#{1,3}\s/g) || []).length;

    if (endpointCount > 2 || sectionCount > 5) {
      console.log('🔍 [IntelligentChunker] 多个API端点/章节 → 分层分块');
      return true;
    }
    return false;
  }

  /**
   * 🚀 New: Technical documentation hierarchical chunking rules
   */
  shouldUseTechnicalHierarchical(content) {
    const headingCount = (content.match(/#{1,4}\s/g) || []).length;
    const diagramCount = (content.match(/```\w*\s*\n[\s\S]*?```/g) || []).length;

    if (headingCount > 4 || diagramCount > 2) {
      console.log('🔍 [IntelligentChunker] 复杂技术文档 → 分层分块');
      return true;
    }
    return false;
  }

  /**
   * 🚀 New: Business documentation hierarchical chunking rules
   */
  shouldUseBusinessHierarchical(content) {
    const processSteps = (content.match(/\d+\.\s|\*\s|-\s/g) || []).length;
    const sectionCount = (content.match(/#{1,3}\s/g) || []).length;

    if (processSteps > 10 || sectionCount > 3) {
      console.log('🔍 [IntelligentChunker] 复杂业务流程 → 分层分块');
      return true;
    }
    return false;
  }

  /**
   * 🚀 New: Configuration file hierarchical chunking rules
   */
  shouldUseConfigHierarchical(content) {
    const sectionCount = (content.match(/\[[\w\s]+\]|\w+:/g) || []).length;

    if (sectionCount > 5) {
      console.log('🔍 [IntelligentChunker] 复杂配置文件 → 分层分块');
      return true;
    }
    return false;
  }

  /**
   * 🚀 New: Structured data hierarchical chunking rules
   */
  shouldUseStructuredHierarchical(content) {
    const nestingLevel = this.calculateNestingLevel(content);
    const objectCount = (content.match(/\{|\[/g) || []).length;

    if (nestingLevel > 3 || objectCount > 5) {
      console.log('🔍 [IntelligentChunker] 复杂结构化数据 → 分层分块');
      return true;
    }
    return false;
  }

  /**
   * 🚀 New: Table hierarchical chunking rules
   */
  shouldUseTableHierarchical(content) {
    const rowCount = content.split('\n').filter(line => line.includes('|')).length;

    if (rowCount > 15) {
      console.log('🔍 [IntelligentChunker] 大型表格 → 分层分块');
      return true;
    }
    return false;
  }

  /**
   * 🚀 New: Generic hierarchical chunking rules
   */
  shouldUseGenericHierarchical(content, contentLength, lines) {
    if (contentLength > 5000) {
      const paragraphCount = content.split(/\n\s*\n/).length;
      if (paragraphCount > 10) {
        console.log('🔍 [IntelligentChunker] 长文档多段落 → 分层分块');
        return true;
      }
    }
    return false;
  }

  /**
   * 🚀 New: Calculate nesting level for structured data
   */
  calculateNestingLevel(content) {
    let maxLevel = 0;
    let currentLevel = 0;

    for (const char of content) {
      if (char === '{' || char === '[') {
        currentLevel++;
        maxLevel = Math.max(maxLevel, currentLevel);
      } else if (char === '}' || char === ']') {
        currentLevel--;
      }
    }

    return maxLevel;
  }

  /**
   * 🚀 新增：获取有效的maxTokens值
   */
  getValidMaxTokens(model) {
    let maxTokens = 4000; // 安全的默认值

    // 尝试从模型配置中获取maxTokens
    const candidates = [
      model.maxTokens,
      model.max_tokens,
      model.contextLength,
      model.context_length
    ];

    for (const candidate of candidates) {
      if (typeof candidate === 'number' && candidate > 0 && candidate <= 1000000) {
        maxTokens = candidate;
        break;
      }
    }

    // 根据模型名称设置合理的默认值
    if (maxTokens === 4000 && model.name) {
      maxTokens = this.getDefaultMaxTokensByName(model.name);
    }

    // 确保返回整数且在合理范围内
    return Math.floor(Math.min(Math.max(maxTokens, 1000), 200000));
  }

  /**
   * 🚀 新增：根据模型名称获取默认maxTokens
   */
  getDefaultMaxTokensByName(modelName) {
    const name = modelName.toLowerCase();

    if (name.includes('gpt-4o') || name.includes('claude-3.5')) {
      return 128000;
    } else if (name.includes('gpt-4') || name.includes('claude-3')) {
      return 8000;
    } else if (name.includes('gpt-3.5')) {
      return 4000;
    } else if (name.includes('gemini-pro')) {
      return 32000;
    } else if (name.includes('llama') && name.includes('70b')) {
      return 8000;
    } else if (name.includes('qwen') || name.includes('baichuan')) {
      return 8000;
    }

    return 4000;
  }

  /**
   * 🚀 新增：计算分块任务的合适token数
   */
  calculateChunkingTokens(modelMaxTokens, contentLength) {
    // 根据内容长度动态调整token使用比例
    let tokenRatio = 0.8; // 默认使用80%

    if (contentLength > 10000) {
      tokenRatio = 0.9; // 长文档使用90%
    } else if (contentLength < 2000) {
      tokenRatio = 0.6; // 短文档使用60%即可
    }

    const calculatedTokens = Math.floor(modelMaxTokens * tokenRatio);

    // 确保至少有4000 tokens，最多不超过100000 tokens
    return Math.min(Math.max(calculatedTokens, 4000), 100000);
  }

  /**
   * 🚀 Enhanced: Comprehensive content type detection for diverse document types
   */
  detectContentType(text) {
    // 安全检查：确保text是字符串
    if (!text || typeof text !== 'string') {
      console.warn('⚠️ [IntelligentChunker] detectContentType: 无效的文本内容', typeof text);
      return 'unknown';
    }

    const trimmed = text.trim();
    if (!trimmed) {
      console.warn('⚠️ [IntelligentChunker] detectContentType: 空文本内容');
      return 'empty';
    }

    const lowerText = trimmed.toLowerCase();
    const lines = trimmed.split('\n');

    // Analyze content characteristics
    const analysis = this.analyzeContentCharacteristics(trimmed, lowerText, lines);

    // SQL Database Content
    if (analysis.sqlScore > 0.7) {
      return 'sql';
    }

    // Programming Code
    if (analysis.codeScore > 0.6) {
      return this.detectSpecificCodeType(trimmed, lowerText);
    }

    // Configuration Files
    if (analysis.configScore > 0.6) {
      return 'config';
    }

    // API Documentation
    if (analysis.apiScore > 0.6) {
      return 'api';
    }

    // Technical Documentation
    if (analysis.techDocScore > 0.5) {
      return 'technical';
    }

    // Business Documentation
    if (analysis.businessScore > 0.5) {
      return 'business';
    }

    // Structured Data (JSON, XML, YAML)
    if (analysis.structuredScore > 0.7) {
      return analysis.structuredType;
    }

    // Tables (Markdown, CSV, etc.)
    if (analysis.tableScore > 0.6) {
      return 'table';
    }

    // Mixed content (contains multiple types)
    if (analysis.mixedScore > 0.5) {
      return 'mixed';
    }

    // Default to text
    return 'text';
  }

  /**
   * 🚀 New: Analyze content characteristics to determine type
   */
  analyzeContentCharacteristics(text, lowerText, lines) {
    const analysis = {
      sqlScore: 0,
      codeScore: 0,
      configScore: 0,
      apiScore: 0,
      techDocScore: 0,
      businessScore: 0,
      structuredScore: 0,
      structuredType: 'json',
      tableScore: 0,
      mixedScore: 0
    };

    // SQL indicators
    const sqlKeywords = ['create table', 'alter table', 'select', 'insert into', 'update', 'delete from', 'primary key', 'foreign key', 'index', 'constraint'];
    analysis.sqlScore = this.calculateKeywordScore(lowerText, sqlKeywords) * 1.2;

    // Code indicators
    const codeKeywords = ['function', 'class', 'import', 'export', 'def', 'public', 'private', 'const', 'let', 'var', 'return', 'if', 'else', 'for', 'while'];
    analysis.codeScore = this.calculateKeywordScore(lowerText, codeKeywords);

    // Configuration indicators
    const configKeywords = ['config', 'settings', 'properties', 'environment', 'env', 'port', 'host', 'database', 'connection'];
    analysis.configScore = this.calculateKeywordScore(lowerText, configKeywords);

    // API documentation indicators
    const apiKeywords = ['endpoint', 'api', 'rest', 'http', 'get', 'post', 'put', 'delete', 'request', 'response', 'parameter', 'header'];
    analysis.apiScore = this.calculateKeywordScore(lowerText, apiKeywords);

    // Technical documentation indicators
    const techKeywords = ['architecture', 'system', 'component', 'module', 'service', 'deployment', 'infrastructure', 'protocol'];
    analysis.techDocScore = this.calculateKeywordScore(lowerText, techKeywords);

    // Business documentation indicators
    const businessKeywords = ['requirement', 'process', 'workflow', 'policy', 'procedure', 'business', 'user', 'customer', 'stakeholder'];
    analysis.businessScore = this.calculateKeywordScore(lowerText, businessKeywords);

    // Structured data detection
    if (text.trim().startsWith('{') && text.trim().endsWith('}')) {
      analysis.structuredScore = 0.9;
      analysis.structuredType = 'json';
    } else if (text.trim().startsWith('[') && text.trim().endsWith(']')) {
      analysis.structuredScore = 0.9;
      analysis.structuredType = 'json';
    } else if (text.includes('<?xml') || (text.includes('<') && text.includes('>'))) {
      analysis.structuredScore = 0.8;
      analysis.structuredType = 'xml';
    } else if (text.includes('---') && text.includes(':')) {
      analysis.structuredScore = 0.7;
      analysis.structuredType = 'yaml';
    }

    // Table detection
    const tableLines = lines.filter(line => line.includes('|')).length;
    if (tableLines > 2) {
      analysis.tableScore = Math.min(tableLines / lines.length * 2, 1);
    }

    // Mixed content detection
    const nonZeroScores = Object.values(analysis).filter(score => typeof score === 'number' && score > 0.3).length;
    if (nonZeroScores > 2) {
      analysis.mixedScore = 0.6;
    }

    return analysis;
  }

  /**
   * 🚀 New: Calculate keyword score for content type detection
   */
  calculateKeywordScore(text, keywords) {
    let score = 0;
    const textLength = text.length;

    keywords.forEach(keyword => {
      const regex = new RegExp(keyword, 'gi');
      const matches = text.match(regex);
      if (matches) {
        score += matches.length * keyword.length / textLength;
      }
    });

    return Math.min(score * 10, 1); // Normalize to 0-1
  }

  /**
   * 🚀 New: Detect specific programming language
   */
  detectSpecificCodeType(text, lowerText) {
    if (lowerText.includes('def ') || lowerText.includes('import ') || lowerText.includes('from ')) {
      return 'python';
    }
    if (lowerText.includes('function') || lowerText.includes('const ') || lowerText.includes('let ')) {
      return 'javascript';
    }
    if (lowerText.includes('public class') || lowerText.includes('private ') || lowerText.includes('package ')) {
      return 'java';
    }
    if (lowerText.includes('#include') || lowerText.includes('int main')) {
      return 'cpp';
    }
    if (lowerText.includes('using ') || lowerText.includes('namespace ')) {
      return 'csharp';
    }
    return 'code';
  }
}
