body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  overflow: hidden;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 消息气泡样式 */
.message-bubble {
  max-width: 80%;
  margin: 8px 0;
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
  position: relative;
}

.message-bubble.user {
  background: #1890ff;
  color: white;
  margin-left: auto;
  margin-right: 16px;
}

.message-bubble.assistant {
  background: #f6f6f6;
  color: #333;
  margin-left: 16px;
  margin-right: auto;
  border: 1px solid #e8e8e8;
}

.message-bubble.system {
  background: #fff7e6;
  color: #d46b08;
  margin: 8px auto;
  text-align: center;
  border: 1px solid #ffd591;
  font-size: 12px;
  max-width: 60%;
}

/* 思考过程样式 */
.thinking-content {
  background: #f0f2f5;
  border-left: 4px solid #1890ff;
  padding: 8px 12px;
  margin: 8px 0;
  border-radius: 4px;
  font-style: italic;
  color: #666;
  font-size: 12px;
}

/* 加载动画 */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% {
    color: rgba(0,0,0,0);
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  40% {
    color: black;
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  60% {
    text-shadow:
      .25em 0 0 black,
      .5em 0 0 rgba(0,0,0,0);
  }
  80%, 100% {
    text-shadow:
      .25em 0 0 black,
      .5em 0 0 black;
  }
}

/* 🔥 新增：打字机光标动画 */
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* 🔥 新增：流式响应优化样式 */
.ai-message-content {
  line-height: 1.6;
  word-break: break-word;
}

.ai-message-content.streaming {
  position: relative;
}

/* 🔥 新增：高质量聊天室样式 */
.chat-message-container {
  transition: all 0.3s ease;
}

.chat-message-container:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 🔥 新增：模型标签样式优化 */
.model-tag {
  background: linear-gradient(45deg, #1890ff, #722ed1);
  color: white;
  border: none;
  font-weight: 500;
}

.vision-tag {
  background: linear-gradient(45deg, #722ed1, #eb2f96);
  color: white;
  border: none;
}

.streaming-tag {
  background: linear-gradient(45deg, #fa8c16, #faad14);
  color: white;
  border: none;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
