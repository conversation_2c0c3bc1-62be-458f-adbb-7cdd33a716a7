/* 🚀 MessageItem组件优化样式 - 减少内联样式计算 */

/* 🚀 优化的背景动画 - 减少GPU负担 */
.shimmer-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
  animation: shimmer 4s ease-in-out infinite;
  transform: translateX(-100%);
  pointer-events: none;
  border-radius: 20px;
  will-change: transform; /* 🚀 GPU加速优化 */
}

.message-shimmer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  animation: shimmer 6s ease-in-out infinite;
  transform: translateX(-100%);
  pointer-events: none;
  border-radius: inherit;
  will-change: transform; /* 🚀 GPU加速优化 */
}

/* 🚀 滚动时禁用动画以提升性能 */
.scrolling .shimmer-bg,
.scrolling .message-shimmer {
  animation-play-state: paused;
}

/* 🚀 性能优化的动画关键帧 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 头像样式 */
.ai-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.ai-avatar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
  transform: translateX(-100%);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #8a2be2 0%, #00d4ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.3);
}

/* 发送者信息 */
.sender-info {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #00d4ff;
  font-weight: 500;
  text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

.streaming-indicator {
  color: rgba(0, 212, 255, 0.8);
  animation: pulse 1.5s ease-in-out infinite;
  font-style: italic;
}

/* 思考过程样式 */
.thinking-section {
  margin-top: 16px;
  padding: 16px 20px;
  background: rgba(26, 31, 46, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(138, 43, 226, 0.3);
  box-shadow: 0 4px 20px rgba(138, 43, 226, 0.1);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.thinking-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(138, 43, 226, 0.05) 50%, transparent 70%);
  animation: shimmer 4s ease-in-out infinite;
  transform: translateX(-100%);
  pointer-events: none;
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #8a2be2;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 0 8px rgba(138, 43, 226, 0.3);
  position: relative;
  z-index: 1;
}

.thinking-content {
  position: relative;
  z-index: 1;
}

/* 调度信息样式 */
.scheduling-info {
  margin-top: 12px;
  font-size: 12px;
  color: rgba(0, 212, 255, 0.8);
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(0, 212, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.2);
  position: relative;
  z-index: 1;
}

/* 时间戳样式 */
.timestamp {
  margin-top: 12px;
  font-size: 11px;
  color: rgba(136, 146, 176, 0.7);
  font-family: Monaco, Menlo, monospace;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
}

/* 性能优化：减少重绘和回流 */
.ai-avatar,
.user-avatar,
.thinking-section,
.scheduling-info {
  will-change: transform;
  transform: translateZ(0);
}

/* 🎯 复制按钮样式 */
.copy-button {
  user-select: none;
  white-space: nowrap;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.copy-button:hover {
  background: rgba(0, 212, 255, 0.2) !important;
  border-color: #00d4ff !important;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.4) !important;
  transform: translateY(-1px) !important;
}

.copy-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 0 5px rgba(0, 212, 255, 0.3) !important;
}

/* 🎯 消息底部复制按钮区域 */
.message-copy-area {
  opacity: 0;
  transform: translateY(-5px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.message-copy-area.visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* 🎯 复制按钮通用样式 */
.copy-btn-base {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  user-select: none;
  backdrop-filter: blur(10px);
}

/* 🎯 原文复制按钮 */
.copy-original-btn {
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  color: #00d4ff;
}

.copy-original-btn:hover {
  background: rgba(0, 212, 255, 0.2);
  border-color: #00d4ff;
  box-shadow: 0 0 8px rgba(0, 212, 255, 0.3);
  transform: translateY(-1px);
}

/* 🎯 纯文本复制按钮 */
.copy-text-btn {
  background: rgba(138, 43, 226, 0.1);
  border: 1px solid rgba(138, 43, 226, 0.3);
  color: #8a2be2;
}

.copy-text-btn:hover {
  background: rgba(138, 43, 226, 0.2);
  border-color: #8a2be2;
  box-shadow: 0 0 8px rgba(138, 43, 226, 0.3);
  transform: translateY(-1px);
}

/* AI消息复制按钮 */
.ai-message-copy {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 代码复制按钮 */
.code-copy-btn {
  transition: all 0.2s ease !important;
}

.code-copy-btn:hover {
  background: rgba(0, 212, 255, 0.2) !important;
  transform: scale(1.05) !important;
}

/* 思考过程复制按钮 */
.thinking-copy {
  transition: all 0.3s ease !important;
}

.thinking-copy:hover {
  background: rgba(138, 43, 226, 0.2) !important;
  border-color: #8a2be2 !important;
  box-shadow: 0 0 10px rgba(138, 43, 226, 0.4) !important;
}

/* 🚀 新增：思考内容复制按钮样式 */
.thinking-copy-btn {
  font-size: 10px !important;
  padding: 2px 6px !important;
  background: rgba(138, 43, 226, 0.1) !important;
  border: 1px solid rgba(138, 43, 226, 0.3) !important;
  color: #8a2be2 !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  user-select: none !important;
  display: flex !important;
  align-items: center !important;
  gap: 3px !important;
}

.thinking-copy-btn:hover {
  background: rgba(138, 43, 226, 0.2) !important;
  border-color: #8a2be2 !important;
  box-shadow: 0 0 8px rgba(138, 43, 226, 0.3) !important;
  transform: scale(1.05) !important;
}

/* 复制成功状态 */
.copy-button.copied {
  background: rgba(82, 196, 26, 0.1) !important;
  border-color: rgba(82, 196, 26, 0.5) !important;
  color: #52c41a !important;
}

/* 复制按钮图标动画 */
.copy-button .anticon {
  transition: transform 0.2s ease;
}

.copy-button:hover .anticon {
  transform: scale(1.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .ai-avatar {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .user-avatar {
    width: 28px;
    height: 28px;
    margin-left: 8px;
  }

  .thinking-section {
    padding: 12px 16px;
  }

  /* 移动端复制按钮优化 */
  .copy-button {
    font-size: 11px !important;
    padding: 3px 6px !important;
  }

  .ai-message-copy {
    opacity: 1 !important; /* 移动端始终显示 */
    transform: translateY(0) !important;
  }
}
