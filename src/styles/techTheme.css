/* 🎨 JDCAIChat 科技感主题样式 */

/* 全局动画关键帧 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 科技感滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(26, 31, 46, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #00e5ff 0%, #9c27b0 100%);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
}

/* Antd组件样式覆盖 */
.ant-menu-dark {
  background: transparent !important;
}

.ant-menu-dark .ant-menu-item {
  background: transparent !important;
  border-radius: 8px !important;
  margin: 4px 8px !important;
  transition: all 0.3s ease !important;
  border: 1px solid transparent !important;
}

.ant-menu-dark .ant-menu-item:hover {
  background: rgba(0, 212, 255, 0.1) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2) !important;
  transform: translateX(4px) !important;
}

.ant-menu-dark .ant-menu-item-selected {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(138, 43, 226, 0.2) 100%) !important;
  border: 1px solid rgba(0, 212, 255, 0.5) !important;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3) !important;
  color: #00d4ff !important;
}

.ant-menu-dark .ant-menu-item-selected::after {
  display: none !important;
}

/* 按钮样式增强 */
.ant-btn-primary {
  background: linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%) !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #00e5ff 0%, #9c27b0 100%) !important;
  box-shadow: 0 6px 20px rgba(0, 212, 255, 0.5) !important;
  transform: translateY(-2px) !important;
}

/* 输入框样式增强 */
.ant-input {
  background: rgba(26, 31, 46, 0.8) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  color: #ffffff !important;
  transition: all 0.3s ease !important;
}

.ant-input:focus {
  border-color: #00d4ff !important;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3) !important;
}

.ant-input::placeholder {
  color: rgba(136, 146, 176, 0.6) !important;
}

/* 卡片样式增强 */
.ant-card {
  background: rgba(26, 31, 46, 0.8) !important;
  border: 1px solid rgba(0, 212, 255, 0.2) !important;
  box-shadow: 0 8px 32px rgba(0, 212, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  transition: all 0.3s ease !important;
}

.ant-card:hover {
  border-color: rgba(0, 212, 255, 0.4) !important;
  box-shadow: 0 12px 40px rgba(0, 212, 255, 0.2) !important;
  transform: translateY(-2px) !important;
}

/* 开关样式增强 */
.ant-switch-checked {
  background: linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%) !important;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5) !important;
}

/* 标签样式增强 */
.ant-tag {
  background: rgba(0, 212, 255, 0.1) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  color: #00d4ff !important;
}

/* 进度条样式增强 */
.ant-progress-bg {
  background: linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%) !important;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5) !important;
}

/* 消息气泡样式增强 */
.message-bubble {
  animation: slideIn 0.3s ease-out;
  transition: all 0.3s ease;
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.15);
}

/* 科技感装饰元素 */
.tech-border {
  position: relative;
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.tech-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
  transition: left 0.5s;
}

.tech-border:hover::before {
  left: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    width: 60px !important;
    min-width: 60px !important;
  }
  
  .ant-menu-item span {
    display: none !important;
  }
}

/* 加载动画 */
.loading-shimmer {
  background: linear-gradient(90deg, 
    rgba(26, 31, 46, 0.8) 25%, 
    rgba(0, 212, 255, 0.1) 50%, 
    rgba(26, 31, 46, 0.8) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* 文本发光效果 */
.glow-text {
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  animation: glow 2s ease-in-out infinite alternate;
}

/* 浮动动画 */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* 🎨 Markdown组件深色主题样式 */
.markdown-content {
  color: #ffffff !important;
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: #00d4ff !important;
  text-shadow: 0 0 8px rgba(0, 212, 255, 0.3) !important;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
  padding-bottom: 8px !important;
  margin-bottom: 16px !important;
}

.markdown-content p {
  color: #ffffff !important;
  margin-bottom: 12px !important;
}

.markdown-content a {
  color: #00d4ff !important;
  text-decoration: none !important;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

.markdown-content a:hover {
  color: #00e5ff !important;
  border-bottom-color: #00d4ff !important;
  text-shadow: 0 0 5px rgba(0, 212, 255, 0.5) !important;
}

.markdown-content code {
  background: rgba(0, 212, 255, 0.1) !important;
  color: #00d4ff !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 0.9em !important;
}

.markdown-content pre {
  background: rgba(26, 31, 46, 0.8) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  border-radius: 8px !important;
  padding: 16px !important;
  margin: 16px 0 !important;
  overflow-x: auto !important;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
}

.markdown-content pre code {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  color: #ffffff !important;
  font-size: 0.9em !important;
}

.markdown-content blockquote {
  border-left: 4px solid #00d4ff !important;
  background: rgba(0, 212, 255, 0.05) !important;
  padding: 12px 16px !important;
  margin: 16px 0 !important;
  border-radius: 0 8px 8px 0 !important;
  color: rgba(255, 255, 255, 0.9) !important;
  box-shadow: 0 2px 10px rgba(0, 212, 255, 0.1) !important;
}

.markdown-content ul,
.markdown-content ol {
  color: #ffffff !important;
  padding-left: 20px !important;
  margin-bottom: 16px !important;
}

.markdown-content li {
  color: #ffffff !important;
  margin-bottom: 6px !important;
}

.markdown-content li::marker {
  color: #00d4ff !important;
}

.markdown-content table {
  border-collapse: collapse !important;
  width: 100% !important;
  margin: 16px 0 !important;
  background: rgba(26, 31, 46, 0.6) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.1) !important;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  padding: 12px !important;
  text-align: left !important;
  color: #ffffff !important;
}

.markdown-content th {
  background: rgba(0, 212, 255, 0.2) !important;
  color: #00d4ff !important;
  font-weight: 600 !important;
  text-shadow: 0 0 5px rgba(0, 212, 255, 0.3) !important;
}

.markdown-content tr:nth-child(even) {
  background: rgba(0, 212, 255, 0.05) !important;
}

.markdown-content tr:hover {
  background: rgba(0, 212, 255, 0.1) !important;
}

.markdown-content hr {
  border: none !important;
  height: 2px !important;
  background: linear-gradient(90deg, transparent 0%, #00d4ff 50%, transparent 100%) !important;
  margin: 24px 0 !important;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3) !important;
}

.markdown-content strong,
.markdown-content b {
  color: #00d4ff !important;
  font-weight: 600 !important;
  text-shadow: 0 0 3px rgba(0, 212, 255, 0.3) !important;
}

.markdown-content em,
.markdown-content i {
  color: rgba(138, 43, 226, 0.9) !important;
  font-style: italic !important;
}

/* 思考过程样式 */
.thinking-content {
  background: rgba(26, 31, 46, 0.6) !important;
  border: 1px solid rgba(138, 43, 226, 0.3) !important;
  border-radius: 8px !important;
  padding: 12px !important;
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 0.9em !important;
  backdrop-filter: blur(10px) !important;
}

.thinking-content h1,
.thinking-content h2,
.thinking-content h3,
.thinking-content h4,
.thinking-content h5,
.thinking-content h6 {
  color: #8a2be2 !important;
  text-shadow: 0 0 5px rgba(138, 43, 226, 0.3) !important;
}

.thinking-content code {
  background: rgba(138, 43, 226, 0.1) !important;
  color: #8a2be2 !important;
  border-color: rgba(138, 43, 226, 0.3) !important;
}

/* 🚀 新增：思考组件专用样式 */
.thinking-section {
  animation: slideIn 0.3s ease-out;
}

.thinking-section:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(138, 43, 226, 0.15);
}

/* 思考组件标题栏悬浮效果 */
.thinking-section .thinking-header:hover {
  background: rgba(138, 43, 226, 0.1) !important;
}

/* 思考组件展开/折叠动画 */
.thinking-section .thinking-content-area {
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 思考组件图标动画 */
.thinking-section .thinking-toggle-icon {
  transition: transform 0.2s ease;
}

.thinking-section .thinking-toggle-icon.expanded {
  transform: rotate(0deg);
}

.thinking-section .thinking-toggle-icon.collapsed {
  transform: rotate(-90deg);
}

/* 代码高亮样式 */
.markdown-content .hljs {
  background: rgba(26, 31, 46, 0.8) !important;
  color: #ffffff !important;
}

.markdown-content .hljs-keyword {
  color: #00d4ff !important;
}

.markdown-content .hljs-string {
  color: #52c41a !important;
}

.markdown-content .hljs-number {
  color: #ffa500 !important;
}

.markdown-content .hljs-comment {
  color: rgba(136, 146, 176, 0.8) !important;
  font-style: italic !important;
}

.markdown-content .hljs-function {
  color: #8a2be2 !important;
}

.markdown-content .hljs-variable {
  color: #ffffff !important;
}

/* 🎨 Modal组件深色主题样式 */
.ant-modal .ant-modal-content {
  background: rgba(26, 31, 46, 0.95) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  box-shadow: 0 20px 60px rgba(0, 212, 255, 0.2) !important;
  backdrop-filter: blur(20px) !important;
}

.ant-modal .ant-modal-header {
  background: transparent !important;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3) !important;
}

.ant-modal .ant-modal-title {
  color: #00d4ff !important;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.3) !important;
}

.ant-modal .ant-modal-body {
  background: transparent !important;
  color: #ffffff !important;
}

.ant-modal .ant-modal-footer {
  background: transparent !important;
  border-top: 1px solid rgba(0, 212, 255, 0.3) !important;
}

.ant-modal .ant-modal-close {
  color: rgba(255, 255, 255, 0.7) !important;
}

.ant-modal .ant-modal-close:hover {
  color: #ff6b6b !important;
}

/* Select组件样式增强 */
.ant-select-dropdown {
  background: rgba(26, 31, 46, 0.95) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  box-shadow: 0 8px 32px rgba(0, 212, 255, 0.2) !important;
  backdrop-filter: blur(20px) !important;
}

.ant-select-item {
  color: #ffffff !important;
}

.ant-select-item-option-selected {
  background: rgba(0, 212, 255, 0.2) !important;
  color: #00d4ff !important;
}

.ant-select-item-option:hover {
  background: rgba(0, 212, 255, 0.1) !important;
}

/* Tooltip样式增强 */
.ant-tooltip .ant-tooltip-inner {
  background: rgba(26, 31, 46, 0.95) !important;
  color: #ffffff !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2) !important;
  backdrop-filter: blur(20px) !important;
}

.ant-tooltip .ant-tooltip-arrow::before {
  background: rgba(26, 31, 46, 0.95) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
}

/* Message组件样式增强 */
.ant-message .ant-message-notice-content {
  background: rgba(26, 31, 46, 0.95) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  box-shadow: 0 8px 32px rgba(0, 212, 255, 0.2) !important;
  backdrop-filter: blur(20px) !important;
  color: #ffffff !important;
}

/* Checkbox样式增强 */
.ant-checkbox-checked .ant-checkbox-inner {
  background: linear-gradient(135deg, #00d4ff 0%, #8a2be2 100%) !important;
  border-color: #00d4ff !important;
}

.ant-checkbox:hover .ant-checkbox-inner {
  border-color: #00d4ff !important;
}

.ant-checkbox-wrapper {
  color: #ffffff !important;
}

/* Upload组件样式增强 */
.ant-upload.ant-upload-drag {
  background: rgba(26, 31, 46, 0.8) !important;
  border: 2px dashed rgba(0, 212, 255, 0.3) !important;
  border-radius: 12px !important;
}

.ant-upload.ant-upload-drag:hover {
  border-color: #00d4ff !important;
  background: rgba(0, 212, 255, 0.05) !important;
}

.ant-upload.ant-upload-drag .ant-upload-text {
  color: #ffffff !important;
}

.ant-upload.ant-upload-drag .ant-upload-hint {
  color: rgba(136, 146, 176, 0.8) !important;
}

/* 全局文本颜色覆盖 */
.ant-typography {
  color: #ffffff !important;
}

.ant-typography-title {
  color: #ffffff !important;
}

.ant-typography-paragraph {
  color: #ffffff !important;
}

/* 确保所有文本在深色背景下可见 */
.ant-space-item,
.ant-form-item-label,
.ant-form-item-control {
  color: #ffffff !important;
}

/* 加载状态样式 */
.ant-spin .ant-spin-dot-item {
  background: #00d4ff !important;
}

.ant-spin .ant-spin-text {
  color: #ffffff !important;
}
